"""
Loop Executor Package - Dynamic loop execution for orchestration engine.

This package provides comprehensive loop execution capabilities that integrate
with the main orchestration engine's transition handling and state management.

Key Components:
- LoopExecutor: Main orchestrator for loop execution
- LoopStateManager: State management and iteration tracking
- LoopBodyExecutor: Execution of loop body transition chains
- LoopParameterResolver: Dynamic parameter resolution from various sources

Features:
- Dynamic iteration sources (lists, ranges, etc.)
- Sequential and parallel execution modes
- Comprehensive error handling and retry logic
- Dual output system (current_item and final_results)
- Integration with main workflow state management
- Exit transition detection for loop completion
"""

from .loop_executor import LoopExecutor
from .loop_state_manager import LoopStateManager
from .loop_body_executor import LoopBodyExecutor
from .loop_parameter_resolver import LoopParameterResolver
from .loop_aggregator import LoopAggregator

__all__ = [
    "LoopExecutor",
    "LoopStateManager",
    "LoopBodyExecutor",
    "LoopParameterResolver",
    "LoopAggregator"
]

# Version information
__version__ = "1.0.0"
__author__ = "Orchestration Engine Team"
__description__ = "Dynamic loop execution system for workflow orchestration"
