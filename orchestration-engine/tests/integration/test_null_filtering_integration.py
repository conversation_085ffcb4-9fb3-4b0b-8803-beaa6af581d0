import pytest
from unittest.mock import Mock, AsyncMock, patch
from app.core_.transition_handler import TransitionHandler
from app.core_.workflow_utils import WorkflowUtils
from app.core_.state_manager import WorkflowStateManager
from app.services.kafka_tool_executor import KafkaToolExecutor
from app.services.agent_executor import Agent<PERSON>xecutor
from app.services.node_executor import NodeExecutor


class TestNullFilteringIntegration:
    """
    Integration tests to verify null value filtering works correctly across
    all parameter processing and tool execution integration points.
    """

    @pytest.fixture
    def mock_state_manager(self):
        """Mock state manager for testing."""
        state_manager = Mock(spec=WorkflowStateManager)
        state_manager.get_transition_result = Mock(return_value={})
        state_manager.mark_transition_completed = Mock()
        state_manager.mark_transition_failed = Mock()
        return state_manager

    @pytest.fixture
    def mock_tool_executor(self):
        """Mock MCP tool executor for testing."""
        executor = Mock(spec=KafkaToolExecutor)
        executor.execute_tool = AsyncMock(return_value={"status": "success", "result": "test_result"})
        return executor

    @pytest.fixture
    def mock_agent_executor(self):
        """Mock agent executor for testing."""
        executor = Mock(spec=AgentExecutor)
        executor.execute_tool = AsyncMock(return_value={"status": "success", "result": "agent_result"})
        return executor

    @pytest.fixture
    def mock_node_executor(self):
        """Mock node executor for testing."""
        executor = Mock(spec=NodeExecutor)
        executor.execute_tool = AsyncMock(return_value={"status": "success", "result": "node_result"})
        return executor

    @pytest.fixture
    def transition_handler(self, mock_state_manager, mock_tool_executor, mock_agent_executor, mock_node_executor):
        """Create transition handler with mocked dependencies."""
        with patch("app.core_.transition_handler.load_schema") as mock_load_schema:
            mock_load_schema.return_value = {"type": "object", "properties": {}}
            
            handler = TransitionHandler(
                workflow_id="test-workflow",
                state_manager=mock_state_manager,
                tool_executor=mock_tool_executor,
                agent_executor=mock_agent_executor,
                node_executor=mock_node_executor,
            )
            
            # Mock nodes for testing
            handler.nodes = {
                "test_node": {
                    "id": "test_node",
                    "server_script_path": "test/path",
                    "server_tools": [
                        {
                            "tool_name": "test_tool",
                            "input_schema": {
                                "predefined_fields": [
                                    {"field_name": "required_field", "required": True, "data_type": {"type": "string"}},
                                    {"field_name": "optional_field", "required": False, "data_type": {"type": "string"}},
                                ]
                            },
                            "output_schema": {"type": "object"}
                        }
                    ]
                }
            }
            
            return handler

    @pytest.mark.asyncio
    async def test_mcp_executor_integration_with_null_filtering(self, transition_handler, mock_tool_executor):
        """
        Test that null values are filtered out when sending parameters to MCP executor.
        """
        # Arrange
        transition = {
            "id": "test_transition",
            "transition_type": "standard",
            "execution_type": "MCP",
            "node_info": {
                "node_id": "test_node",
                "tools_to_use": [{"tool_name": "test_tool", "tool_id": "test_id"}],
            },
            "tool_params_config": {
                "required_field": "valid_value",
                "optional_field": None,  # Should be filtered out
                "empty_field": "",       # Should be filtered out
                "null_string": "null",   # Should be filtered out
                "valid_number": 42,      # Should be kept
            }
        }

        # Act
        await transition_handler._execute_standard_or_reflection_transition(transition)

        # Assert
        mock_tool_executor.execute_tool.assert_called_once()
        call_args = mock_tool_executor.execute_tool.call_args
        
        # Verify that null/empty values were filtered out
        tool_parameters = call_args.kwargs["tool_parameters"]
        expected_params = {
            "required_field": "valid_value",
            "valid_number": 42,
            # optional_field, empty_field, null_string should be filtered out
        }
        assert tool_parameters == expected_params

    @pytest.mark.asyncio
    async def test_agent_executor_integration_with_null_filtering(self, transition_handler, mock_agent_executor):
        """
        Test that null values are filtered out when sending parameters to Agent executor.
        """
        # Arrange
        transition = {
            "id": "test_transition",
            "transition_type": "standard",
            "execution_type": "Agent",
            "node_info": {
                "node_id": "test_node",
                "tools_to_use": [{"tool_name": "test_tool", "tool_id": "test_id"}],
            },
            "tool_params_config": {
                "agent_type": "component",
                "execution_type": "response",
                "query": "test query",
                "null_config": None,     # Should be filtered out
                "empty_dict": {},        # Should be filtered out
                "valid_config": {"key": "value"},  # Should be kept
            }
        }

        # Act
        await transition_handler._execute_standard_or_reflection_transition(transition)

        # Assert
        mock_agent_executor.execute_tool.assert_called_once()
        call_args = mock_agent_executor.execute_tool.call_args
        
        # Verify that null/empty values were filtered out
        tool_parameters = call_args.kwargs["tool_parameters"]
        expected_params = {
            "agent_type": "component",
            "execution_type": "response",
            "query": "test query",
            "valid_config": {"key": "value"},
            # null_config and empty_dict should be filtered out
        }
        assert tool_parameters == expected_params

    @pytest.mark.asyncio
    async def test_node_executor_integration_with_null_filtering(self, transition_handler, mock_node_executor):
        """
        Test that null values are filtered out when sending parameters to Node executor.
        """
        # Arrange
        transition = {
            "id": "test_transition",
            "transition_type": "standard",
            "execution_type": "API request",
            "node_info": {
                "node_id": "test_node",
                "tools_to_use": [{"tool_name": "test_tool", "tool_id": "test_id"}],
            },
            "tool_params_config": {
                "url": "https://api.example.com",
                "method": "POST",
                "headers": None,         # Should be filtered out
                "body": "",              # Should be filtered out
                "timeout": 30,           # Should be kept
                "empty_list": [],        # Should be filtered out
            }
        }

        # Act
        await transition_handler._execute_standard_or_reflection_transition(transition)

        # Assert
        mock_node_executor.execute_tool.assert_called_once()
        call_args = mock_node_executor.execute_tool.call_args
        
        # Verify that null/empty values were filtered out
        tool_parameters = call_args[0][2]  # Third positional argument
        expected_params = {
            "url": "https://api.example.com",
            "method": "POST",
            "timeout": 30,
            # headers, body, empty_list should be filtered out
        }
        assert tool_parameters == expected_params

    @pytest.mark.asyncio
    async def test_handle_mapping_integration_with_null_filtering(self, transition_handler, mock_tool_executor):
        """
        Test that null values from handle mappings are filtered out.
        """
        # Arrange
        # Mock previous transition result with null values
        transition_handler.state_manager.get_transition_result = Mock(
            return_value={
                "result": {
                    "valid_data": "mapped_value",
                    "null_data": None,
                    "empty_data": "",
                }
            }
        )

        transition = {
            "id": "test_transition",
            "transition_type": "standard",
            "execution_type": "MCP",
            "node_info": {
                "node_id": "test_node",
                "tools_to_use": [{"tool_name": "test_tool", "tool_id": "test_id"}],
            },
            "input_data_configs": [
                {
                    "from_transition_id": "prev_transition",
                    "handle_mappings": [
                        {"source_handle_id": "valid_data", "target_handle_id": "mapped_field", "edge_id": "edge1"},
                        {"source_handle_id": "null_data", "target_handle_id": "null_field", "edge_id": "edge2"},
                        {"source_handle_id": "empty_data", "target_handle_id": "empty_field", "edge_id": "edge3"},
                    ]
                }
            ],
            "tool_params_config": {
                "static_field": "static_value",
                "null_static": None,  # Should be filtered out
            }
        }

        # Act
        await transition_handler._execute_standard_or_reflection_transition(transition)

        # Assert
        mock_tool_executor.execute_tool.assert_called_once()
        call_args = mock_tool_executor.execute_tool.call_args
        
        # Verify that null/empty values were filtered out
        tool_parameters = call_args.kwargs["tool_parameters"]
        expected_params = {
            "static_field": "static_value",
            "mapped_field": "mapped_value",
            # null_static, null_field, empty_field should be filtered out
        }
        assert tool_parameters == expected_params

    @pytest.mark.asyncio
    async def test_schema_formatting_integration_with_null_filtering(self, transition_handler, mock_tool_executor):
        """
        Test that schema-based parameter formatting filters out null values correctly.
        """
        # Arrange
        transition = {
            "id": "test_transition",
            "transition_type": "standard",
            "execution_type": "MCP",
            "node_info": {
                "node_id": "test_node",
                "tools_to_use": [{"tool_name": "test_tool", "tool_id": "test_id"}],
            },
            "tool_params_config": [
                {"field_name": "required_field", "field_value": "valid_value"},
                {"field_name": "optional_field", "field_value": None},  # Should be filtered out
                {"field_name": "empty_field", "field_value": ""},       # Should be filtered out
                {"field_name": "number_field", "field_value": 42},      # Should be kept
            ]
        }

        # Act
        await transition_handler._execute_standard_or_reflection_transition(transition)

        # Assert
        mock_tool_executor.execute_tool.assert_called_once()
        call_args = mock_tool_executor.execute_tool.call_args
        
        # Verify that null/empty values were filtered out
        tool_parameters = call_args.kwargs["tool_parameters"]
        expected_params = {
            "required_field": "valid_value",
            "number_field": 42,
            # optional_field and empty_field should be filtered out
        }
        assert tool_parameters == expected_params

    @pytest.mark.asyncio
    async def test_nested_parameter_filtering_integration(self, transition_handler, mock_tool_executor):
        """
        Test that nested null values are filtered out correctly in integration.
        """
        # Arrange
        transition = {
            "id": "test_transition",
            "transition_type": "standard",
            "execution_type": "MCP",
            "node_info": {
                "node_id": "test_node",
                "tools_to_use": [{"tool_name": "test_tool", "tool_id": "test_id"}],
            },
            "tool_params_config": {
                "nested_object": {
                    "valid_field": "valid_value",
                    "null_field": None,
                    "empty_field": "",
                    "deeply_nested": {
                        "deep_valid": "deep_value",
                        "deep_null": None,
                    }
                },
                "empty_nested": {
                    "all_null": None,
                    "all_empty": "",
                }
            }
        }

        # Act
        await transition_handler._execute_standard_or_reflection_transition(transition)

        # Assert
        mock_tool_executor.execute_tool.assert_called_once()
        call_args = mock_tool_executor.execute_tool.call_args
        
        # Verify that nested null/empty values were filtered out
        tool_parameters = call_args.kwargs["tool_parameters"]
        expected_params = {
            "nested_object": {
                "valid_field": "valid_value",
                "deeply_nested": {
                    "deep_valid": "deep_value",
                }
            }
            # empty_nested should be completely filtered out
        }
        assert tool_parameters == expected_params
