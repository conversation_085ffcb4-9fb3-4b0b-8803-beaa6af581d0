#!/usr/bin/env python3
"""
Test script to verify Redis timeout handling improvements.
This script tests the Redis connection timeout handling and recovery mechanisms.
"""

import sys
import os
import time
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.db_connections.redis_connections import RedisManager
from app.core_.state_manager import WorkflowStateManager
from app.utils.enhanced_logger import get_logger

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = get_logger("RedisTimeoutTest")

def test_redis_connection_handling():
    """Test Redis connection handling with timeout scenarios."""
    logger.info("Starting Redis timeout handling test")
    
    try:
        # Test 1: Basic connection test
        logger.info("Test 1: Basic Redis connection test")
        redis_manager = RedisManager(db_index=0)
        
        if redis_manager.is_connected():
            logger.info("✓ Redis connection successful")
        else:
            logger.warning("✗ Redis connection failed")
            return False
            
        # Test 2: Test set/get operations with timeout handling
        logger.info("Test 2: Testing set/get operations")
        test_key = "test:timeout_handling"
        test_value = "test_value_123"
        
        if redis_manager.set_value(test_key, test_value):
            logger.info("✓ Set operation successful")
        else:
            logger.warning("✗ Set operation failed")
            
        retrieved_value = redis_manager.get_value(test_key)
        if retrieved_value == test_value:
            logger.info("✓ Get operation successful")
        else:
            logger.warning(f"✗ Get operation failed. Expected: {test_value}, Got: {retrieved_value}")
            
        # Clean up test key
        redis_manager.delete_value(test_key)
        
        # Test 3: Test connection check resilience
        logger.info("Test 3: Testing connection check resilience")
        for i in range(5):
            is_connected = redis_manager.is_connected()
            logger.info(f"Connection check {i+1}: {'✓' if is_connected else '✗'}")
            time.sleep(0.5)
            
        logger.info("Redis timeout handling test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}", exc_info=True)
        return False

def test_state_manager_redis_handling():
    """Test StateManager Redis handling with timeout scenarios."""
    logger.info("Starting StateManager Redis handling test")
    
    try:
        # Create a test workflow state manager
        workflow_id = "test_workflow_timeout"
        state_manager = WorkflowStateManager(workflow_id)
        
        # Test transition result storage and retrieval
        test_transition_id = "test_transition_timeout"
        test_result = {"status": "completed", "data": "test_data"}
        
        logger.info("Testing transition result storage")
        state_manager.mark_transition_completed(test_transition_id, test_result)
        
        logger.info("Testing transition result retrieval")
        retrieved_result = state_manager.get_transition_result(test_transition_id)
        
        if retrieved_result:
            logger.info("✓ Transition result retrieval successful")
        else:
            logger.warning("✗ Transition result retrieval failed")
            
        # Test archiving (this would normally be triggered by Redis events)
        logger.info("Testing transition result archiving")
        archive_success = state_manager.archive_transition_result(test_transition_id)
        
        if archive_success:
            logger.info("✓ Transition result archiving successful")
        else:
            logger.info("ℹ Transition result archiving skipped (expected if no PostgreSQL)")
            
        logger.info("StateManager Redis handling test completed")
        return True
        
    except Exception as e:
        logger.error(f"StateManager test failed with error: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("Redis Timeout Handling Test Suite")
    logger.info("=" * 60)
    
    # Run tests
    test1_passed = test_redis_connection_handling()
    test2_passed = test_state_manager_redis_handling()
    
    # Summary
    logger.info("=" * 60)
    logger.info("Test Results Summary:")
    logger.info(f"Redis Connection Test: {'PASSED' if test1_passed else 'FAILED'}")
    logger.info(f"StateManager Test: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        logger.info("✓ All tests passed!")
        sys.exit(0)
    else:
        logger.error("✗ Some tests failed!")
        sys.exit(1)
