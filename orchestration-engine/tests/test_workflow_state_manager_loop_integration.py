import pytest
from app.core_.state_manager import WorkflowStateManager


class TestWorkflowStateManagerLoopIntegration:
    """
    Test suite for WorkflowStateManager loop state integration.
    Tests basic loop state storage, retrieval, and management operations.
    """

    @pytest.fixture
    def state_manager(self):
        """Create WorkflowStateManager for testing."""
        # Create a simple instance without database connections for testing
        manager = WorkflowStateManager(workflow_id="test-workflow-123")
        return manager

    def test_loop_state_storage_and_retrieval(self, state_manager):
        """Test storing and retrieving loop state data."""
        loop_id = "loop_test_123"
        transition_id = "transition_456"
        loop_state_data = {
            "loop_id": loop_id,
            "transition_id": transition_id,
            "current_iteration_index": 2,
            "total_iterations": 5,
            "iteration_status": {0: "completed", 1: "completed", 2: "running"},
            "loop_execution_state": "running",
            "iteration_results": {0: {"result": "test_0"}, 1: {"result": "test_1"}},
            "iteration_contexts": {0: {"item": "item_0"}, 1: {"item": "item_1"}},
            "loop_metadata": {"start_time": 1234567890}
        }

        # Store loop state
        state_manager.store_loop_state(loop_id, transition_id, loop_state_data)

        # Verify storage
        assert loop_id in state_manager.loop_states
        assert transition_id in state_manager.active_loops
        assert state_manager.active_loops[transition_id] == loop_id

        # Retrieve by loop_id
        retrieved_state = state_manager.get_loop_state(loop_id)
        assert retrieved_state == loop_state_data

        # Retrieve by transition_id
        retrieved_by_transition = state_manager.get_loop_state_by_transition(transition_id)
        assert retrieved_by_transition == loop_state_data

    def test_loop_state_removal(self, state_manager):
        """Test removing loop state data."""
        loop_id = "loop_test_456"
        transition_id = "transition_789"
        loop_state_data = {"test": "data"}

        # Store loop state
        state_manager.store_loop_state(loop_id, transition_id, loop_state_data)
        assert state_manager.get_loop_state(loop_id) is not None

        # Remove loop state
        removed = state_manager.remove_loop_state(loop_id, transition_id)
        assert removed is True

        # Verify removal
        assert state_manager.get_loop_state(loop_id) is None
        assert state_manager.get_loop_state_by_transition(transition_id) is None
        assert not state_manager.is_loop_active(transition_id)

    def test_active_loop_management(self, state_manager):
        """Test active loop tracking functionality."""
        loop_id = "loop_active_123"
        transition_id = "transition_active_456"
        loop_state_data = {"test": "active_data"}

        # Initially no active loop
        assert not state_manager.is_loop_active(transition_id)
        assert state_manager.get_active_loop_id(transition_id) is None

        # Store loop state
        state_manager.store_loop_state(loop_id, transition_id, loop_state_data)

        # Verify active loop tracking
        assert state_manager.is_loop_active(transition_id)
        assert state_manager.get_active_loop_id(transition_id) == loop_id

    def test_get_all_loop_states(self, state_manager):
        """Test retrieving all loop states."""
        loop_states = {
            "loop_1": {"data": "test_1"},
            "loop_2": {"data": "test_2"},
            "loop_3": {"data": "test_3"}
        }

        # Store multiple loop states
        for loop_id, data in loop_states.items():
            state_manager.store_loop_state(loop_id, f"transition_{loop_id}", data)

        # Retrieve all loop states
        all_states = state_manager.get_all_loop_states()
        assert len(all_states) == 3
        for loop_id, data in loop_states.items():
            assert all_states[loop_id] == data

    def test_loop_state_initialization(self, state_manager):
        """Test that loop state properties are properly initialized."""
        assert isinstance(state_manager.loop_states, dict)
        assert isinstance(state_manager.active_loops, dict)
        assert len(state_manager.loop_states) == 0
        assert len(state_manager.active_loops) == 0

    def test_loop_state_edge_cases(self, state_manager):
        """Test edge cases for loop state management."""
        # Test retrieving non-existent loop state
        assert state_manager.get_loop_state("nonexistent") is None
        assert state_manager.get_loop_state_by_transition("nonexistent") is None
        assert not state_manager.is_loop_active("nonexistent")
        assert state_manager.get_active_loop_id("nonexistent") is None

        # Test removing non-existent loop state
        removed = state_manager.remove_loop_state("nonexistent")
        assert removed is False

        # Test removing loop state without transition_id
        state_manager.store_loop_state("test_loop", "test_transition", {"test": "data"})
        removed = state_manager.remove_loop_state("test_loop")
        assert removed is True
        assert state_manager.get_loop_state("test_loop") is None
        # Active loop mapping should still exist since transition_id wasn't provided
        assert state_manager.is_loop_active("test_transition")
