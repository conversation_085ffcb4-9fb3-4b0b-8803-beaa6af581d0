"""
Test Loop Node Core Configuration according to PRD.

This module tests all six core loop configuration elements:
1. Iteration Behavior (independent/sequential)
2. Iteration Source (lists/number ranges/batch processing)
3. Exit Condition (all items/max iterations/timeout/success/failure)
4. Iteration Settings (parallel/concurrent/order/timeout)
5. Result Aggregation (collect all/successful/count/latest/first/combine)
6. Error Handling (continue/retry/exit/include errors)
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from app.services.loop_executor.loop_executor import LoopExecutor


class TestLoopNodeCoreConfiguration:
    """Test core loop configuration elements according to PRD."""

    @pytest.fixture
    def loop_executor(self):
        """Create a loop executor instance for testing."""
        mock_state_manager = Mock()
        mock_transition_handler = Mock()
        mock_workflow_utils = Mock()
        mock_result_callback = Mock()
        mock_transitions_by_id = {}
        mock_nodes = {}

        executor = LoopExecutor(
            state_manager=mock_state_manager,
            workflow_utils=mock_workflow_utils,
            result_callback=mock_result_callback,
            transitions_by_id=mock_transitions_by_id,
            nodes=mock_nodes,
            transition_handler=mock_transition_handler,
            user_id="test_user"
        )
        return executor

    # ========================================
    # 1. ITERATION BEHAVIOR TESTS
    # ========================================

    def test_iteration_behavior_independent(self, loop_executor):
        """Test independent iteration behavior configuration."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }
        
        parsed_config = loop_executor.parse_loop_config(config)
        assert parsed_config["iteration_behavior"] == "independent"
        
        # Independent iterations should allow parallel execution
        assert parsed_config.get("allows_parallel", True)

    def test_iteration_behavior_sequential(self, loop_executor):
        """Test sequential iteration behavior configuration."""
        config = {
            "iteration_behavior": "sequential",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }
        
        parsed_config = loop_executor.parse_loop_config(config)
        assert parsed_config["iteration_behavior"] == "sequential"
        
        # Sequential iterations should have concurrency disabled
        assert parsed_config["concurrency"]["enabled"] == False

    def test_iteration_behavior_invalid(self, loop_executor):
        """Test invalid iteration behavior raises error."""
        config = {
            "iteration_behavior": "invalid_behavior",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }
        
        with pytest.raises(ValueError, match="Invalid iteration_behavior"):
            loop_executor.parse_loop_config(config)

    # ========================================
    # 2. ITERATION SOURCE TESTS
    # ========================================

    def test_iteration_source_list_mixed_types(self, loop_executor):
        """Test iteration source with mixed-type list."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, "string", {"key": "value"}, True, 3.14]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }
        
        parsed_config = loop_executor.parse_loop_config(config)
        loop_executor.current_loop_config = parsed_config
        loop_executor.prepare_iteration_data_from_input()
        
        # Should handle mixed types correctly
        assert len(loop_executor.current_iteration_data) == 5
        assert loop_executor.current_iteration_data[0] == (0, 1)
        assert loop_executor.current_iteration_data[1] == (1, "string")
        assert loop_executor.current_iteration_data[2] == (2, {"key": "value"})
        assert loop_executor.current_iteration_data[3] == (3, True)
        assert loop_executor.current_iteration_data[4] == (4, 3.14)

    def test_iteration_source_number_range_basic(self, loop_executor):
        """Test basic number range iteration source."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "number_range": {
                    "start": 1,
                    "end": 5
                }
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }
        
        parsed_config = loop_executor.parse_loop_config(config)
        loop_executor.current_loop_config = parsed_config
        loop_executor.prepare_iteration_data_from_input()
        
        # Should generate range(1, 5) = [1, 2, 3, 4]
        assert len(loop_executor.current_iteration_data) == 4
        assert loop_executor.current_iteration_data[0] == (0, 1)
        assert loop_executor.current_iteration_data[1] == (1, 2)
        assert loop_executor.current_iteration_data[2] == (2, 3)
        assert loop_executor.current_iteration_data[3] == (3, 4)

    def test_iteration_source_number_range_with_step(self, loop_executor):
        """Test number range with custom step size."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "number_range": {
                    "start": 0,
                    "end": 10,
                    "step": 3
                }
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }
        
        parsed_config = loop_executor.parse_loop_config(config)
        loop_executor.current_loop_config = parsed_config
        loop_executor.prepare_iteration_data_from_input()
        
        # Should generate range(0, 10, 3) = [0, 3, 6, 9]
        assert len(loop_executor.current_iteration_data) == 4
        assert loop_executor.current_iteration_data[0] == (0, 0)
        assert loop_executor.current_iteration_data[1] == (1, 3)
        assert loop_executor.current_iteration_data[2] == (2, 6)
        assert loop_executor.current_iteration_data[3] == (3, 9)

    def test_iteration_source_batch_processing(self, loop_executor):
        """Test batch processing with configurable batch size."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
                "batch_size": 3
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        loop_executor.current_loop_config = parsed_config
        loop_executor.prepare_iteration_data_from_input()

        # Should create batches of size 3: [1,2,3], [4,5,6], [7,8,9], [10]
        assert len(loop_executor.current_iteration_data) == 4
        assert loop_executor.current_iteration_data[0] == (0, [1, 2, 3])
        assert loop_executor.current_iteration_data[1] == (1, [4, 5, 6])
        assert loop_executor.current_iteration_data[2] == (2, [7, 8, 9])
        assert loop_executor.current_iteration_data[3] == (3, [10])

    def test_iteration_source_number_range_with_batch(self, loop_executor):
        """Test number range with batch processing."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "number_range": {
                    "start": 1,
                    "end": 8
                },
                "batch_size": 2
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        loop_executor.current_loop_config = parsed_config
        loop_executor.prepare_iteration_data_from_input()

        # Should create batches from range(1, 8): [1,2], [3,4], [5,6], [7]
        assert len(loop_executor.current_iteration_data) == 4
        assert loop_executor.current_iteration_data[0] == (0, [1, 2])
        assert loop_executor.current_iteration_data[1] == (1, [3, 4])
        assert loop_executor.current_iteration_data[2] == (2, [5, 6])
        assert loop_executor.current_iteration_data[3] == (3, [7])

    # ========================================
    # 3. EXIT CONDITION TESTS
    # ========================================

    def test_exit_condition_all_items_processed(self, loop_executor):
        """Test all items processed exit condition."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        assert parsed_config["exit_condition"]["condition_type"] == "all_items_processed"

    def test_exit_condition_max_iterations(self, loop_executor):
        """Test max iterations exit condition."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3, 4, 5]
            },
            "exit_condition": {
                "condition_type": "max_iterations",
                "max_iterations": 3
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        assert parsed_config["exit_condition"]["condition_type"] == "max_iterations"
        assert parsed_config["exit_condition"]["max_iterations"] == 3

    def test_exit_condition_timeout(self, loop_executor):
        """Test timeout exit condition."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "timeout",
                "timeout_seconds": 60
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        assert parsed_config["exit_condition"]["condition_type"] == "timeout"
        assert parsed_config["exit_condition"]["timeout_seconds"] == 60

    def test_exit_condition_success_condition(self, loop_executor):
        """Test success condition exit condition."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3, 4, 5]
            },
            "exit_condition": {
                "condition_type": "success_condition",
                "required_successes": 2
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        assert parsed_config["exit_condition"]["condition_type"] == "success_condition"
        assert parsed_config["exit_condition"]["required_successes"] == 2

    def test_exit_condition_failure_threshold(self, loop_executor):
        """Test failure threshold exit condition."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3, 4, 5]
            },
            "exit_condition": {
                "condition_type": "failure_threshold",
                "max_consecutive_failures": 3
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        assert parsed_config["exit_condition"]["condition_type"] == "failure_threshold"
        assert parsed_config["exit_condition"]["max_consecutive_failures"] == 3

    # ========================================
    # 4. ITERATION SETTINGS TESTS
    # ========================================

    def test_iteration_settings_parallel_execution(self, loop_executor):
        """Test parallel execution iteration settings."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "iteration_settings": {
                "parallel_execution": True,
                "max_concurrent": 2,
                "preserve_order": True,
                "iteration_timeout": 30
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        settings = parsed_config["iteration_settings"]
        assert settings["parallel_execution"] is True
        assert settings["max_concurrent"] == 2
        assert settings["preserve_order"] is True
        assert settings["iteration_timeout"] == 30

    def test_iteration_settings_sequential_execution(self, loop_executor):
        """Test sequential execution iteration settings."""
        config = {
            "iteration_behavior": "sequential",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "iteration_settings": {
                "parallel_execution": False,
                "preserve_order": True,
                "iteration_timeout": 60
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        settings = parsed_config["iteration_settings"]
        assert settings["parallel_execution"] is False
        assert settings["preserve_order"] is True
        assert settings["iteration_timeout"] == 60

    def test_iteration_settings_defaults(self, loop_executor):
        """Test default iteration settings when not specified."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        settings = parsed_config.get("iteration_settings", {})

        # Should have sensible defaults
        assert settings.get("parallel_execution", False) is False
        assert settings.get("preserve_order", True) is True
        assert settings.get("iteration_timeout", 30) >= 30

    # ========================================
    # 5. RESULT AGGREGATION TESTS
    # ========================================

    def test_result_aggregation_collect_all(self, loop_executor):
        """Test collect all result aggregation."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "result_aggregation": {
                "aggregation_type": "collect_all",
                "include_metadata": True
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        aggregation = parsed_config["result_aggregation"]
        assert aggregation["aggregation_type"] == "collect_all"
        assert aggregation["include_metadata"] is True

    def test_result_aggregation_collect_successful(self, loop_executor):
        """Test collect successful result aggregation."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "result_aggregation": {
                "aggregation_type": "collect_successful",
                "include_metadata": False
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        aggregation = parsed_config["result_aggregation"]
        assert aggregation["aggregation_type"] == "collect_successful"
        assert aggregation["include_metadata"] is False

    def test_result_aggregation_count_only(self, loop_executor):
        """Test count only result aggregation."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "result_aggregation": {
                "aggregation_type": "count_only"
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        aggregation = parsed_config["result_aggregation"]
        assert aggregation["aggregation_type"] == "count_only"

    def test_result_aggregation_latest_only(self, loop_executor):
        """Test latest only result aggregation."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "result_aggregation": {
                "aggregation_type": "latest_only"
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        aggregation = parsed_config["result_aggregation"]
        assert aggregation["aggregation_type"] == "latest_only"

    def test_result_aggregation_first_success(self, loop_executor):
        """Test first success result aggregation."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "result_aggregation": {
                "aggregation_type": "first_success"
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        aggregation = parsed_config["result_aggregation"]
        assert aggregation["aggregation_type"] == "first_success"

    def test_result_aggregation_combine_text(self, loop_executor):
        """Test combine text result aggregation."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "result_aggregation": {
                "aggregation_type": "combine_text",
                "separator": "\n"
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        aggregation = parsed_config["result_aggregation"]
        assert aggregation["aggregation_type"] == "combine_text"
        assert aggregation["separator"] == "\n"

    # ========================================
    # 6. ERROR HANDLING TESTS
    # ========================================

    def test_error_handling_continue(self, loop_executor):
        """Test continue on error handling."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "error_handling": {
                "on_iteration_error": "continue",
                "include_errors": False
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        error_handling = parsed_config["error_handling"]
        assert error_handling["on_iteration_error"] == "continue"
        assert error_handling["include_errors"] is False

    def test_error_handling_retry_once(self, loop_executor):
        """Test retry once error handling."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "error_handling": {
                "on_iteration_error": "retry_once",
                "include_errors": True
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        error_handling = parsed_config["error_handling"]
        assert error_handling["on_iteration_error"] == "retry_once"
        assert error_handling["include_errors"] is True

    def test_error_handling_retry_twice(self, loop_executor):
        """Test retry twice error handling."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "error_handling": {
                "on_iteration_error": "retry_twice",
                "include_errors": True
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        error_handling = parsed_config["error_handling"]
        assert error_handling["on_iteration_error"] == "retry_twice"
        assert error_handling["include_errors"] is True

    def test_error_handling_exit_loop(self, loop_executor):
        """Test exit loop on error handling."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "error_handling": {
                "on_iteration_error": "exit_loop",
                "include_errors": True
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        error_handling = parsed_config["error_handling"]
        assert error_handling["on_iteration_error"] == "exit_loop"
        assert error_handling["include_errors"] is True

    def test_error_handling_defaults(self, loop_executor):
        """Test default error handling when not specified."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)
        error_handling = parsed_config.get("error_handling", {})

        # Should have sensible defaults
        assert error_handling.get("on_iteration_error", "continue") == "continue"
        assert error_handling.get("include_errors", False) is False

    # ========================================
    # COMPREHENSIVE CONFIGURATION TESTS
    # ========================================

    def test_full_configuration_example_from_prd(self, loop_executor):
        """Test the full configuration example from the PRD."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3, 4, 5],
                "batch_size": 2
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "iteration_settings": {
                "parallel_execution": False,
                "preserve_order": True,
                "iteration_timeout": 30
            },
            "result_aggregation": {
                "aggregation_type": "collect_all",
                "include_metadata": True
            },
            "error_handling": {
                "on_iteration_error": "continue",
                "include_errors": False
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)

        # Verify all sections are parsed correctly
        assert parsed_config["iteration_behavior"] == "independent"
        assert parsed_config["iteration_source"]["original"]["iteration_list"] == [1, 2, 3, 4, 5]
        assert parsed_config["iteration_source"]["batch_size"] == 2
        assert parsed_config["exit_condition"]["condition_type"] == "all_items_processed"
        assert parsed_config["iteration_settings"]["parallel_execution"] is False
        assert parsed_config["iteration_settings"]["preserve_order"] is True
        assert parsed_config["iteration_settings"]["iteration_timeout"] == 30
        assert parsed_config["result_aggregation"]["aggregation_type"] == "collect_all"
        assert parsed_config["result_aggregation"]["include_metadata"] is True
        assert parsed_config["error_handling"]["on_iteration_error"] == "continue"
        assert parsed_config["error_handling"]["include_errors"] is False

    def test_complex_configuration_with_number_range(self, loop_executor):
        """Test complex configuration with number range from PRD."""
        config = {
            "iteration_behavior": "sequential",
            "iteration_source": {
                "number_range": {
                    "start": 1,
                    "end": 100,
                    "step": 5
                }
            },
            "exit_condition": {
                "condition_type": "max_iterations",
                "max_iterations": 50
            },
            "iteration_settings": {
                "parallel_execution": False,
                "preserve_order": True,
                "iteration_timeout": 60
            },
            "result_aggregation": {
                "aggregation_type": "collect_successful",
                "include_metadata": True
            },
            "error_handling": {
                "on_iteration_error": "retry_once",
                "include_errors": True
            }
        }

        parsed_config = loop_executor.parse_loop_config(config)

        # Verify complex configuration is parsed correctly
        assert parsed_config["iteration_behavior"] == "sequential"
        # Check the original format preserved for tests
        assert parsed_config["iteration_source"]["original"]["number_range"]["start"] == 1
        assert parsed_config["iteration_source"]["original"]["number_range"]["end"] == 100
        assert parsed_config["iteration_source"]["original"]["number_range"]["step"] == 5
        assert parsed_config["exit_condition"]["condition_type"] == "max_iterations"
        assert parsed_config["exit_condition"]["max_iterations"] == 50
        assert parsed_config["result_aggregation"]["aggregation_type"] == "collect_successful"
        assert parsed_config["error_handling"]["on_iteration_error"] == "retry_once"
