"""
Performance Tests for Loop Executor.

This module tests performance characteristics including sequential vs concurrent
execution, memory usage, scalability, and performance regression detection.
"""

import pytest
import asyncio
import time
import os
from unittest.mock import Mock, AsyncMock
from app.services.loop_executor.loop_executor import LoopExecutor

try:
    import psutil

    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


class TestLoopExecutorPerformance:
    """Test performance characteristics of loop execution."""

    @pytest.fixture
    def loop_executor(self):
        """Create a LoopExecutor instance for testing."""
        state_manager = Mock()
        state_manager.workflow_id = "perf_test_workflow"
        state_manager.store_loop_state = AsyncMock()
        state_manager.save_workflow_state = AsyncMock()

        workflow_utils = Mock()
        result_callback = Mock()
        transitions_by_id = {"perf_transition": {"id": "perf_transition"}}
        nodes = {"perf_node": {"id": "perf_node"}}
        transition_handler = Mock()

        return LoopExecutor(
            state_manager=state_manager,
            workflow_utils=workflow_utils,
            result_callback=result_callback,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            transition_handler=transition_handler,
        )

    def get_memory_usage(self):
        """Get current memory usage in MB."""
        if not PSUTIL_AVAILABLE:
            return 0.0  # Return 0 if psutil not available
        process = psutil.Process(os.getpid())
        return process.memory_info().rss / 1024 / 1024

    @pytest.mark.asyncio
    async def test_sequential_vs_concurrent_execution_performance(self, loop_executor):
        """Test performance comparison between sequential and concurrent execution."""
        # Test data
        iteration_count = 50
        work_delay = 0.01  # 10ms per iteration

        test_data = list(range(iteration_count))

        # Mock iteration with controlled delay
        async def mock_work_iteration(index, item):
            await asyncio.sleep(work_delay)
            return {"iteration_index": index, "value": item * 2, "status": "completed"}

        loop_executor.execute_single_iteration = mock_work_iteration

        # Test sequential execution
        sequential_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {"type": "list", "data": test_data},
            "loop_body_transitions": ["perf_transition"],
            "concurrency": {"enabled": False},
        }

        start_time = time.time()
        sequential_result = await loop_executor.execute_tool(
            loop_config=sequential_config, transition_id="sequential_perf"
        )
        sequential_time = time.time() - start_time

        # Reset executor state
        loop_executor.reset_loop_state()

        # Test concurrent execution
        concurrent_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {"type": "list", "data": test_data},
            "loop_body_transitions": ["perf_transition"],
            "concurrency": {
                "enabled": True,
                "max_concurrent": 10,
                "preserve_order": True,
            },
        }

        start_time = time.time()
        concurrent_result = await loop_executor.execute_tool(
            loop_config=concurrent_config, transition_id="concurrent_perf"
        )
        concurrent_time = time.time() - start_time

        # Verify results are equivalent
        assert len(sequential_result) == len(concurrent_result) == iteration_count

        # Concurrent should be significantly faster
        speedup_ratio = sequential_time / concurrent_time
        assert speedup_ratio > 2.0, f"Expected speedup > 2x, got {speedup_ratio:.2f}x"

        print(f"Sequential time: {sequential_time:.2f}s")
        print(f"Concurrent time: {concurrent_time:.2f}s")
        print(f"Speedup: {speedup_ratio:.2f}x")

    @pytest.mark.asyncio
    @pytest.mark.skipif(not PSUTIL_AVAILABLE, reason="psutil not available")
    async def test_memory_usage_with_large_iteration_sets(self, loop_executor):
        """Test memory usage patterns with large iteration sets."""
        # Test different sizes
        test_sizes = [100, 500, 1000, 2000]
        memory_usage_results = []

        for size in test_sizes:
            # Reset state
            loop_executor.reset_loop_state()

            # Measure initial memory
            initial_memory = self.get_memory_usage()

            # Create large dataset
            large_data = list(range(size))

            loop_config = {
                "loop_type": "context_independent",
                "aggregation_config": {"type": "count"},
                "iteration_source": {"type": "list", "data": large_data},
                "loop_body_transitions": ["perf_transition"],
                "performance": {"memory_efficient": True, "batch_size": 50},
            }

            # Mock lightweight iteration
            async def mock_memory_iteration(index, item):
                return {"count": 1}

            loop_executor.execute_single_iteration = mock_memory_iteration

            # Execute and measure peak memory
            peak_memory = initial_memory

            async def memory_monitor():
                nonlocal peak_memory
                while True:
                    current_memory = self.get_memory_usage()
                    peak_memory = max(peak_memory, current_memory)
                    await asyncio.sleep(0.01)

            # Start memory monitoring
            monitor_task = asyncio.create_task(memory_monitor())

            try:
                result = await loop_executor.execute_tool(
                    loop_config=loop_config, transition_id=f"memory_test_{size}"
                )

                # Verify result
                assert result == size

            finally:
                monitor_task.cancel()
                try:
                    await monitor_task
                except asyncio.CancelledError:
                    pass

            # Calculate memory usage
            memory_delta = peak_memory - initial_memory
            memory_usage_results.append(
                {
                    "size": size,
                    "initial_memory": initial_memory,
                    "peak_memory": peak_memory,
                    "memory_delta": memory_delta,
                    "memory_per_item": memory_delta / size if size > 0 else 0,
                }
            )

        # Verify memory usage is reasonable and scales linearly
        for i, result in enumerate(memory_usage_results):
            print(
                f"Size {result['size']}: {result['memory_delta']:.2f}MB delta, "
                f"{result['memory_per_item']:.4f}MB per item"
            )

            # Memory delta should be reasonable (less than 100MB for these tests)
            assert (
                result["memory_delta"] < 100
            ), f"Memory usage too high: {result['memory_delta']:.2f}MB"

    @pytest.mark.asyncio
    async def test_scalability_with_high_concurrency(self, loop_executor):
        """Test scalability with increasing concurrency levels."""
        iteration_count = 100
        work_delay = 0.005  # 5ms per iteration
        concurrency_levels = [1, 5, 10, 20, 50]

        test_data = list(range(iteration_count))
        performance_results = []

        # Mock iteration with controlled delay
        async def mock_scalability_iteration(index, item):
            await asyncio.sleep(work_delay)
            return {"iteration_index": index, "value": item, "status": "completed"}

        loop_executor.execute_single_iteration = mock_scalability_iteration

        for concurrency in concurrency_levels:
            # Reset state
            loop_executor.reset_loop_state()

            loop_config = {
                "loop_type": "context_independent",
                "aggregation_config": {"type": "list"},
                "iteration_source": {"type": "list", "data": test_data},
                "loop_body_transitions": ["perf_transition"],
                "concurrency": {
                    "enabled": True,
                    "max_concurrent": concurrency,
                    "preserve_order": True,
                },
            }

            start_time = time.time()
            result = await loop_executor.execute_tool(
                loop_config=loop_config, transition_id=f"scalability_test_{concurrency}"
            )
            execution_time = time.time() - start_time

            # Verify result
            assert len(result) == iteration_count

            # Calculate throughput
            throughput = iteration_count / execution_time

            performance_results.append(
                {
                    "concurrency": concurrency,
                    "execution_time": execution_time,
                    "throughput": throughput,
                }
            )

        # Verify scalability characteristics
        for i, result in enumerate(performance_results):
            print(
                f"Concurrency {result['concurrency']}: {result['execution_time']:.2f}s, "
                f"{result['throughput']:.1f} iter/sec"
            )

            # Higher concurrency should generally improve throughput
            if i > 0:
                prev_result = performance_results[i - 1]
                # Allow some variance, but expect general improvement
                throughput_improvement = (
                    result["throughput"] / prev_result["throughput"]
                )
                # Don't enforce strict improvement due to overhead, but check reasonableness
                assert (
                    throughput_improvement > 0.5
                ), f"Throughput degraded significantly at concurrency {result['concurrency']}"

    @pytest.mark.asyncio
    async def test_performance_regression_detection(self, loop_executor):
        """Test for performance regression detection."""
        # Baseline performance test
        baseline_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "range",
                "data": {"start": 0, "stop": 100, "step": 1},
            },
            "loop_body_transitions": ["perf_transition"],
            "concurrency": {"enabled": True, "max_concurrent": 10},
        }

        # Mock baseline iteration
        async def mock_baseline_iteration(index, item):
            await asyncio.sleep(0.001)  # 1ms baseline work
            return {"iteration_index": index, "value": item * 2}

        loop_executor.execute_single_iteration = mock_baseline_iteration

        # Run multiple times to get average
        execution_times = []
        for run in range(3):
            loop_executor.reset_loop_state()

            start_time = time.time()
            result = await loop_executor.execute_tool(
                loop_config=baseline_config, transition_id=f"regression_test_{run}"
            )
            execution_time = time.time() - start_time
            execution_times.append(execution_time)

            assert len(result) == 100

        # Calculate baseline metrics
        avg_execution_time = sum(execution_times) / len(execution_times)
        baseline_throughput = 100 / avg_execution_time

        # Performance thresholds (adjust based on expected performance)
        expected_min_throughput = 50  # iterations per second
        expected_max_time = 5.0  # seconds

        assert (
            baseline_throughput >= expected_min_throughput
        ), f"Throughput too low: {baseline_throughput:.1f} < {expected_min_throughput}"

        assert (
            avg_execution_time <= expected_max_time
        ), f"Execution time too high: {avg_execution_time:.2f}s > {expected_max_time}s"

        print(
            f"Baseline performance: {avg_execution_time:.2f}s avg, {baseline_throughput:.1f} iter/sec"
        )

    @pytest.mark.asyncio
    async def test_load_testing_for_loop_heavy_workflows(self, loop_executor):
        """Test load handling for loop-heavy workflows."""
        # Simulate multiple concurrent loops
        num_concurrent_loops = 5
        iterations_per_loop = 50

        async def run_single_loop(loop_id):
            """Run a single loop instance."""
            loop_config = {
                "loop_type": "context_independent",
                "aggregation_config": {"type": "count"},
                "iteration_source": {
                    "type": "range",
                    "data": {"start": 0, "stop": iterations_per_loop, "step": 1},
                },
                "loop_body_transitions": ["perf_transition"],
                "concurrency": {"enabled": True, "max_concurrent": 5},
            }

            # Mock work iteration
            async def mock_load_iteration(index, item):
                await asyncio.sleep(0.002)  # 2ms work
                return {"count": 1}

            # Create new executor instance for each loop to avoid state conflicts
            state_manager = Mock()
            state_manager.workflow_id = f"load_test_workflow_{loop_id}"
            state_manager.store_loop_state = AsyncMock()
            state_manager.save_workflow_state = AsyncMock()

            test_executor = LoopExecutor(
                state_manager=state_manager,
                workflow_utils=Mock(),
                result_callback=Mock(),
                transitions_by_id={"perf_transition": {"id": "perf_transition"}},
                nodes={"perf_node": {"id": "perf_node"}},
                transition_handler=Mock(),
            )
            test_executor.execute_single_iteration = mock_load_iteration

            result = await test_executor.execute_tool(
                loop_config=loop_config, transition_id=f"load_test_loop_{loop_id}"
            )

            return {"loop_id": loop_id, "result": result}

        # Run multiple loops concurrently
        start_time = time.time()

        tasks = [run_single_loop(i) for i in range(num_concurrent_loops)]
        results = await asyncio.gather(*tasks)

        total_time = time.time() - start_time

        # Verify all loops completed successfully
        assert len(results) == num_concurrent_loops
        for result in results:
            assert result["result"] == iterations_per_loop

        # Calculate load test metrics
        total_iterations = num_concurrent_loops * iterations_per_loop
        overall_throughput = total_iterations / total_time

        print(
            f"Load test: {num_concurrent_loops} loops, {total_iterations} total iterations"
        )
        print(
            f"Total time: {total_time:.2f}s, Overall throughput: {overall_throughput:.1f} iter/sec"
        )

        # Verify reasonable performance under load
        expected_min_load_throughput = 100  # iterations per second under load
        assert (
            overall_throughput >= expected_min_load_throughput
        ), f"Load throughput too low: {overall_throughput:.1f} < {expected_min_load_throughput}"

    @pytest.mark.asyncio
    @pytest.mark.skipif(not PSUTIL_AVAILABLE, reason="psutil not available")
    async def test_memory_efficiency_with_streaming(self, loop_executor):
        """Test memory efficiency with streaming iteration."""
        # Large dataset that would consume significant memory if loaded all at once
        large_size = 5000

        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "count"},
            "iteration_source": {
                "type": "range",
                "data": {"start": 0, "stop": large_size, "step": 1},
            },
            "loop_body_transitions": ["perf_transition"],
            "performance": {
                "memory_efficient": True,
                "batch_size": 100,
                "gc_between_batches": True,
            },
        }

        # Monitor memory during execution
        initial_memory = self.get_memory_usage()
        peak_memory = initial_memory

        async def mock_streaming_iteration(index, item):
            nonlocal peak_memory
            current_memory = self.get_memory_usage()
            peak_memory = max(peak_memory, current_memory)
            return {"count": 1}

        loop_executor.execute_single_iteration = mock_streaming_iteration

        result = await loop_executor.execute_tool(
            loop_config=loop_config, transition_id="streaming_memory_test"
        )

        final_memory = self.get_memory_usage()
        memory_delta = peak_memory - initial_memory

        # Verify execution completed
        assert result == large_size

        # Memory usage should be reasonable despite large dataset
        memory_per_item = memory_delta / large_size

        print(f"Streaming test: {large_size} items, {memory_delta:.2f}MB peak delta")
        print(f"Memory per item: {memory_per_item:.6f}MB")

        # Memory per item should be very low with streaming
        assert (
            memory_per_item < 0.01
        ), f"Memory per item too high: {memory_per_item:.6f}MB"
