"""
Tests for Phase 5 Advanced Features of Loop Executor.

This module tests conditional termination, custom aggregation,
performance optimizations, and enhanced error handling.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from app.services.loop_executor.loop_executor import LoopExecutor
from app.services.loop_executor.loop_error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON>ate<PERSON><PERSON>, ErrorSeverity, RecoveryStrategy


class TestConditionalTermination:
    """Test conditional loop termination features."""

    @pytest.fixture
    def loop_executor(self):
        """Create a LoopExecutor instance for testing."""
        state_manager = Mock()
        workflow_utils = Mock()
        result_callback = Mock()
        transitions_by_id = {"test_transition": {"id": "test_transition"}}
        nodes = {"test_node": {"id": "test_node"}}
        transition_handler = Mock()
        
        return LoopExecutor(
            state_manager=state_manager,
            workflow_utils=workflow_utils,
            result_callback=result_callback,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            transition_handler=transition_handler,
        )

    @pytest.mark.asyncio
    async def test_max_iterations_termination(self, loop_executor):
        """Test termination based on maximum iterations."""
        # Setup termination conditions
        termination_config = {
            "max_iterations": 3
        }
        
        # Test evaluation
        result = await loop_executor._evaluate_termination_conditions(2, {"value": 10})
        assert result["should_terminate"] is True
        assert result["reason"] == "max_iterations_reached"

    @pytest.mark.asyncio
    async def test_success_condition_termination(self, loop_executor):
        """Test termination based on success condition."""
        loop_executor.current_loop_config = {
            "termination_conditions": {
                "success_condition": "result['value'] > 100"
            }
        }
        
        # Test with condition met
        result = await loop_executor._evaluate_termination_conditions(1, {"value": 150})
        assert result["should_terminate"] is True
        assert result["reason"] == "success_condition_met"
        
        # Test with condition not met
        result = await loop_executor._evaluate_termination_conditions(1, {"value": 50})
        assert result["should_terminate"] is False

    @pytest.mark.asyncio
    async def test_timeout_termination(self, loop_executor):
        """Test termination based on timeout."""
        import time
        
        loop_executor.current_loop_config = {
            "termination_conditions": {
                "timeout_seconds": 1
            }
        }
        loop_executor.loop_context = {"start_time": time.time() - 2}  # 2 seconds ago
        
        result = await loop_executor._evaluate_termination_conditions(1, {"value": 10})
        assert result["should_terminate"] is True
        assert result["reason"] == "timeout_reached"

    @pytest.mark.asyncio
    async def test_condition_safety_validation(self, loop_executor):
        """Test that dangerous conditions are rejected."""
        # Test dangerous pattern rejection
        assert loop_executor._evaluate_condition("import os", {}, 0) is False
        assert loop_executor._evaluate_condition("exec('malicious')", {}, 0) is False
        assert loop_executor._evaluate_condition("__import__('os')", {}, 0) is False
        
        # Test safe condition
        assert loop_executor._evaluate_condition("result == 10", 10, 0) is True


class TestCustomAggregation:
    """Test custom aggregation functions."""

    @pytest.fixture
    def loop_aggregator(self):
        """Create a LoopAggregator instance for testing."""
        from app.services.loop_executor.loop_aggregator import LoopAggregator
        return LoopAggregator()

    def test_builtin_average_function(self, loop_aggregator):
        """Test builtin average aggregation function."""
        config = {
            "type": "custom",
            "custom_function": {
                "type": "builtin",
                "name": "average"
            }
        }
        
        results = [1, 2, 3, 4, 5]
        result = loop_aggregator._aggregate_custom(results, config)
        
        assert result["aggregated_result"] == 3.0
        assert result["metadata"]["function_type"] == "builtin"

    def test_lambda_aggregation_function(self, loop_aggregator):
        """Test lambda-style aggregation function."""
        config = {
            "type": "custom",
            "custom_function": {
                "type": "lambda",
                "expression": "sum(results)"
            }
        }
        
        results = [1, 2, 3, 4, 5]
        result = loop_aggregator._aggregate_custom(results, config)
        
        assert result["aggregated_result"] == 15

    def test_code_aggregation_function(self, loop_aggregator):
        """Test code-based aggregation function."""
        config = {
            "type": "custom",
            "custom_function": {
                "type": "code",
                "code": "result = max(results) - min(results)"
            }
        }
        
        results = [1, 2, 3, 4, 5]
        result = loop_aggregator._aggregate_custom(results, config)
        
        assert result["aggregated_result"] == 4  # 5 - 1

    def test_custom_function_safety_validation(self, loop_aggregator):
        """Test that dangerous custom functions are rejected."""
        config = {
            "type": "custom",
            "custom_function": {
                "type": "code",
                "code": "import os; result = os.listdir('/')"
            }
        }
        
        results = [1, 2, 3]
        with pytest.raises(ValueError, match="Dangerous pattern detected"):
            loop_aggregator._aggregate_custom(results, config)


class TestPerformanceOptimizations:
    """Test performance optimization features."""

    @pytest.fixture
    def loop_executor(self):
        """Create a LoopExecutor instance for testing."""
        state_manager = Mock()
        workflow_utils = Mock()
        result_callback = Mock()
        transitions_by_id = {"test_transition": {"id": "test_transition"}}
        nodes = {"test_node": {"id": "test_node"}}
        transition_handler = Mock()
        
        return LoopExecutor(
            state_manager=state_manager,
            workflow_utils=workflow_utils,
            result_callback=result_callback,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            transition_handler=transition_handler,
        )

    @pytest.mark.asyncio
    async def test_memory_efficient_iteration_detection(self, loop_executor):
        """Test memory-efficient iteration detection."""
        # Test with small dataset
        loop_executor.current_loop_config = {"performance": {"memory_threshold": 100}}
        loop_executor.current_iteration_data = [(i, i) for i in range(50)]
        
        result = await loop_executor._enable_memory_efficient_iteration()
        assert result is False
        
        # Test with large dataset
        loop_executor.current_iteration_data = [(i, i) for i in range(150)]
        result = await loop_executor._enable_memory_efficient_iteration()
        assert result is True

    @pytest.mark.asyncio
    async def test_performance_profiling(self, loop_executor):
        """Test performance profiling functionality."""
        loop_executor.current_loop_config = {
            "performance": {"enable_profiling": True}
        }
        
        # Start profiling
        profiling_data = await loop_executor._profile_loop_execution()
        assert "start_time" in profiling_data
        assert "iteration_times" in profiling_data
        
        # Record iteration performance
        import time
        start_time = time.time()
        await asyncio.sleep(0.01)  # Small delay
        end_time = time.time()
        
        await loop_executor._record_iteration_performance(0, start_time, end_time)
        assert len(loop_executor.profiling_data["iteration_times"]) == 1
        
        # Finalize profiling
        report = await loop_executor._finalize_performance_profiling()
        assert "total_execution_time" in report
        assert "throughput" in report


class TestEnhancedErrorHandling:
    """Test enhanced error handling features."""

    @pytest.fixture
    def error_handler(self):
        """Create a LoopErrorHandler instance for testing."""
        return LoopErrorHandler()

    def test_error_classification_network(self, error_handler):
        """Test network error classification."""
        error = ConnectionError("Connection refused")
        context = {}
        
        loop_error = error_handler.classify_error(error, 0, context)
        
        assert loop_error.category == ErrorCategory.NETWORK
        assert loop_error.severity == ErrorSeverity.MEDIUM
        assert loop_error.recovery_strategy == RecoveryStrategy.RETRY

    def test_error_classification_validation(self, error_handler):
        """Test validation error classification."""
        error = ValueError("Invalid input data")
        context = {}
        
        loop_error = error_handler.classify_error(error, 0, context)
        
        assert loop_error.category == ErrorCategory.VALIDATION
        assert loop_error.severity == ErrorSeverity.HIGH
        assert loop_error.recovery_strategy == RecoveryStrategy.SKIP

    def test_error_classification_timeout(self, error_handler):
        """Test timeout error classification."""
        error = TimeoutError("Operation timed out")
        context = {}
        
        loop_error = error_handler.classify_error(error, 0, context)
        
        assert loop_error.category == ErrorCategory.TIMEOUT
        assert loop_error.severity == ErrorSeverity.MEDIUM
        assert loop_error.recovery_strategy == RecoveryStrategy.RETRY

    @pytest.mark.asyncio
    async def test_retry_recovery_strategy(self, error_handler):
        """Test retry recovery strategy."""
        from app.services.loop_executor.loop_error_handler import LoopError
        
        loop_error = LoopError(
            iteration_index=0,
            error=ConnectionError("Network error"),
            category=ErrorCategory.NETWORK,
            severity=ErrorSeverity.MEDIUM,
            recovery_strategy=RecoveryStrategy.RETRY,
            timestamp=0,
            context={"max_retries": 3},
            stack_trace="",
            retry_count=0
        )
        
        result = await error_handler._handle_retry_recovery(loop_error)
        assert result["action"] == "retry"
        assert loop_error.retry_count == 1

    @pytest.mark.asyncio
    async def test_skip_recovery_strategy(self, error_handler):
        """Test skip recovery strategy."""
        from app.services.loop_executor.loop_error_handler import LoopError
        
        loop_error = LoopError(
            iteration_index=0,
            error=ValueError("Invalid data"),
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.HIGH,
            recovery_strategy=RecoveryStrategy.SKIP,
            timestamp=0,
            context={},
            stack_trace=""
        )
        
        result = await error_handler._handle_skip_recovery(loop_error)
        assert result["action"] == "skip"
        assert loop_error.resolved is True

    def test_error_statistics_tracking(self, error_handler):
        """Test error statistics tracking."""
        # Simulate multiple errors
        errors = [
            ConnectionError("Network error 1"),
            ConnectionError("Network error 2"),
            ValueError("Validation error"),
            TimeoutError("Timeout error")
        ]
        
        for i, error in enumerate(errors):
            error_handler.classify_error(error, i, {})
        
        report = error_handler.get_error_report()
        assert report["total_errors"] == 4
        assert "network" in report["category_breakdown"]
        assert "validation" in report["category_breakdown"]
        assert "timeout" in report["category_breakdown"]
