"""
Test Loop Node Architecture.

This module tests the new loop node architecture where:
- Loop nodes have no tools
- Loop nodes have dual outputs (iteration and exit)
- Loop body is formed by workflow connections
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from app.services.loop_executor.loop_executor import LoopExecutor


class TestLoopNodeArchitecture:
    """Test the new loop node architecture."""

    @pytest.fixture
    def loop_executor(self):
        """Create a LoopExecutor instance for testing."""
        state_manager = Mock()
        state_manager.workflow_id = "test_workflow"
        state_manager.store_loop_state = AsyncMock()
        state_manager.save_workflow_state = AsyncMock()

        workflow_utils = Mock()
        result_callback = AsyncMock()
        transitions_by_id = {}
        nodes = {}
        transition_handler = Mock()

        executor = LoopExecutor(
            state_manager=state_manager,
            workflow_utils=workflow_utils,
            result_callback=result_callback,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            transition_handler=transition_handler,
        )

        # Mock the error handler to avoid attribute errors
        executor.error_handler = Mock()
        executor.error_handler.get_error_report.return_value = {"total_errors": 0}

        return executor

    def test_new_schema_config_parsing(self, loop_executor):
        """Test parsing of new schema configuration."""
        new_schema_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "source_type": "static_list",
                "static_items": ["item1", "item2", "item3"],
            },
            "exit_condition": {"condition_type": "all_items_processed"},
            "result_aggregation": {
                "aggregation_type": "collect_all",
                "include_metadata": True,
            },
            "iteration_settings": {
                "parallel_execution": True,
                "max_concurrent": 2,
                "preserve_order": True,
                "iteration_timeout": 30,
            },
            "error_handling": {
                "on_iteration_error": "retry_once",
                "include_errors": True,
            },
        }

        parsed_config = loop_executor.parse_loop_config(new_schema_config)

        # Verify conversion to internal format
        assert parsed_config["loop_type"] == "context_independent"
        assert parsed_config["iteration_source"]["type"] == "list"
        assert parsed_config["iteration_source"]["data"] == ["item1", "item2", "item3"]
        assert parsed_config["aggregation_config"]["type"] == "list"
        assert parsed_config["concurrency"]["enabled"] == True
        assert parsed_config["concurrency"]["max_concurrent"] == 2
        assert parsed_config["retry_config"]["max_retries"] == 1

    def test_new_schema_validation(self, loop_executor):
        """Test validation of new schema configuration."""
        # Valid configuration
        valid_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "source_type": "static_list",
                "static_items": [1, 2, 3],
            },
            "exit_condition": {"condition_type": "all_items_processed"},
        }

        # Should not raise exception
        loop_executor._validate_new_schema_raw(valid_config)

        # Invalid configuration - missing required field
        invalid_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "source_type": "static_list",
                "static_items": [1, 2, 3],
            },
            # Missing exit_condition
        }

        with pytest.raises(
            ValueError,
            match="Required loop configuration field missing: exit_condition",
        ):
            loop_executor._validate_new_schema_raw(invalid_config)

    def test_input_field_iteration_source(self, loop_executor):
        """Test iteration source from input field."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "source_type": "input_field",
                "input_field_path": "orders.items",
            },
            "exit_condition": {"condition_type": "all_items_processed"},
        }

        parsed_config = loop_executor.parse_loop_config(config)
        loop_executor.current_loop_config = parsed_config

        # Mock input data
        loop_executor.input_data = {"orders": {"items": ["order1", "order2", "order3"]}}

        # Prepare iteration data
        loop_executor.prepare_iteration_data_from_input()

        # Verify iteration data was extracted correctly
        assert len(loop_executor.current_iteration_data) == 3
        assert loop_executor.current_iteration_data[0] == (0, "order1")
        assert loop_executor.current_iteration_data[1] == (1, "order2")
        assert loop_executor.current_iteration_data[2] == (2, "order3")

    @pytest.mark.asyncio
    async def test_dual_output_creation(self, loop_executor):
        """Test creation of iteration and exit outputs."""
        # Setup
        loop_executor.current_iteration_data = [(0, "item1"), (1, "item2")]
        loop_executor.loop_context = {
            "completed_iterations": 2,
            "failed_iterations": 0,
            "start_time": 0,
        }
        loop_executor.iteration_results = {
            0: {"result": "processed_item1"},
            1: {"result": "processed_item2"},
        }

        # Mock aggregator
        loop_executor.aggregator = Mock()
        loop_executor.aggregator.aggregate_results.return_value = [
            "processed_item1",
            "processed_item2",
        ]

        # Test iteration output creation
        iteration_output = await loop_executor.create_iteration_output(0, "item1")

        assert iteration_output["current_item"] == "item1"
        assert iteration_output["iteration_index"] == 0
        assert "iteration_metadata" in iteration_output
        assert iteration_output["iteration_metadata"]["total_iterations"] == 2

        # Test exit output creation
        exit_output = await loop_executor.create_exit_output()

        assert exit_output["aggregated_results"] == [
            "processed_item1",
            "processed_item2",
        ]
        assert "metadata" in exit_output
        assert exit_output["metadata"]["total_iterations"] == 2
        assert exit_output["metadata"]["completed_iterations"] == 2
        assert exit_output["metadata"]["failed_iterations"] == 0

    @pytest.mark.asyncio
    async def test_exit_condition_evaluation(self, loop_executor):
        """Test exit condition evaluation."""
        # Test all_items_processed condition
        loop_executor.current_loop_config = {
            "exit_condition": {"condition_type": "all_items_processed"}
        }
        loop_executor.current_iteration_data = [
            (0, "item1"),
            (1, "item2"),
            (2, "item3"),
        ]

        # Should not exit on first iteration
        assert await loop_executor.should_exit_loop(0) == False
        # Should not exit on second iteration
        assert await loop_executor.should_exit_loop(1) == False
        # Should exit on last iteration
        assert await loop_executor.should_exit_loop(2) == True

        # Test max_iterations condition
        loop_executor.current_loop_config = {
            "exit_condition": {"condition_type": "max_iterations", "max_iterations": 2}
        }

        # Should not exit on first iteration
        assert await loop_executor.should_exit_loop(0) == False
        # Should exit on second iteration (max_iterations = 2, so index 1 is the last)
        assert await loop_executor.should_exit_loop(1) == True

    @pytest.mark.asyncio
    async def test_execute_tool_with_new_architecture(self, loop_executor):
        """Test execute_tool method with new loop node architecture."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "source_type": "static_list",
                "static_items": ["item1", "item2"],
            },
            "exit_condition": {"condition_type": "all_items_processed"},
            "result_aggregation": {"aggregation_type": "collect_all"},
        }

        input_data = {"test": "data"}
        output_routing = {
            "iteration_output": "process_item",
            "exit_output": "final_step",
        }

        # Mock required methods
        loop_executor.aggregator = Mock()
        loop_executor.aggregator.aggregate_results.return_value = ["result1", "result2"]
        loop_executor.aggregator.validate_aggregation_config.return_value = True

        # Execute
        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="test_loop",
            input_data=input_data,
            output_routing=output_routing,
        )

        # Verify result structure
        assert "iteration_outputs" in result
        assert "exit_output" in result
        assert len(result["iteration_outputs"]) == 2
        assert result["exit_output"]["aggregated_results"] == ["result1", "result2"]

        # Verify input data and output routing were stored
        assert loop_executor.input_data == input_data
        assert loop_executor.output_routing == output_routing

    def test_range_iteration_source(self, loop_executor):
        """Test range-based iteration source."""
        config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "source_type": "number_range",
                "range_config": {"start": 1, "end": 5, "step": 2},
            },
            "exit_condition": {"condition_type": "all_items_processed"},
        }

        parsed_config = loop_executor.parse_loop_config(config)
        loop_executor.current_loop_config = parsed_config

        # Prepare iteration data
        loop_executor.prepare_iteration_data_from_input()

        # Verify range was generated correctly: range(1, 6, 2) = [1, 3, 5] (inclusive end)
        assert len(loop_executor.current_iteration_data) == 3
        assert loop_executor.current_iteration_data[0] == (0, 1)
        assert loop_executor.current_iteration_data[1] == (1, 3)
        assert loop_executor.current_iteration_data[2] == (2, 5)

    def test_aggregation_type_conversion(self, loop_executor):
        """Test conversion of aggregation types from new schema."""
        test_cases = [
            ("collect_all", {"type": "list"}),
            ("collect_successful", {"type": "list", "filter_successful": True}),
            ("count_only", {"type": "count"}),
            ("combine_text", {"type": "concatenate"}),
        ]

        for aggregation_type, expected_config in test_cases:
            config = {
                "iteration_behavior": "independent",
                "iteration_source": {
                    "source_type": "static_list",
                    "static_items": [1, 2, 3],
                },
                "exit_condition": {"condition_type": "all_items_processed"},
                "result_aggregation": {"aggregation_type": aggregation_type},
            }

            parsed_config = loop_executor.parse_loop_config(config)

            # Check aggregation config conversion
            for key, value in expected_config.items():
                assert parsed_config["aggregation_config"][key] == value

    def test_error_handling_conversion(self, loop_executor):
        """Test conversion of error handling from new schema."""
        test_cases = [
            ("continue", {"max_retries": 0}),
            ("retry_once", {"max_retries": 1, "retry_delay": 1.0}),
            ("retry_twice", {"max_retries": 2, "retry_delay": 1.0}),
            ("exit_loop", {"max_retries": 0, "stop_on_error": True}),
        ]

        for error_action, expected_config in test_cases:
            config = {
                "iteration_behavior": "independent",
                "iteration_source": {
                    "source_type": "static_list",
                    "static_items": [1, 2, 3],
                },
                "exit_condition": {"condition_type": "all_items_processed"},
                "error_handling": {"on_iteration_error": error_action},
            }

            parsed_config = loop_executor.parse_loop_config(config)

            # Check retry config conversion
            for key, value in expected_config.items():
                assert parsed_config["retry_config"][key] == value
