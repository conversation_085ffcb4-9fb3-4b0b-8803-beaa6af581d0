"""
Test Loop Node Execution Flow Sequence according to PRD.

This module tests the critical execution sequence requirements:
1. Initialize Loop -> For Each Iteration -> Complete Loop
2. Loop body transitions and exit transitions must NOT execute in parallel
3. Proper sequential execution flow
4. Chain completion before exit transition execution
5. State management during execution flow
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch, call
from app.services.loop_executor.loop_executor import LoopExecutor
from app.services.loop_executor.loop_body_chain_executor import LoopBodyChainExecutor


class TestLoopNodeExecutionFlow:
    """Test loop node execution flow sequence according to PRD."""

    @pytest.fixture
    def mock_state_manager(self):
        """Create a mock state manager."""
        mock = Mock()
        mock.store_result = Mock()
        mock.get_result = Mock(return_value={"test": "data"})
        mock.mark_transition_completed = Mock()
        return mock

    @pytest.fixture
    def mock_transition_handler(self):
        """Create a mock transition handler."""
        mock = Mock()
        mock.execute_transition = AsyncMock()
        return mock

    @pytest.fixture
    def loop_executor(self, mock_state_manager, mock_transition_handler):
        """Create a loop executor instance for testing."""
        mock_workflow_utils = Mock()
        mock_transitions_by_id = {}
        
        executor = LoopExecutor(
            state_manager=mock_state_manager,
            transition_handler=mock_transition_handler,
            workflow_utils=mock_workflow_utils,
            transitions_by_id=mock_transitions_by_id,
            user_id="test_user"
        )
        return executor

    # ========================================
    # EXECUTION SEQUENCE TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_proper_execution_sequence(self, loop_executor):
        """Test the proper execution sequence: Initialize -> Iterate -> Complete."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2", "item3"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        execution_order = []

        # Mock methods to track execution order
        async def mock_initialize(*args, **kwargs):
            execution_order.append("initialize")

        async def mock_execute_loop(*args, **kwargs):
            execution_order.append("execute_loop")
            return ["result1", "result2", "result3"]

        async def mock_finalize(*args, **kwargs):
            execution_order.append("finalize")

        with patch.object(loop_executor, 'initialize_loop_state', side_effect=mock_initialize):
            with patch.object(loop_executor, 'execute_loop_with_outputs', side_effect=mock_execute_loop):
                with patch.object(loop_executor, 'finalize_loop_execution', side_effect=mock_finalize):
                    
                    await loop_executor.execute_tool(
                        loop_config=loop_config,
                        input_data={},
                        output_routing={"iteration_output": "process", "exit_output": "final"},
                        transition_id="test_transition"
                    )

        # Verify correct execution order
        assert execution_order == ["initialize", "execute_loop", "finalize"]

    @pytest.mark.asyncio
    async def test_no_parallel_execution_of_body_and_exit(self, loop_executor):
        """Test that loop body and exit transitions do not execute in parallel."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        execution_timeline = []

        # Mock loop body execution
        async def mock_execute_iteration(iteration_index, iteration_item):
            execution_timeline.append(f"body_start_{iteration_index}")
            await asyncio.sleep(0.1)  # Simulate work
            execution_timeline.append(f"body_end_{iteration_index}")
            return f"result_{iteration_index}"

        # Mock exit transition execution
        async def mock_send_exit_output(results):
            execution_timeline.append("exit_start")
            await asyncio.sleep(0.05)  # Simulate work
            execution_timeline.append("exit_end")

        with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_execute_iteration):
            with patch.object(loop_executor, '_send_exit_output', side_effect=mock_send_exit_output):
                with patch.object(loop_executor, 'execute_sequential_loop') as mock_sequential:
                    
                    async def sequential_execution():
                        # Execute iterations sequentially
                        for i, item in enumerate(["item1", "item2"]):
                            await mock_execute_iteration(i, item)
                        # Then send exit output
                        await mock_send_exit_output(["result_0", "result_1"])
                    
                    mock_sequential.side_effect = sequential_execution
                    
                    await loop_executor.execute_tool(
                        loop_config=loop_config,
                        input_data={},
                        output_routing={"iteration_output": "process", "exit_output": "final"},
                        transition_id="test_transition"
                    )

        # Verify that all body executions complete before exit starts
        body_end_indices = [i for i, event in enumerate(execution_timeline) if event.startswith("body_end")]
        exit_start_index = next((i for i, event in enumerate(execution_timeline) if event == "exit_start"), -1)
        
        if exit_start_index != -1:
            assert all(i < exit_start_index for i in body_end_indices), \
                f"Body and exit executed in parallel! Timeline: {execution_timeline}"

    @pytest.mark.asyncio
    async def test_sequential_iteration_execution(self, loop_executor):
        """Test sequential execution of iterations when configured."""
        loop_config = {
            "iteration_behavior": "sequential",
            "iteration_source": {
                "iteration_list": ["item1", "item2", "item3"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "iteration_settings": {
                "parallel_execution": False
            }
        }

        execution_order = []

        async def mock_execute_iteration(iteration_index, iteration_item):
            execution_order.append(f"iteration_{iteration_index}_start")
            await asyncio.sleep(0.01)  # Simulate work
            execution_order.append(f"iteration_{iteration_index}_end")
            return f"result_{iteration_index}"

        with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_execute_iteration):
            with patch.object(loop_executor, 'execute_sequential_loop') as mock_sequential:
                
                async def sequential_execution():
                    for i, item in enumerate(["item1", "item2", "item3"]):
                        await mock_execute_iteration(i, item)
                
                mock_sequential.side_effect = sequential_execution
                
                await loop_executor.execute_tool(
                    loop_config=loop_config,
                    input_data={},
                    output_routing={"iteration_output": "process", "exit_output": "final"},
                    transition_id="test_transition"
                )

        # Verify sequential execution (each iteration completes before next starts)
        expected_order = [
            "iteration_0_start", "iteration_0_end",
            "iteration_1_start", "iteration_1_end", 
            "iteration_2_start", "iteration_2_end"
        ]
        assert execution_order == expected_order

    @pytest.mark.asyncio
    async def test_parallel_iteration_execution_when_allowed(self, loop_executor):
        """Test parallel execution of iterations when explicitly configured."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2", "item3"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "iteration_settings": {
                "parallel_execution": True,
                "max_concurrent": 2
            }
        }

        execution_timeline = []

        async def mock_execute_iteration(iteration_index, iteration_item):
            execution_timeline.append(f"iteration_{iteration_index}_start")
            await asyncio.sleep(0.05)  # Simulate work
            execution_timeline.append(f"iteration_{iteration_index}_end")
            return f"result_{iteration_index}"

        with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_execute_iteration):
            with patch.object(loop_executor, 'execute_concurrent_loop') as mock_concurrent:
                
                async def concurrent_execution():
                    # Simulate concurrent execution
                    tasks = [
                        mock_execute_iteration(i, item) 
                        for i, item in enumerate(["item1", "item2", "item3"])
                    ]
                    await asyncio.gather(*tasks)
                
                mock_concurrent.side_effect = concurrent_execution
                
                await loop_executor.execute_tool(
                    loop_config=loop_config,
                    input_data={},
                    output_routing={"iteration_output": "process", "exit_output": "final"},
                    transition_id="test_transition"
                )

        # Verify that some iterations can start before others end (parallel execution)
        start_events = [event for event in execution_timeline if "_start" in event]
        end_events = [event for event in execution_timeline if "_end" in event]
        
        # In parallel execution, we should see multiple starts before all ends
        assert len(start_events) > 0 and len(end_events) > 0

    # ========================================
    # CHAIN COMPLETION TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_chain_completion_before_exit(self, loop_executor):
        """Test that loop body chains complete before exit transitions execute."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        chain_completion_times = []
        exit_execution_time = None

        async def mock_chain_execution(chain_id, *args, **kwargs):
            await asyncio.sleep(0.1)  # Simulate chain work
            completion_time = time.time()
            chain_completion_times.append(completion_time)
            return f"chain_result_{chain_id}"

        async def mock_exit_execution(*args, **kwargs):
            nonlocal exit_execution_time
            exit_execution_time = time.time()

        with patch.object(loop_executor, '_execute_loop_body_chain', side_effect=mock_chain_execution):
            with patch.object(loop_executor, '_send_exit_output', side_effect=mock_exit_execution):
                
                await loop_executor.execute_tool(
                    loop_config=loop_config,
                    input_data={},
                    output_routing={"iteration_output": "process", "exit_output": "final"},
                    transition_id="test_transition"
                )

        # Verify all chains completed before exit execution
        if exit_execution_time and chain_completion_times:
            assert all(completion_time <= exit_execution_time for completion_time in chain_completion_times), \
                "Exit transition executed before all chains completed"

    @pytest.mark.asyncio
    async def test_state_management_during_execution(self, loop_executor):
        """Test proper state management during execution flow."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        state_changes = []

        def mock_state_change(state, *args, **kwargs):
            state_changes.append(state)

        # Mock state manager methods
        loop_executor.state_manager.mark_transition_started = Mock(side_effect=lambda *args: mock_state_change("started"))
        loop_executor.state_manager.mark_transition_completed = Mock(side_effect=lambda *args: mock_state_change("completed"))

        with patch.object(loop_executor, 'execute_loop_with_outputs', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = ["result1", "result2"]
            
            await loop_executor.execute_tool(
                loop_config=loop_config,
                input_data={},
                output_routing={"iteration_output": "process", "exit_output": "final"},
                transition_id="test_transition"
            )

        # Verify state transitions occurred
        assert len(state_changes) > 0

    # ========================================
    # ERROR HANDLING IN EXECUTION FLOW
    # ========================================

    @pytest.mark.asyncio
    async def test_execution_flow_with_iteration_failure(self, loop_executor):
        """Test execution flow when an iteration fails."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2", "item3"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "error_handling": {
                "on_iteration_error": "continue"
            }
        }

        execution_log = []

        async def mock_execute_iteration(iteration_index, iteration_item):
            execution_log.append(f"executing_{iteration_index}")
            if iteration_index == 1:  # Fail second iteration
                raise Exception("Iteration failed")
            return f"result_{iteration_index}"

        async def mock_exit_execution(results):
            execution_log.append("exit_executed")

        with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_execute_iteration):
            with patch.object(loop_executor, '_send_exit_output', side_effect=mock_exit_execution):
                with patch.object(loop_executor, 'execute_sequential_loop') as mock_sequential:
                    
                    async def sequential_with_error():
                        results = []
                        for i, item in enumerate(["item1", "item2", "item3"]):
                            try:
                                result = await mock_execute_iteration(i, item)
                                results.append(result)
                            except Exception:
                                execution_log.append(f"error_{i}")
                                continue  # Continue on error
                        await mock_exit_execution(results)
                    
                    mock_sequential.side_effect = sequential_with_error
                    
                    await loop_executor.execute_tool(
                        loop_config=loop_config,
                        input_data={},
                        output_routing={"iteration_output": "process", "exit_output": "final"},
                        transition_id="test_transition"
                    )

        # Verify execution continued after error and exit was still executed
        assert "executing_0" in execution_log
        assert "executing_1" in execution_log
        assert "error_1" in execution_log
        assert "executing_2" in execution_log
        assert "exit_executed" in execution_log

    @pytest.mark.asyncio
    async def test_execution_flow_with_early_exit(self, loop_executor):
        """Test execution flow with early exit conditions."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2", "item3", "item4", "item5"]
            },
            "exit_condition": {
                "condition_type": "max_iterations",
                "max_iterations": 3
            }
        }

        execution_count = 0
        exit_executed = False

        async def mock_execute_iteration(iteration_index, iteration_item):
            nonlocal execution_count
            execution_count += 1
            return f"result_{iteration_index}"

        async def mock_exit_execution(results):
            nonlocal exit_executed
            exit_executed = True

        with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_execute_iteration):
            with patch.object(loop_executor, '_send_exit_output', side_effect=mock_exit_execution):
                
                await loop_executor.execute_tool(
                    loop_config=loop_config,
                    input_data={},
                    output_routing={"iteration_output": "process", "exit_output": "final"},
                    transition_id="test_transition"
                )

        # Verify early exit occurred and exit transition was executed
        assert execution_count <= 3  # Should not execute more than max_iterations
        assert exit_executed  # Exit should still be executed
