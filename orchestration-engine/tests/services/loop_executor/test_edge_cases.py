"""
Edge Case Tests for Loop Executor.

This module tests edge cases and boundary conditions for loop execution
including empty sets, single iterations, nested loops, and error scenarios.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from app.services.loop_executor.loop_executor import LoopExecutor


class TestLoopExecutorEdgeCases:
    """Test edge cases and boundary conditions for loop execution."""

    @pytest.fixture
    def loop_executor(self):
        """Create a LoopExecutor instance for testing."""
        state_manager = Mock()
        state_manager.workflow_id = "test_workflow"
        state_manager.store_loop_state = AsyncMock()
        state_manager.save_workflow_state = AsyncMock()
        
        workflow_utils = Mock()
        result_callback = Mock()
        transitions_by_id = {"test_transition": {"id": "test_transition"}}
        nodes = {"test_node": {"id": "test_node"}}
        transition_handler = Mock()
        
        return LoopExecutor(
            state_manager=state_manager,
            workflow_utils=workflow_utils,
            result_callback=result_callback,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            transition_handler=transition_handler,
        )

    @pytest.mark.asyncio
    async def test_empty_iteration_set(self, loop_executor):
        """Test loop execution with empty iteration set."""
        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "list",
                "data": []  # Empty list
            },
            "loop_body_transitions": ["test_transition"]
        }

        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="empty_loop"
        )

        # Should return empty result
        assert result == []

    @pytest.mark.asyncio
    async def test_single_iteration_loop(self, loop_executor):
        """Test loop execution with single iteration."""
        loop_config = {
            "loop_type": "context_preserving",
            "aggregation_config": {"type": "object"},
            "iteration_source": {
                "type": "list",
                "data": ["single_item"]
            },
            "loop_body_transitions": ["test_transition"]
        }

        # Mock single iteration
        async def mock_single_iteration(index, item):
            return {"iteration_index": index, "item": item, "status": "completed"}
        
        loop_executor.execute_single_iteration = mock_single_iteration

        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="single_loop"
        )

        # Should return object with single result
        assert isinstance(result, dict)
        assert "results" in result
        assert len(result["results"]) == 1

    @pytest.mark.asyncio
    async def test_range_edge_cases(self, loop_executor):
        """Test range iteration source edge cases."""
        test_cases = [
            # Zero range
            {"start": 5, "stop": 5, "step": 1, "expected_count": 0},
            # Negative step
            {"start": 10, "stop": 0, "step": -2, "expected_count": 5},
            # Single item range
            {"start": 0, "stop": 1, "step": 1, "expected_count": 1},
            # Large step
            {"start": 0, "stop": 100, "step": 50, "expected_count": 2},
        ]

        for case in test_cases:
            loop_config = {
                "loop_type": "context_independent",
                "aggregation_config": {"type": "count"},
                "iteration_source": {
                    "type": "range",
                    "data": {
                        "start": case["start"],
                        "stop": case["stop"],
                        "step": case["step"]
                    }
                },
                "loop_body_transitions": ["test_transition"]
            }

            # Mock iteration execution
            async def mock_count_iteration(index, item):
                return {"count": 1}
            
            loop_executor.execute_single_iteration = mock_count_iteration

            result = await loop_executor.execute_tool(
                loop_config=loop_config,
                transition_id=f"range_test_{case['start']}_{case['stop']}_{case['step']}"
            )

            assert result == case["expected_count"]

    @pytest.mark.asyncio
    async def test_nested_loop_scenarios(self, loop_executor):
        """Test nested loop scenarios (loop within loop body)."""
        # Outer loop configuration
        outer_loop_config = {
            "loop_type": "context_preserving",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "list",
                "data": [["a", "b"], ["c", "d"]]  # Nested data
            },
            "loop_body_transitions": ["inner_loop_transition"]
        }

        # Mock nested iteration execution
        async def mock_nested_iteration(index, item):
            # Simulate inner loop processing
            inner_results = []
            for i, inner_item in enumerate(item):
                inner_results.append({
                    "outer_index": index,
                    "inner_index": i,
                    "item": inner_item,
                    "processed": inner_item.upper()
                })
            return {
                "outer_iteration": index,
                "inner_results": inner_results,
                "status": "completed"
            }
        
        loop_executor.execute_single_iteration = mock_nested_iteration

        result = await loop_executor.execute_tool(
            loop_config=outer_loop_config,
            transition_id="nested_loop"
        )

        # Verify nested structure
        assert isinstance(result, list)
        assert len(result) == 2
        
        # Check first outer iteration
        assert result[0]["outer_iteration"] == 0
        assert len(result[0]["inner_results"]) == 2
        assert result[0]["inner_results"][0]["processed"] == "A"

    @pytest.mark.asyncio
    async def test_loop_execution_with_errors_in_loop_body(self, loop_executor):
        """Test loop execution when errors occur in loop body."""
        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "list",
                "data": ["good1", "bad", "good2", "error", "good3"]
            },
            "loop_body_transitions": ["test_transition"],
            "retry_config": {
                "max_retries": 2,
                "retry_delay": 0.001
            }
        }

        # Mock iteration that fails on specific items
        async def mock_error_prone_iteration(index, item):
            if item == "bad":
                raise ValueError("Validation error")
            elif item == "error":
                raise RuntimeError("System error")
            return {"iteration_index": index, "item": item, "status": "completed"}
        
        loop_executor.execute_single_iteration = mock_error_prone_iteration

        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="error_loop"
        )

        # Should have results for good items and error results for bad items
        assert isinstance(result, list)
        assert len(result) == 5
        
        # Check that good items succeeded
        good_results = [r for r in result if r.get("status") == "completed"]
        assert len(good_results) == 3
        
        # Check that bad items have error status
        error_results = [r for r in result if r.get("status") in ["failed", "skipped"]]
        assert len(error_results) == 2

    @pytest.mark.asyncio
    async def test_memory_limits_with_large_iteration_sets(self, loop_executor):
        """Test loop execution with large iteration sets and memory constraints."""
        # Large iteration set
        large_data = list(range(1000))
        
        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "count"},
            "iteration_source": {
                "type": "list",
                "data": large_data
            },
            "loop_body_transitions": ["test_transition"],
            "performance": {
                "memory_efficient": True,
                "batch_size": 50
            }
        }

        # Mock lightweight iteration
        iteration_count = 0
        async def mock_lightweight_iteration(index, item):
            nonlocal iteration_count
            iteration_count += 1
            return {"count": 1}
        
        loop_executor.execute_single_iteration = mock_lightweight_iteration

        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="large_loop"
        )

        # Verify all iterations were processed
        assert result == 1000
        assert iteration_count == 1000

    @pytest.mark.asyncio
    async def test_concurrent_execution_edge_cases(self, loop_executor):
        """Test concurrent execution edge cases."""
        test_cases = [
            # More concurrent workers than items
            {
                "data": ["a", "b"],
                "max_concurrent": 10,
                "expected_count": 2
            },
            # Single concurrent worker
            {
                "data": ["a", "b", "c", "d"],
                "max_concurrent": 1,
                "expected_count": 4
            },
            # Exact match
            {
                "data": ["a", "b", "c"],
                "max_concurrent": 3,
                "expected_count": 3
            }
        ]

        for case in test_cases:
            loop_config = {
                "loop_type": "context_independent",
                "aggregation_config": {"type": "list"},
                "iteration_source": {
                    "type": "list",
                    "data": case["data"]
                },
                "loop_body_transitions": ["test_transition"],
                "concurrency": {
                    "enabled": True,
                    "max_concurrent": case["max_concurrent"],
                    "preserve_order": True
                }
            }

            # Mock concurrent iteration
            async def mock_concurrent_iteration(index, item):
                await asyncio.sleep(0.001)  # Small delay
                return {"iteration_index": index, "item": item, "status": "completed"}
            
            loop_executor.execute_single_iteration = mock_concurrent_iteration

            result = await loop_executor.execute_tool(
                loop_config=loop_config,
                transition_id=f"concurrent_edge_{case['max_concurrent']}"
            )

            assert len(result) == case["expected_count"]

    @pytest.mark.asyncio
    async def test_malformed_iteration_sources(self, loop_executor):
        """Test handling of malformed iteration sources."""
        malformed_configs = [
            # Missing data field
            {
                "type": "list"
                # Missing "data" field
            },
            # Invalid range configuration
            {
                "type": "range",
                "data": {"start": "invalid", "stop": 10}
            },
            # Non-list data for list type
            {
                "type": "list",
                "data": "not_a_list"
            }
        ]

        for config in malformed_configs:
            loop_config = {
                "loop_type": "context_independent",
                "aggregation_config": {"type": "list"},
                "iteration_source": config,
                "loop_body_transitions": ["test_transition"]
            }

            # Should raise validation error
            with pytest.raises((ValueError, TypeError)):
                await loop_executor.execute_tool(
                    loop_config=loop_config,
                    transition_id="malformed_test"
                )

    @pytest.mark.asyncio
    async def test_extreme_concurrency_scenarios(self, loop_executor):
        """Test extreme concurrency scenarios."""
        # Very high concurrency
        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "range",
                "data": {"start": 0, "stop": 100, "step": 1}
            },
            "loop_body_transitions": ["test_transition"],
            "concurrency": {
                "enabled": True,
                "max_concurrent": 1000,  # Very high
                "preserve_order": True,
                "iteration_timeout": 1.0
            }
        }

        # Mock fast iteration
        async def mock_fast_iteration(index, item):
            return {"iteration_index": index, "value": item * 2}
        
        loop_executor.execute_single_iteration = mock_fast_iteration

        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="extreme_concurrent"
        )

        # Should complete successfully despite high concurrency
        assert len(result) == 100
        
        # Verify order preservation
        for i, item in enumerate(result):
            assert item["iteration_index"] == i

    @pytest.mark.asyncio
    async def test_zero_timeout_scenarios(self, loop_executor):
        """Test scenarios with very short or zero timeouts."""
        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "list",
                "data": ["fast", "slow"]
            },
            "loop_body_transitions": ["test_transition"],
            "concurrency": {
                "enabled": True,
                "max_concurrent": 2,
                "iteration_timeout": 0.001  # Very short timeout
            }
        }

        # Mock iterations with different speeds
        async def mock_variable_speed_iteration(index, item):
            if item == "slow":
                await asyncio.sleep(0.01)  # Longer than timeout
            return {"iteration_index": index, "item": item, "status": "completed"}
        
        loop_executor.execute_single_iteration = mock_variable_speed_iteration

        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="timeout_test"
        )

        # Should have mixed results - some timeouts, some successes
        assert len(result) == 2
