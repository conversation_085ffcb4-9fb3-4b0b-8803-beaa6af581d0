"""
Test Loop Node Edge Cases and <PERSON>rror Scenarios according to PRD.

This module tests edge cases and error scenarios:
1. Large datasets and memory management
2. Timeout scenarios and recovery
3. Failure handling and retry strategies
4. State management under stress
5. Resource cleanup and memory leaks
6. Concurrent execution limits
7. Invalid configurations and data
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from app.services.loop_executor.loop_executor import LoopExecutor


class TestLoopNodeEdgeCases:
    """Test loop node edge cases and error scenarios according to PRD."""

    @pytest.fixture
    def loop_executor(self):
        """Create a loop executor instance for testing."""
        mock_state_manager = Mock()
        mock_transition_handler = Mock()
        mock_workflow_utils = Mock()
        mock_transitions_by_id = {}
        
        executor = LoopExecutor(
            state_manager=mock_state_manager,
            transition_handler=mock_transition_handler,
            workflow_utils=mock_workflow_utils,
            transitions_by_id=mock_transitions_by_id,
            user_id="test_user"
        )
        return executor

    # ========================================
    # LARGE DATASET TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_large_dataset_memory_management(self, loop_executor):
        """Test handling of large datasets without memory issues."""
        # Create a large dataset
        large_dataset = list(range(10000))
        
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": large_dataset
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "iteration_settings": {
                "parallel_execution": True,
                "max_concurrent": 10
            }
        }

        # Mock iteration execution to be lightweight
        async def mock_execute_iteration(iteration_index, iteration_item):
            # Simulate minimal processing
            return f"processed_{iteration_item}"

        with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_execute_iteration):
            with patch.object(loop_executor, 'execute_concurrent_loop') as mock_concurrent:
                
                async def mock_concurrent_execution():
                    # Simulate processing in batches to avoid memory issues
                    batch_size = 100
                    results = []
                    for i in range(0, len(large_dataset), batch_size):
                        batch = large_dataset[i:i+batch_size]
                        batch_results = [f"processed_{item}" for item in batch]
                        results.extend(batch_results)
                        # Simulate some processing time
                        await asyncio.sleep(0.001)
                    return results
                
                mock_concurrent.side_effect = mock_concurrent_execution
                
                result = await loop_executor.execute_tool(
                    loop_config=loop_config,
                    input_data={},
                    output_routing={"iteration_output": "process", "exit_output": "final"},
                    transition_id="test_transition"
                )

        # Should complete without memory errors
        assert result is not None

    def test_large_dataset_batch_processing(self, loop_executor):
        """Test batch processing with large datasets."""
        large_dataset = list(range(1000))
        
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": large_dataset,
                "batch_size": 50
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }
        
        parsed_config = loop_executor.parse_loop_config(loop_config)
        loop_executor.current_loop_config = parsed_config
        loop_executor.prepare_iteration_data_from_input()

        # Should create 20 batches of 50 items each
        assert len(loop_executor.current_iteration_data) == 20
        
        # Verify first and last batches
        assert len(loop_executor.current_iteration_data[0][1]) == 50
        assert len(loop_executor.current_iteration_data[-1][1]) == 50

    # ========================================
    # TIMEOUT SCENARIO TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_iteration_timeout_handling(self, loop_executor):
        """Test handling of iteration timeouts."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2", "item3"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "iteration_settings": {
                "iteration_timeout": 1  # 1 second timeout
            },
            "error_handling": {
                "on_iteration_error": "continue"
            }
        }

        timeout_count = 0

        async def mock_execute_iteration(iteration_index, iteration_item):
            nonlocal timeout_count
            if iteration_index == 1:  # Make second iteration timeout
                await asyncio.sleep(2)  # Longer than timeout
            return f"result_{iteration_index}"

        async def mock_handle_timeout(iteration_index):
            nonlocal timeout_count
            timeout_count += 1

        with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_execute_iteration):
            with patch.object(loop_executor, '_handle_iteration_timeout', side_effect=mock_handle_timeout):
                with patch('asyncio.wait_for', side_effect=lambda coro, timeout: asyncio.create_task(coro) if timeout > 1.5 else asyncio.create_task(mock_handle_timeout(1))):
                    
                    await loop_executor.execute_tool(
                        loop_config=loop_config,
                        input_data={},
                        output_routing={"iteration_output": "process", "exit_output": "final"},
                        transition_id="test_transition"
                    )

        # Should have handled timeout appropriately
        assert timeout_count > 0

    @pytest.mark.asyncio
    async def test_overall_loop_timeout(self, loop_executor):
        """Test overall loop timeout handling."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2", "item3"]
            },
            "exit_condition": {
                "condition_type": "timeout",
                "timeout_seconds": 1
            }
        }

        async def mock_slow_execution(iteration_index, iteration_item):
            await asyncio.sleep(0.5)  # Each iteration takes 0.5 seconds
            return f"result_{iteration_index}"

        with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_slow_execution):
            start_time = time.time()
            
            await loop_executor.execute_tool(
                loop_config=loop_config,
                input_data={},
                output_routing={"iteration_output": "process", "exit_output": "final"},
                transition_id="test_transition"
            )
            
            end_time = time.time()
            
            # Should complete within reasonable time of timeout
            assert end_time - start_time <= 2  # Allow some buffer

    # ========================================
    # FAILURE HANDLING TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_retry_strategies(self, loop_executor):
        """Test different retry strategies for failed iterations."""
        test_cases = [
            ("retry_once", 1),
            ("retry_twice", 2),
        ]

        for retry_strategy, expected_retries in test_cases:
            loop_config = {
                "iteration_behavior": "independent",
                "iteration_source": {
                    "iteration_list": ["item1"]
                },
                "exit_condition": {
                    "condition_type": "all_items_processed"
                },
                "error_handling": {
                    "on_iteration_error": retry_strategy
                }
            }

            retry_count = 0

            async def mock_failing_execution(iteration_index, iteration_item):
                nonlocal retry_count
                retry_count += 1
                raise Exception("Iteration failed")

            with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_failing_execution):
                with patch.object(loop_executor, '_handle_iteration_retry') as mock_retry:
                    
                    await loop_executor.execute_tool(
                        loop_config=loop_config,
                        input_data={},
                        output_routing={"iteration_output": "process", "exit_output": "final"},
                        transition_id="test_transition"
                    )

            # Reset for next test case
            retry_count = 0

    @pytest.mark.asyncio
    async def test_failure_threshold_exit(self, loop_executor):
        """Test exit on failure threshold."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2", "item3", "item4", "item5"]
            },
            "exit_condition": {
                "condition_type": "failure_threshold",
                "max_consecutive_failures": 2
            },
            "error_handling": {
                "on_iteration_error": "exit_loop"
            }
        }

        execution_count = 0

        async def mock_failing_execution(iteration_index, iteration_item):
            nonlocal execution_count
            execution_count += 1
            raise Exception("Iteration failed")

        with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_failing_execution):
            
            await loop_executor.execute_tool(
                loop_config=loop_config,
                input_data={},
                output_routing={"iteration_output": "process", "exit_output": "final"},
                transition_id="test_transition"
            )

        # Should stop after failure threshold is reached
        assert execution_count <= 2

    # ========================================
    # STATE MANAGEMENT STRESS TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_concurrent_state_access(self, loop_executor):
        """Test state management under concurrent access."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": list(range(100))
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "iteration_settings": {
                "parallel_execution": True,
                "max_concurrent": 10
            }
        }

        state_access_count = 0

        async def mock_execute_iteration(iteration_index, iteration_item):
            nonlocal state_access_count
            state_access_count += 1
            # Simulate state access
            loop_executor.state_manager.store_result(f"result_{iteration_index}", f"data_{iteration_item}")
            await asyncio.sleep(0.01)  # Simulate work
            return f"result_{iteration_index}"

        with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_execute_iteration):
            
            await loop_executor.execute_tool(
                loop_config=loop_config,
                input_data={},
                output_routing={"iteration_output": "process", "exit_output": "final"},
                transition_id="test_transition"
            )

        # Should handle concurrent state access without issues
        assert state_access_count > 0

    @pytest.mark.asyncio
    async def test_memory_cleanup_after_completion(self, loop_executor):
        """Test proper memory cleanup after loop completion."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2", "item3"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        # Track memory usage (simplified)
        memory_allocations = []

        async def mock_execute_iteration(iteration_index, iteration_item):
            # Simulate memory allocation
            memory_allocations.append(f"allocated_{iteration_index}")
            return f"result_{iteration_index}"

        async def mock_cleanup():
            # Simulate cleanup
            memory_allocations.clear()

        with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_execute_iteration):
            with patch.object(loop_executor, 'finalize_loop_execution', side_effect=mock_cleanup):
                
                await loop_executor.execute_tool(
                    loop_config=loop_config,
                    input_data={},
                    output_routing={"iteration_output": "process", "exit_output": "final"},
                    transition_id="test_transition"
                )

        # Memory should be cleaned up
        assert len(memory_allocations) == 0

    # ========================================
    # INVALID CONFIGURATION TESTS
    # ========================================

    def test_invalid_iteration_source(self, loop_executor):
        """Test handling of invalid iteration source."""
        invalid_configs = [
            # Missing iteration source
            {
                "iteration_behavior": "independent",
                "exit_condition": {"condition_type": "all_items_processed"}
            },
            # Empty iteration list
            {
                "iteration_behavior": "independent",
                "iteration_source": {"iteration_list": []},
                "exit_condition": {"condition_type": "all_items_processed"}
            },
            # Invalid number range
            {
                "iteration_behavior": "independent",
                "iteration_source": {
                    "number_range": {"start": 10, "end": 5}  # start > end
                },
                "exit_condition": {"condition_type": "all_items_processed"}
            }
        ]

        for config in invalid_configs:
            with pytest.raises((ValueError, KeyError)):
                loop_executor.parse_loop_config(config)

    def test_invalid_exit_conditions(self, loop_executor):
        """Test handling of invalid exit conditions."""
        invalid_configs = [
            # Missing required parameters
            {
                "iteration_behavior": "independent",
                "iteration_source": {"iteration_list": [1, 2, 3]},
                "exit_condition": {"condition_type": "max_iterations"}  # Missing max_iterations
            },
            # Invalid timeout
            {
                "iteration_behavior": "independent",
                "iteration_source": {"iteration_list": [1, 2, 3]},
                "exit_condition": {"condition_type": "timeout", "timeout_seconds": -1}
            }
        ]

        for config in invalid_configs:
            with pytest.raises(ValueError):
                loop_executor.parse_loop_config(config)

    # ========================================
    # RESOURCE LIMIT TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_concurrent_execution_limits(self, loop_executor):
        """Test enforcement of concurrent execution limits."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": list(range(20))
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "iteration_settings": {
                "parallel_execution": True,
                "max_concurrent": 3
            }
        }

        active_executions = 0
        max_concurrent_observed = 0

        async def mock_execute_iteration(iteration_index, iteration_item):
            nonlocal active_executions, max_concurrent_observed
            active_executions += 1
            max_concurrent_observed = max(max_concurrent_observed, active_executions)
            
            await asyncio.sleep(0.1)  # Simulate work
            
            active_executions -= 1
            return f"result_{iteration_index}"

        with patch.object(loop_executor, '_execute_single_iteration', side_effect=mock_execute_iteration):
            
            await loop_executor.execute_tool(
                loop_config=loop_config,
                input_data={},
                output_routing={"iteration_output": "process", "exit_output": "final"},
                transition_id="test_transition"
            )

        # Should not exceed max_concurrent limit
        assert max_concurrent_observed <= 3
