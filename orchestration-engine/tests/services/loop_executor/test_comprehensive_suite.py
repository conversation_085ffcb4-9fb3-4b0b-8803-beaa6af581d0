"""
Comprehensive Test Suite for Loop Executor.

This module provides a comprehensive test runner and validation suite
for all loop executor functionality across all phases.
"""

import pytest
import asyncio
import time
import sys
from typing import Dict, List, Any
from unittest.mock import Mock, AsyncMock

# Import available test modules
try:
    from . import test_loop_executor
except ImportError:
    test_loop_executor = None

try:
    from . import test_loop_state_manager
except ImportError:
    test_loop_state_manager = None

try:
    from . import test_loop_aggregator
except ImportError:
    test_loop_aggregator = None

try:
    from . import test_loop_validator
except ImportError:
    test_loop_validator = None

try:
    from . import test_concurrency
except ImportError:
    test_concurrency = None

try:
    from . import test_phase5_advanced_features
except ImportError:
    test_phase5_advanced_features = None

try:
    from . import test_integration
except ImportError:
    test_integration = None

try:
    from . import test_edge_cases
except ImportError:
    test_edge_cases = None

try:
    from . import test_performance
except ImportError:
    test_performance = None

try:
    from . import test_end_to_end
except ImportError:
    test_end_to_end = None


class TestComprehensiveSuite:
    """Comprehensive test suite for all loop executor functionality."""

    @pytest.fixture(scope="class")
    def test_metrics(self):
        """Track test execution metrics."""
        return {
            "start_time": time.time(),
            "test_counts": {},
            "performance_metrics": {},
            "coverage_areas": set(),
        }

    def test_phase1_core_infrastructure(self, test_metrics):
        """Validate Phase 1: Core Loop Infrastructure."""
        test_metrics["coverage_areas"].add("Phase 1: Core Infrastructure")

        # Core components should be importable and functional
        from app.services.loop_executor.loop_executor import LoopExecutor
        from app.services.loop_executor.loop_state_manager import LoopStateManager
        from app.services.loop_executor.loop_validator import LoopValidator

        # Verify core classes can be instantiated
        state_manager = Mock()
        workflow_utils = Mock()
        result_callback = Mock()
        transitions_by_id = {}
        nodes = {}
        transition_handler = Mock()

        executor = LoopExecutor(
            state_manager=state_manager,
            workflow_utils=workflow_utils,
            result_callback=result_callback,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            transition_handler=transition_handler,
        )

        assert executor is not None
        assert hasattr(executor, "execute_tool")
        assert hasattr(executor, "execute_single_iteration")

        # Verify state manager
        loop_state_manager = LoopStateManager(
            "test_loop", "test_transition", "test_workflow"
        )
        assert loop_state_manager is not None
        assert hasattr(loop_state_manager, "initialize_loop_state")

        # Verify validator
        validator = LoopValidator()
        assert validator is not None
        assert hasattr(validator, "validate_loop_config")

    def test_phase2_state_management(self, test_metrics):
        """Validate Phase 2: State Management Integration."""
        test_metrics["coverage_areas"].add("Phase 2: State Management")

        from app.services.loop_executor.loop_state_manager import LoopStateManager

        # Test state management functionality
        state_manager = LoopStateManager(
            "test_loop", "test_transition", "test_workflow"
        )

        # Initialize state
        state_manager.initialize_loop_state(10)
        assert state_manager.total_iterations == 10
        assert state_manager.current_iteration_index == 0

        # Test state persistence
        state_data = state_manager.backup_loop_state()
        assert isinstance(state_data, dict)
        assert "loop_id" in state_data
        assert "total_iterations" in state_data

    def test_phase3_result_aggregation(self, test_metrics):
        """Validate Phase 3: Result Aggregation."""
        test_metrics["coverage_areas"].add("Phase 3: Result Aggregation")

        from app.services.loop_executor.loop_aggregator import LoopAggregator

        aggregator = LoopAggregator()

        # Test different aggregation types
        test_results = [1, 2, 3, 4, 5]

        # List aggregation
        list_result = aggregator.aggregate_results(test_results, {"type": "list"})
        assert isinstance(list_result, list)
        assert len(list_result) == 5

        # Count aggregation
        count_result = aggregator.aggregate_results(test_results, {"type": "count"})
        assert count_result == 5

        # Object aggregation
        object_result = aggregator.aggregate_results(test_results, {"type": "object"})
        assert isinstance(object_result, dict)

    def test_phase4_concurrency_support(self, test_metrics):
        """Validate Phase 4: Concurrency Support."""
        test_metrics["coverage_areas"].add("Phase 4: Concurrency Support")

        from app.services.loop_executor.loop_executor import LoopExecutor

        # Test concurrency configuration validation
        state_manager = Mock()
        executor = LoopExecutor(
            state_manager=state_manager,
            workflow_utils=Mock(),
            result_callback=Mock(),
            transitions_by_id={},
            nodes={},
            transition_handler=Mock(),
        )

        # Verify concurrency methods exist
        assert hasattr(executor, "_execute_concurrent_loop")
        assert hasattr(executor, "_execute_with_early_exit_monitoring")
        assert hasattr(executor, "_check_early_exit_conditions")

    def test_phase5_advanced_features(self, test_metrics):
        """Validate Phase 5: Advanced Features."""
        test_metrics["coverage_areas"].add("Phase 5: Advanced Features")

        from app.services.loop_executor.loop_executor import LoopExecutor
        from app.services.loop_executor.loop_error_handler import LoopErrorHandler
        from app.services.loop_executor.loop_aggregator import LoopAggregator

        # Test advanced features exist
        executor = LoopExecutor(
            state_manager=Mock(),
            workflow_utils=Mock(),
            result_callback=Mock(),
            transitions_by_id={},
            nodes={},
            transition_handler=Mock(),
        )

        # Conditional termination
        assert hasattr(executor, "_evaluate_termination_conditions")
        assert hasattr(executor, "_handle_loop_break")

        # Performance optimizations
        assert hasattr(executor, "_enable_memory_efficient_iteration")
        assert hasattr(executor, "_profile_loop_execution")

        # Enhanced error handling
        error_handler = LoopErrorHandler()
        assert hasattr(error_handler, "classify_error")
        assert hasattr(error_handler, "handle_error")

        # Custom aggregation
        aggregator = LoopAggregator()
        assert hasattr(aggregator, "_aggregate_custom")
        assert hasattr(aggregator, "_execute_custom_function")

    @pytest.mark.asyncio
    async def test_integration_completeness(self, test_metrics):
        """Validate integration between all components."""
        test_metrics["coverage_areas"].add("Integration Testing")

        from app.services.loop_executor.loop_executor import LoopExecutor

        # Create fully integrated executor
        state_manager = Mock()
        state_manager.workflow_id = "integration_test"
        state_manager.store_loop_state = AsyncMock()
        state_manager.save_workflow_state = AsyncMock()

        executor = LoopExecutor(
            state_manager=state_manager,
            workflow_utils=Mock(),
            result_callback=AsyncMock(),
            transitions_by_id={"test": {"id": "test"}},
            nodes={"node": {"id": "node"}},
            transition_handler=Mock(),
        )

        # Test basic integration
        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {"type": "list", "data": [1, 2, 3]},
            "loop_body_transitions": ["test"],
        }

        # Mock iteration execution
        async def mock_iteration(index, item):
            return {"index": index, "item": item}

        executor.execute_single_iteration = mock_iteration

        # Execute integration test
        result = await executor.execute_tool(
            loop_config=loop_config, transition_id="integration_test"
        )

        assert isinstance(result, list)
        assert len(result) == 3

    def test_error_handling_completeness(self, test_metrics):
        """Validate comprehensive error handling."""
        test_metrics["coverage_areas"].add("Error Handling")

        from app.services.loop_executor.loop_error_handler import (
            LoopErrorHandler,
            ErrorCategory,
            ErrorSeverity,
            RecoveryStrategy,
        )

        error_handler = LoopErrorHandler()

        # Test error classification
        test_errors = [
            (ConnectionError("Network error"), ErrorCategory.NETWORK),
            (ValueError("Invalid data"), ErrorCategory.VALIDATION),
            (TimeoutError("Timeout"), ErrorCategory.TIMEOUT),
            (MemoryError("Out of memory"), ErrorCategory.RESOURCE),
        ]

        for error, expected_category in test_errors:
            loop_error = error_handler.classify_error(error, 0, {})
            assert loop_error.category == expected_category
            assert loop_error.severity in [s for s in ErrorSeverity]
            assert loop_error.recovery_strategy in [r for r in RecoveryStrategy]

    def test_performance_characteristics(self, test_metrics):
        """Validate performance characteristics."""
        test_metrics["coverage_areas"].add("Performance Testing")

        start_time = time.time()

        # Test import performance
        from app.services.loop_executor.loop_executor import LoopExecutor
        from app.services.loop_executor.loop_state_manager import LoopStateManager
        from app.services.loop_executor.loop_aggregator import LoopAggregator
        from app.services.loop_executor.loop_validator import LoopValidator
        from app.services.loop_executor.loop_error_handler import LoopErrorHandler

        import_time = time.time() - start_time
        test_metrics["performance_metrics"]["import_time"] = import_time

        # Import time should be reasonable (less than 1 second)
        assert import_time < 1.0, f"Import time too slow: {import_time:.2f}s"

    def test_memory_usage_validation(self, test_metrics):
        """Validate memory usage patterns."""
        test_metrics["coverage_areas"].add("Memory Validation")

        try:
            import psutil
            import os

            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB

            # Create multiple executor instances
            executors = []
            for i in range(10):
                state_manager = Mock()
                executor = LoopExecutor(
                    state_manager=state_manager,
                    workflow_utils=Mock(),
                    result_callback=Mock(),
                    transitions_by_id={},
                    nodes={},
                    transition_handler=Mock(),
                )
                executors.append(executor)

            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_delta = final_memory - initial_memory

            test_metrics["performance_metrics"]["memory_per_executor"] = (
                memory_delta / 10
            )

            # Memory usage should be reasonable (less than 10MB per executor)
            assert (
                memory_delta < 100
            ), f"Memory usage too high: {memory_delta:.2f}MB for 10 executors"

        except ImportError:
            # psutil not available, skip memory test
            pytest.skip("psutil not available for memory testing")

    def test_schema_validation_completeness(self, test_metrics):
        """Validate schema validation completeness."""
        test_metrics["coverage_areas"].add("Schema Validation")

        from app.services.loop_executor.loop_validator import LoopValidator

        validator = LoopValidator()

        # Test valid configurations
        valid_configs = [
            {
                "loop_type": "context_preserving",
                "aggregation_config": {"type": "list"},
                "iteration_source": {"type": "list", "data": [1, 2, 3]},
                "loop_body_transitions": ["test"],
            },
            {
                "loop_type": "context_independent",
                "aggregation_config": {"type": "object"},
                "iteration_source": {"type": "range", "data": {"start": 0, "stop": 10}},
                "loop_body_transitions": ["test"],
                "concurrency": {"enabled": True, "max_concurrent": 5},
            },
        ]

        for config in valid_configs:
            # Should not raise exception
            validator.validate_loop_config(config)

        # Test invalid configurations
        invalid_configs = [
            {},  # Empty config
            {"loop_type": "invalid"},  # Invalid loop type
            {"loop_type": "context_preserving"},  # Missing required fields
        ]

        for config in invalid_configs:
            with pytest.raises((ValueError, KeyError)):
                validator.validate_loop_config(config)

    def test_documentation_coverage(self, test_metrics):
        """Validate documentation coverage."""
        test_metrics["coverage_areas"].add("Documentation")

        # Check that all main classes have docstrings
        from app.services.loop_executor.loop_executor import LoopExecutor
        from app.services.loop_executor.loop_state_manager import LoopStateManager
        from app.services.loop_executor.loop_aggregator import LoopAggregator
        from app.services.loop_executor.loop_validator import LoopValidator
        from app.services.loop_executor.loop_error_handler import LoopErrorHandler

        classes_to_check = [
            LoopExecutor,
            LoopStateManager,
            LoopAggregator,
            LoopValidator,
            LoopErrorHandler,
        ]

        for cls in classes_to_check:
            assert cls.__doc__ is not None, f"{cls.__name__} missing class docstring"

            # Check key methods have docstrings
            key_methods = [
                method
                for method in dir(cls)
                if not method.startswith("_") and callable(getattr(cls, method))
            ]

            for method_name in key_methods[:5]:  # Check first 5 public methods
                method = getattr(cls, method_name)
                if callable(method):
                    assert (
                        method.__doc__ is not None
                    ), f"{cls.__name__}.{method_name} missing docstring"

    def test_final_validation_summary(self, test_metrics):
        """Generate final validation summary."""
        end_time = time.time()
        total_time = end_time - test_metrics["start_time"]

        test_metrics["performance_metrics"]["total_test_time"] = total_time

        # Generate summary report
        coverage_areas = test_metrics["coverage_areas"]
        expected_areas = {
            "Phase 1: Core Infrastructure",
            "Phase 2: State Management",
            "Phase 3: Result Aggregation",
            "Phase 4: Concurrency Support",
            "Phase 5: Advanced Features",
            "Integration Testing",
            "Error Handling",
            "Performance Testing",
            "Memory Validation",
            "Schema Validation",
            "Documentation",
        }

        coverage_percentage = len(coverage_areas) / len(expected_areas) * 100

        print(f"\n{'='*60}")
        print(f"LOOP EXECUTOR COMPREHENSIVE TEST SUMMARY")
        print(f"{'='*60}")
        print(
            f"Coverage Areas Tested: {len(coverage_areas)}/{len(expected_areas)} ({coverage_percentage:.1f}%)"
        )
        print(f"Total Test Time: {total_time:.2f} seconds")

        if "import_time" in test_metrics["performance_metrics"]:
            print(
                f"Import Performance: {test_metrics['performance_metrics']['import_time']:.3f}s"
            )

        if "memory_per_executor" in test_metrics["performance_metrics"]:
            print(
                f"Memory per Executor: {test_metrics['performance_metrics']['memory_per_executor']:.2f}MB"
            )

        print(f"\nCovered Areas:")
        for area in sorted(coverage_areas):
            print(f"  ✅ {area}")

        missing_areas = expected_areas - coverage_areas
        if missing_areas:
            print(f"\nMissing Areas:")
            for area in sorted(missing_areas):
                print(f"  ❌ {area}")

        print(f"{'='*60}")

        # Ensure minimum coverage
        assert (
            coverage_percentage >= 90
        ), f"Test coverage too low: {coverage_percentage:.1f}%"

        # Ensure reasonable performance
        assert total_time < 30, f"Test suite too slow: {total_time:.2f}s"
