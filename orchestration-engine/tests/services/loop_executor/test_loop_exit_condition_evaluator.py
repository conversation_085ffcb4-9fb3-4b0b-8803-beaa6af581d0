"""
Tests for LoopExitConditionEvaluator.

This module tests the exit condition evaluation logic for different types of loop exit conditions.
"""

import pytest
import time
from app.services.loop_executor.loop_exit_condition_evaluator import LoopExitConditionEvaluator


class TestLoopExitConditionEvaluator:
    """Test the LoopExitConditionEvaluator class."""

    @pytest.fixture
    def evaluator(self):
        """Create a LoopExitConditionEvaluator instance for testing."""
        return LoopExitConditionEvaluator()

    def test_all_items_processed_condition(self, evaluator):
        """Test all_items_processed exit condition."""
        config = {"condition_type": "all_items_processed"}
        evaluator.initialize_evaluation(config)

        # Should not exit before all items are processed
        assert not evaluator.should_exit_loop(0, 5, {}, {})
        assert not evaluator.should_exit_loop(3, 5, {}, {})
        
        # Should exit when all items are processed
        assert evaluator.should_exit_loop(4, 5, {}, {})

    def test_max_iterations_condition(self, evaluator):
        """Test max_iterations exit condition."""
        config = {"condition_type": "max_iterations", "max_iterations": 3}
        evaluator.initialize_evaluation(config)

        # Should not exit before max iterations
        assert not evaluator.should_exit_loop(0, 10, {}, {})
        assert not evaluator.should_exit_loop(1, 10, {}, {})
        
        # Should exit at max iterations
        assert evaluator.should_exit_loop(2, 10, {}, {})
        assert evaluator.should_exit_loop(5, 10, {}, {})

    def test_timeout_condition(self, evaluator):
        """Test timeout exit condition."""
        config = {"condition_type": "timeout", "timeout_minutes": 0.01}  # 0.6 seconds
        evaluator.initialize_evaluation(config)

        # Should not exit immediately
        assert not evaluator.should_exit_loop(0, 10, {}, {})
        
        # Wait for timeout
        time.sleep(0.7)
        
        # Should exit after timeout
        assert evaluator.should_exit_loop(1, 10, {}, {})

    def test_success_condition(self, evaluator):
        """Test success_condition exit condition."""
        config = {"condition_type": "success_condition", "success_threshold": 2}
        evaluator.initialize_evaluation(config)

        # Create iteration results with successes
        iteration_results = {
            0: {"status": "success", "result": "data1"},
            1: {"status": "failed", "error": "error1"},
            2: {"status": "success", "result": "data2"},
        }

        # Should not exit before threshold
        single_success = {0: {"status": "success", "result": "data1"}}
        assert not evaluator.should_exit_loop(0, 10, single_success, {})
        
        # Should exit when threshold is met
        assert evaluator.should_exit_loop(2, 10, iteration_results, {})

    def test_failure_threshold_condition(self, evaluator):
        """Test failure_threshold exit condition."""
        config = {"condition_type": "failure_threshold", "failure_threshold": 2}
        evaluator.initialize_evaluation(config)

        # Create iteration results with consecutive failures
        iteration_results = {
            0: {"status": "success", "result": "data1"},
            1: {"status": "failed", "error": "error1"},
            2: {"status": "failed", "error": "error2"},
        }

        # Should not exit with non-consecutive failures
        non_consecutive = {
            0: {"status": "failed", "error": "error1"},
            1: {"status": "success", "result": "data1"},
            2: {"status": "failed", "error": "error2"},
        }
        assert not evaluator.should_exit_loop(2, 10, non_consecutive, {})
        
        # Should exit with consecutive failures at the end
        assert evaluator.should_exit_loop(2, 10, iteration_results, {})

    def test_unknown_condition_type(self, evaluator):
        """Test handling of unknown condition types."""
        config = {"condition_type": "unknown_condition"}
        evaluator.initialize_evaluation(config)

        # Should default to all_items_processed behavior
        assert not evaluator.should_exit_loop(0, 5, {}, {})
        assert evaluator.should_exit_loop(4, 5, {}, {})

    def test_is_iteration_successful(self, evaluator):
        """Test iteration success detection."""
        # Test explicit status success
        assert evaluator._is_iteration_successful({"status": "success"})
        assert evaluator._is_iteration_successful({"status": "completed"})
        assert evaluator._is_iteration_successful({"status": "ok"})
        
        # Test no error field
        assert evaluator._is_iteration_successful({"result": "data"})
        
        # Test result/data presence
        assert evaluator._is_iteration_successful({"data": "some_data"})
        
        # Test non-dict success
        assert evaluator._is_iteration_successful("some_result")
        assert evaluator._is_iteration_successful(42)
        
        # Test failure cases
        assert not evaluator._is_iteration_successful({"status": "failed"})
        assert not evaluator._is_iteration_successful({"error": "some_error"})
        assert not evaluator._is_iteration_successful(None)

    def test_is_iteration_failed(self, evaluator):
        """Test iteration failure detection."""
        # Test explicit status failure
        assert evaluator._is_iteration_failed({"status": "failed"})
        assert evaluator._is_iteration_failed({"status": "error"})
        assert evaluator._is_iteration_failed({"status": "timeout"})
        
        # Test error field presence
        assert evaluator._is_iteration_failed({"error": "some_error"})
        
        # Test None result
        assert evaluator._is_iteration_failed(None)
        
        # Test success cases
        assert not evaluator._is_iteration_failed({"status": "success"})
        assert not evaluator._is_iteration_failed({"result": "data"})
        assert not evaluator._is_iteration_failed("some_result")

    def test_evaluation_summary(self, evaluator):
        """Test evaluation summary generation."""
        config = {
            "condition_type": "max_iterations",
            "max_iterations": 5,
            "timeout_minutes": 10
        }
        evaluator.initialize_evaluation(config)

        summary = evaluator.get_evaluation_summary()
        
        assert summary["condition_type"] == "max_iterations"
        assert summary["max_iterations"] == 5
        assert "start_time" in summary
        assert "elapsed_time" in summary
        assert summary["consecutive_failures"] == 0
        assert summary["success_count"] == 0

    def test_reset_evaluation_state(self, evaluator):
        """Test evaluation state reset."""
        config = {"condition_type": "timeout", "timeout_minutes": 1}
        evaluator.initialize_evaluation(config)
        
        # Modify state
        evaluator.consecutive_failures = 5
        evaluator.success_count = 3
        
        # Reset
        evaluator.reset_evaluation_state()
        
        assert evaluator.start_time is None
        assert evaluator.consecutive_failures == 0
        assert evaluator.success_count == 0

    def test_error_handling_in_evaluation(self, evaluator):
        """Test error handling during evaluation."""
        # Initialize with invalid config to trigger error
        config = {"condition_type": "timeout"}  # Missing timeout_minutes
        evaluator.initialize_evaluation(config)
        
        # Should not crash and should default to continuing
        result = evaluator.should_exit_loop(0, 10, {}, {})
        assert isinstance(result, bool)

    def test_uninitialized_evaluator(self, evaluator):
        """Test behavior when evaluator is not initialized."""
        # Should default to all_items_processed behavior
        assert not evaluator.should_exit_loop(0, 5, {}, {})
        assert evaluator.should_exit_loop(4, 5, {}, {})
