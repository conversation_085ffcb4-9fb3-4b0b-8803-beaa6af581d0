"""
End-to-End Tests for Loop Executor.

This module tests complete workflow scenarios with loop execution,
real-world use cases, and comprehensive integration testing.
"""

import pytest
import async<PERSON>
import json
from unittest.mock import Mock, AsyncMock, patch
from app.services.loop_executor.loop_executor import LoopExecutor


class TestLoopExecutorEndToEnd:
    """Test end-to-end loop execution scenarios."""

    @pytest.fixture
    def workflow_engine_mock(self):
        """Create a mock workflow engine for end-to-end testing."""
        engine = Mock()
        engine.execute_workflow = AsyncMock()
        engine.get_workflow_state = AsyncMock()
        engine.save_workflow_state = AsyncMock()
        return engine

    @pytest.fixture
    def complete_loop_executor(self, workflow_engine_mock):
        """Create a complete LoopExecutor setup for end-to-end testing."""
        state_manager = Mock()
        state_manager.workflow_id = "e2e_workflow_123"
        state_manager.store_loop_state = AsyncMock()
        state_manager.load_loop_state = AsyncMock()
        state_manager.save_workflow_state = AsyncMock()
        
        workflow_utils = Mock()
        workflow_utils.resolve_parameters = Mock(side_effect=lambda x, _: x)
        
        result_callback = AsyncMock()
        
        transitions_by_id = {
            "data_processing": {"id": "data_processing", "type": "tool"},
            "validation": {"id": "validation", "type": "tool"},
            "transformation": {"id": "transformation", "type": "tool"},
            "aggregation": {"id": "aggregation", "type": "tool"}
        }
        
        nodes = {
            "loop_node": {"id": "loop_node", "type": "loop"},
            "next_node": {"id": "next_node", "type": "tool"}
        }
        
        transition_handler = Mock()
        transition_handler.execute_transition = AsyncMock()
        
        return LoopExecutor(
            state_manager=state_manager,
            workflow_utils=workflow_utils,
            result_callback=result_callback,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            transition_handler=transition_handler,
        )

    @pytest.mark.asyncio
    async def test_blog_writing_workflow_scenario(self, complete_loop_executor):
        """Test end-to-end blog writing workflow with loop execution."""
        # Blog writing scenario: process multiple topics
        blog_topics = [
            {"title": "AI in Healthcare", "keywords": ["AI", "healthcare", "technology"]},
            {"title": "Sustainable Energy", "keywords": ["renewable", "solar", "wind"]},
            {"title": "Remote Work Trends", "keywords": ["remote", "productivity", "collaboration"]}
        ]

        loop_config = {
            "loop_type": "context_preserving",
            "aggregation_config": {
                "type": "custom",
                "custom_function": {
                    "type": "builtin",
                    "name": "flatten"
                }
            },
            "iteration_source": {
                "type": "list",
                "data": blog_topics
            },
            "loop_body_transitions": ["data_processing", "validation", "transformation"],
            "exit_transition": "aggregation",
            "concurrency": {
                "enabled": True,
                "max_concurrent": 2,
                "preserve_order": True
            }
        }

        # Mock blog processing pipeline
        async def mock_blog_iteration(index, topic):
            # Simulate blog content generation
            content = {
                "title": topic["title"],
                "content": f"Generated content for {topic['title']}...",
                "keywords": topic["keywords"],
                "word_count": len(topic["title"]) * 50,  # Simulated word count
                "status": "completed",
                "iteration_index": index
            }
            
            # Simulate processing time
            await asyncio.sleep(0.01)
            
            return content
        
        complete_loop_executor.execute_single_iteration = mock_blog_iteration

        # Execute blog writing workflow
        result = await complete_loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="blog_writing_loop"
        )

        # Verify blog writing results
        assert isinstance(result, list)
        assert len(result) == 3
        
        # Check each blog post
        for i, blog_post in enumerate(result):
            assert blog_post["title"] == blog_topics[i]["title"]
            assert blog_post["status"] == "completed"
            assert blog_post["word_count"] > 0
            assert "content" in blog_post

    @pytest.mark.asyncio
    async def test_data_processing_pipeline_scenario(self, complete_loop_executor):
        """Test end-to-end data processing pipeline with validation and transformation."""
        # Data processing scenario: process customer records
        customer_data = [
            {"id": 1, "name": "John Doe", "email": "<EMAIL>", "age": 30},
            {"id": 2, "name": "Jane Smith", "email": "<EMAIL>", "age": 25},
            {"id": 3, "name": "Bob Johnson", "email": "invalid-email", "age": -5},  # Invalid data
            {"id": 4, "name": "Alice Brown", "email": "<EMAIL>", "age": 35},
            {"id": 5, "name": "", "email": "<EMAIL>", "age": 40}  # Invalid name
        ]

        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {
                "type": "custom",
                "custom_function": {
                    "type": "lambda",
                    "expression": "[item for item in results if item.get('valid', False)]"
                }
            },
            "iteration_source": {
                "type": "list",
                "data": customer_data
            },
            "loop_body_transitions": ["validation", "transformation"],
            "retry_config": {
                "max_retries": 2,
                "retry_delay": 0.001
            }
        }

        # Mock data processing with validation
        async def mock_data_processing_iteration(index, customer):
            # Validation step
            is_valid = (
                customer.get("name", "").strip() != "" and
                "@" in customer.get("email", "") and
                customer.get("age", 0) > 0
            )
            
            if not is_valid:
                # Return invalid record with error info
                return {
                    "id": customer["id"],
                    "valid": False,
                    "error": "Validation failed",
                    "original_data": customer,
                    "iteration_index": index
                }
            
            # Transformation step for valid records
            transformed_customer = {
                "id": customer["id"],
                "name": customer["name"].title(),
                "email": customer["email"].lower(),
                "age": customer["age"],
                "age_group": "young" if customer["age"] < 30 else "adult",
                "valid": True,
                "processed_at": "2024-01-01T00:00:00Z",
                "iteration_index": index
            }
            
            return transformed_customer
        
        complete_loop_executor.execute_single_iteration = mock_data_processing_iteration

        # Execute data processing pipeline
        result = await complete_loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="data_processing_pipeline"
        )

        # Verify data processing results
        assert isinstance(result, list)
        
        # Should only have valid records (customers 1, 2, and 4)
        valid_customers = [r for r in result if r.get("valid", False)]
        assert len(valid_customers) == 3
        
        # Check transformations
        for customer in valid_customers:
            assert customer["name"].istitle()  # Name should be title case
            assert customer["email"].islower()  # Email should be lowercase
            assert customer["age_group"] in ["young", "adult"]

    @pytest.mark.asyncio
    async def test_list_processing_with_error_recovery(self, complete_loop_executor):
        """Test list processing with comprehensive error recovery."""
        # Mixed data with various error scenarios
        mixed_data = [
            {"type": "number", "value": 10},
            {"type": "string", "value": "hello"},
            {"type": "number", "value": "invalid"},  # Type mismatch
            {"type": "boolean", "value": True},
            {"type": "null", "value": None},
            {"type": "number", "value": 25},
            {"type": "error", "value": "trigger_error"}  # Explicit error trigger
        ]

        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {
                "type": "custom",
                "custom_function": {
                    "type": "code",
                    "code": """
successful = [r for r in results if r.get('status') == 'success']
failed = [r for r in results if r.get('status') == 'error']
result = {
    'successful_count': len(successful),
    'failed_count': len(failed),
    'success_rate': len(successful) / len(results) if results else 0,
    'successful_items': successful,
    'failed_items': failed
}
"""
                }
            },
            "iteration_source": {
                "type": "list",
                "data": mixed_data
            },
            "loop_body_transitions": ["data_processing"],
            "retry_config": {
                "max_retries": 1,
                "retry_delay": 0.001
            }
        }

        # Mock processing with error scenarios
        async def mock_error_prone_processing(index, item):
            if item.get("value") == "trigger_error":
                raise RuntimeError("Explicit error triggered")
            
            if item.get("type") == "number" and not isinstance(item.get("value"), (int, float)):
                raise ValueError(f"Invalid number: {item.get('value')}")
            
            # Successful processing
            processed_value = item["value"]
            if item["type"] == "string":
                processed_value = processed_value.upper()
            elif item["type"] == "number":
                processed_value = processed_value * 2
            
            return {
                "original": item,
                "processed_value": processed_value,
                "type": item["type"],
                "status": "success",
                "iteration_index": index
            }
        
        complete_loop_executor.execute_single_iteration = mock_error_prone_processing

        # Execute with error recovery
        result = await complete_loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="error_recovery_processing"
        )

        # Verify error recovery results
        assert isinstance(result, dict)
        assert "successful_count" in result
        assert "failed_count" in result
        assert "success_rate" in result
        
        # Should have some successful and some failed items
        assert result["successful_count"] > 0
        assert result["failed_count"] > 0
        assert result["successful_count"] + result["failed_count"] == len(mixed_data)

    @pytest.mark.asyncio
    async def test_cross_platform_compatibility(self, complete_loop_executor):
        """Test cross-platform compatibility scenarios."""
        # Test data that might behave differently across platforms
        platform_test_data = [
            {"path": "/unix/style/path", "platform": "unix"},
            {"path": "C:\\Windows\\Style\\Path", "platform": "windows"},
            {"encoding": "utf-8", "text": "Hello 世界"},
            {"encoding": "ascii", "text": "Hello World"},
            {"timezone": "UTC", "timestamp": "2024-01-01T12:00:00Z"},
            {"timezone": "EST", "timestamp": "2024-01-01T07:00:00-05:00"}
        ]

        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "list",
                "data": platform_test_data
            },
            "loop_body_transitions": ["data_processing"],
            "concurrency": {
                "enabled": True,
                "max_concurrent": 3
            }
        }

        # Mock platform-aware processing
        async def mock_platform_processing(index, item):
            import os
            import platform
            
            processed_item = item.copy()
            processed_item["processed_on"] = platform.system()
            processed_item["iteration_index"] = index
            
            # Handle path normalization
            if "path" in item:
                processed_item["normalized_path"] = os.path.normpath(item["path"])
            
            # Handle encoding
            if "text" in item and "encoding" in item:
                try:
                    encoded = item["text"].encode(item["encoding"])
                    processed_item["encoding_success"] = True
                    processed_item["byte_length"] = len(encoded)
                except UnicodeEncodeError:
                    processed_item["encoding_success"] = False
                    processed_item["encoding_error"] = "UnicodeEncodeError"
            
            return processed_item
        
        complete_loop_executor.execute_single_iteration = mock_platform_processing

        # Execute cross-platform test
        result = await complete_loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="cross_platform_test"
        )

        # Verify cross-platform results
        assert isinstance(result, list)
        assert len(result) == len(platform_test_data)
        
        # Check platform-specific processing
        for item in result:
            assert "processed_on" in item
            assert item["processed_on"] in ["Windows", "Linux", "Darwin"]  # Common platforms

    @pytest.mark.asyncio
    async def test_backward_compatibility_with_existing_workflows(self, complete_loop_executor):
        """Test backward compatibility with existing workflow patterns."""
        # Simulate existing workflow structure
        legacy_workflow_data = {
            "workflow_id": "legacy_workflow_123",
            "transitions": [
                {"id": "start", "type": "tool", "next": "loop_transition"},
                {"id": "loop_transition", "type": "loop", "next": "end"},
                {"id": "end", "type": "tool"}
            ],
            "data": ["item1", "item2", "item3"]
        }

        # Legacy-style loop configuration
        loop_config = {
            "loop_type": "context_preserving",  # Legacy default
            "aggregation_config": {"type": "list"},  # Legacy default
            "iteration_source": {
                "type": "list",
                "data": legacy_workflow_data["data"]
            },
            "loop_body_transitions": ["data_processing"]
        }

        # Mock legacy-compatible processing
        async def mock_legacy_processing(index, item):
            # Legacy-style result format
            return {
                "item": item,
                "index": index,
                "processed": True,
                "timestamp": "2024-01-01T00:00:00Z"
            }
        
        complete_loop_executor.execute_single_iteration = mock_legacy_processing

        # Execute with legacy compatibility
        result = await complete_loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="legacy_compatibility_test"
        )

        # Verify backward compatibility
        assert isinstance(result, list)
        assert len(result) == 3
        
        # Check legacy result format
        for i, item in enumerate(result):
            assert item["item"] == legacy_workflow_data["data"][i]
            assert item["index"] == i
            assert item["processed"] is True

    @pytest.mark.asyncio
    async def test_user_acceptance_testing_scenario(self, complete_loop_executor):
        """Test comprehensive user acceptance scenario."""
        # Real-world scenario: E-commerce order processing
        orders = [
            {
                "order_id": "ORD-001",
                "customer_id": "CUST-123",
                "items": [
                    {"product_id": "PROD-A", "quantity": 2, "price": 29.99},
                    {"product_id": "PROD-B", "quantity": 1, "price": 49.99}
                ],
                "shipping_address": "123 Main St, City, State",
                "payment_method": "credit_card"
            },
            {
                "order_id": "ORD-002",
                "customer_id": "CUST-456",
                "items": [
                    {"product_id": "PROD-C", "quantity": 3, "price": 19.99}
                ],
                "shipping_address": "456 Oak Ave, Town, State",
                "payment_method": "paypal"
            },
            {
                "order_id": "ORD-003",
                "customer_id": "CUST-789",
                "items": [],  # Invalid: empty items
                "shipping_address": "",  # Invalid: empty address
                "payment_method": "invalid_method"  # Invalid payment method
            }
        ]

        loop_config = {
            "loop_type": "context_preserving",
            "aggregation_config": {
                "type": "custom",
                "custom_function": {
                    "type": "code",
                    "code": """
processed_orders = [r for r in results if r.get('status') == 'processed']
failed_orders = [r for r in results if r.get('status') == 'failed']
total_revenue = sum(order.get('total_amount', 0) for order in processed_orders)

result = {
    'summary': {
        'total_orders': len(results),
        'processed_orders': len(processed_orders),
        'failed_orders': len(failed_orders),
        'success_rate': len(processed_orders) / len(results) if results else 0,
        'total_revenue': total_revenue
    },
    'processed_orders': processed_orders,
    'failed_orders': failed_orders
}
"""
                }
            },
            "iteration_source": {
                "type": "list",
                "data": orders
            },
            "loop_body_transitions": ["validation", "transformation", "data_processing"],
            "termination_conditions": {
                "max_iterations": 100,  # Safety limit
                "timeout_seconds": 30   # Timeout protection
            }
        }

        # Mock comprehensive order processing
        async def mock_order_processing(index, order):
            # Validation
            if not order.get("items") or not order.get("shipping_address"):
                return {
                    "order_id": order["order_id"],
                    "status": "failed",
                    "error": "Invalid order data",
                    "iteration_index": index
                }
            
            # Calculate total
            total_amount = sum(
                item["quantity"] * item["price"] 
                for item in order["items"]
            )
            
            # Process order
            processed_order = {
                "order_id": order["order_id"],
                "customer_id": order["customer_id"],
                "total_amount": total_amount,
                "item_count": sum(item["quantity"] for item in order["items"]),
                "status": "processed",
                "processed_at": "2024-01-01T00:00:00Z",
                "iteration_index": index
            }
            
            return processed_order
        
        complete_loop_executor.execute_single_iteration = mock_order_processing

        # Execute user acceptance test
        result = await complete_loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="user_acceptance_test"
        )

        # Verify user acceptance criteria
        assert isinstance(result, dict)
        assert "summary" in result
        assert "processed_orders" in result
        assert "failed_orders" in result
        
        summary = result["summary"]
        assert summary["total_orders"] == 3
        assert summary["processed_orders"] == 2  # Two valid orders
        assert summary["failed_orders"] == 1     # One invalid order
        assert summary["success_rate"] == 2/3    # 66.7% success rate
        assert summary["total_revenue"] > 0      # Should have revenue from valid orders
        
        # Verify processed orders have required fields
        for order in result["processed_orders"]:
            assert "order_id" in order
            assert "total_amount" in order
            assert order["status"] == "processed"
