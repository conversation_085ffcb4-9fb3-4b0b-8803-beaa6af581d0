import pytest
from app.core_.loop_aggregator import LoopAggregator


class TestLoopAggregator:
    """
    Test suite for LoopAggregator class.
    Tests various aggregation strategies and result processing.
    """

    @pytest.fixture
    def aggregator(self):
        """Create LoopAggregator instance for testing."""
        return LoopAggregator()

    @pytest.fixture
    def sample_results(self):
        """Sample results for testing."""
        return {
            0: {"status": "completed", "output": "Result 0", "value": 10},
            1: {"status": "completed", "output": "Result 1", "value": 20},
            2: {"status": "failed", "output": "Error", "value": 0},
            3: {"status": "completed", "output": "Result 3", "value": 30}
        }

    def test_list_aggregation(self, aggregator, sample_results):
        """Test list aggregation strategy."""
        config = {"type": "list"}
        result = aggregator.aggregate_results(sample_results, config)
        
        assert isinstance(result, list)
        assert len(result) == 4
        assert result[0]["output"] == "Result 0"
        assert result[3]["output"] == "Result 3"

    def test_object_aggregation(self, aggregator, sample_results):
        """Test object aggregation strategy."""
        config = {"type": "object", "metadata": {"test": "data"}}
        result = aggregator.aggregate_results(sample_results, config)
        
        assert isinstance(result, dict)
        assert result["count"] == 4
        assert result["aggregation_type"] == "object"
        assert result["success_count"] == 3
        assert result["failure_count"] == 1
        assert result["success_rate"] == 0.75
        assert result["metadata"]["test"] == "data"
        assert len(result["results"]) == 4

    def test_concatenation_aggregation(self, aggregator):
        """Test concatenation aggregation strategy."""
        results = {0: "Hello", 1: "World", 2: "Test"}
        config = {"type": "concatenate", "separator": " "}
        result = aggregator.aggregate_results(results, config)
        
        assert isinstance(result, str)
        assert result == "Hello World Test"

    def test_concatenation_with_dict_results(self, aggregator, sample_results):
        """Test concatenation with dictionary results."""
        config = {"type": "concatenate", "separator": " | "}
        result = aggregator.aggregate_results(sample_results, config)
        
        assert isinstance(result, str)
        assert "Result 0" in result
        assert "Result 1" in result
        assert " | " in result

    def test_merge_aggregation_overwrite(self, aggregator):
        """Test merge aggregation with overwrite strategy."""
        results = {
            0: {"key1": "value1", "key2": "value2"},
            1: {"key2": "new_value2", "key3": "value3"},
            2: {"key1": "final_value1"}
        }
        config = {"type": "merge", "merge_strategy": "overwrite"}
        result = aggregator.aggregate_results(results, config)
        
        assert isinstance(result, dict)
        assert result["key1"] == "final_value1"  # Last value wins
        assert result["key2"] == "new_value2"
        assert result["key3"] == "value3"

    def test_merge_aggregation_append(self, aggregator):
        """Test merge aggregation with append strategy."""
        results = {
            0: {"items": ["a"], "count": 1},
            1: {"items": ["b", "c"], "count": 2},
            2: {"items": ["d"], "count": 1}
        }
        config = {"type": "merge", "merge_strategy": "append"}
        result = aggregator.aggregate_results(results, config)
        
        assert isinstance(result, dict)
        assert result["items"] == ["a", "b", "c", "d"]
        assert result["count"] == [1, 2, 1]  # Appended as list

    def test_first_result_aggregation(self, aggregator, sample_results):
        """Test first result aggregation."""
        config = {"type": "first"}
        result = aggregator.aggregate_results(sample_results, config)
        
        assert result == sample_results[0]
        assert result["output"] == "Result 0"

    def test_last_result_aggregation(self, aggregator, sample_results):
        """Test last result aggregation."""
        config = {"type": "last"}
        result = aggregator.aggregate_results(sample_results, config)
        
        assert result == sample_results[3]
        assert result["output"] == "Result 3"

    def test_count_aggregation(self, aggregator, sample_results):
        """Test count aggregation."""
        config = {"type": "count"}
        result = aggregator.aggregate_results(sample_results, config)
        
        assert isinstance(result, int)
        assert result == 4

    def test_custom_aggregation(self, aggregator, sample_results):
        """Test custom aggregation function."""
        # Register custom aggregator
        def sum_values(results):
            return sum(r.get("value", 0) for r in results if isinstance(r, dict))
        
        aggregator.register_custom_aggregator("sum_values", sum_values)
        
        config = {"type": "custom", "function_name": "sum_values"}
        result = aggregator.aggregate_results(sample_results, config)
        
        assert isinstance(result, int)
        assert result == 60  # 10 + 20 + 0 + 30

    def test_empty_results(self, aggregator):
        """Test aggregation with empty results."""
        config = {"type": "list"}
        result = aggregator.aggregate_results({}, config)
        
        assert result == []

    def test_order_preservation(self, aggregator):
        """Test that results maintain order."""
        results = {2: "second", 0: "first", 1: "middle", 3: "last"}
        config = {"type": "list"}
        result = aggregator.aggregate_results(results, config, preserve_order=True)
        
        assert result == ["first", "middle", "second", "last"]

    def test_order_not_preserved(self, aggregator):
        """Test aggregation without order preservation."""
        results = {2: "second", 0: "first", 1: "middle"}
        config = {"type": "list"}
        result = aggregator.aggregate_results(results, config, preserve_order=False)
        
        # Should contain all values but order may vary
        assert len(result) == 3
        assert "first" in result
        assert "middle" in result
        assert "second" in result

    def test_config_validation_valid(self, aggregator):
        """Test validation of valid aggregation configs."""
        valid_configs = [
            {"type": "list"},
            {"type": "object", "metadata": {}},
            {"type": "concatenate", "separator": ","},
            {"type": "merge", "merge_strategy": "append"},
            {"type": "first"},
            {"type": "last"},
            {"type": "count"}
        ]
        
        for config in valid_configs:
            assert aggregator.validate_aggregation_config(config) is True

    def test_config_validation_invalid(self, aggregator):
        """Test validation of invalid aggregation configs."""
        invalid_configs = [
            {},  # Missing type
            {"type": "invalid"},  # Invalid type
            {"type": "custom"},  # Missing function_name
            {"type": "custom", "function_name": "nonexistent"},  # Unregistered function
            "not_a_dict"  # Not a dictionary
        ]
        
        for config in invalid_configs:
            assert aggregator.validate_aggregation_config(config) is False

    def test_custom_aggregator_registration(self, aggregator):
        """Test custom aggregator registration."""
        def test_func(results):
            return len(results)
        
        aggregator.register_custom_aggregator("test_func", test_func)
        assert "test_func" in aggregator.custom_aggregators
        assert aggregator.custom_aggregators["test_func"] == test_func

    def test_aggregation_summary(self, aggregator, sample_results):
        """Test aggregation summary generation."""
        summary = aggregator.get_aggregation_summary(sample_results)
        
        assert summary["total_results"] == 4
        assert summary["indices"] == [0, 1, 2, 3]
        assert summary["index_range"] == "0-3"
        assert summary["has_gaps"] is False
        assert "dict" in summary["result_types"]
        assert summary["result_types"]["dict"] == 4

    def test_aggregation_summary_with_gaps(self, aggregator):
        """Test aggregation summary with gaps in indices."""
        results = {0: "first", 2: "third", 5: "sixth"}
        summary = aggregator.get_aggregation_summary(results)
        
        assert summary["total_results"] == 3
        assert summary["indices"] == [0, 2, 5]
        assert summary["index_range"] == "0-5"
        assert summary["has_gaps"] is True

    def test_aggregation_summary_empty(self, aggregator):
        """Test aggregation summary with empty results."""
        summary = aggregator.get_aggregation_summary({})
        
        assert summary["total_results"] == 0
        assert summary["indices"] == []
        assert summary["result_types"] == {}

    def test_unknown_aggregation_type_fallback(self, aggregator, sample_results):
        """Test fallback to list aggregation for unknown types."""
        config = {"type": "unknown_type"}
        result = aggregator.aggregate_results(sample_results, config)
        
        # Should fallback to list aggregation
        assert isinstance(result, list)
        assert len(result) == 4

    def test_custom_aggregation_error_handling(self, aggregator, sample_results):
        """Test error handling in custom aggregation."""
        def failing_func(results):
            raise ValueError("Test error")
        
        aggregator.register_custom_aggregator("failing_func", failing_func)
        config = {"type": "custom", "function_name": "failing_func"}
        
        with pytest.raises(ValueError, match="Test error"):
            aggregator.aggregate_results(sample_results, config)

    def test_list_aggregation_with_filter(self, aggregator, sample_results):
        """Test list aggregation with filtering."""
        def success_filter(result):
            return isinstance(result, dict) and result.get("status") == "completed"
        
        config = {"type": "list", "filter": success_filter}
        result = aggregator.aggregate_results(sample_results, config)
        
        assert len(result) == 3  # Only successful results
        for r in result:
            assert r["status"] == "completed"
