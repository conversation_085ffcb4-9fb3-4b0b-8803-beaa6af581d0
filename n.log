  "transition_id": "transition-ConditionalNode-*************",
  "result": {
    "routing_decision": {
      "target_transitions": [
        "transition-MergeDataComponent-*************"
      ],
      "matched_conditions": [],
      "condition_result": false,
      "execution_time_ms": 0.****************
    },
    "metadata": {
      "total_conditions": 1,
      "total_matches": 0,
      "evaluation_strategy": "all_matches"
    },
    "input_data": {}
  },
  "error": null
}
2025-07-14 19:49:10 - ComponentSystem - INFO - [_send_result:1121] [ReqID:9914287c-441d-4a1c-b818-e00a8aced724] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Sent result for component ApiRequestNode to topic node_results for RequestID=9914287c-441d-4a1c-b818-e00a8aced724
2025-07-14 19:49:10 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=9914287c-441d-4a1c-b818-e00a8aced724
2025-07-14 19:49:10 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:9914287c-441d-4a1c-b818-e00a8aced724] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Successfully committed offset 1179 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1178-1752502749.835156
2025-07-14 19:49:10 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1179 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1178-1752502749.835156
2025-07-14 19:49:10 - ComponentSystem - INFO - [_process_message:936] [ReqID:9914287c-441d-4a1c-b818-e00a8aced724] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1178, TaskID=ApiRequestNode-node-execution-request-0-1178-1752502749.835156
2025-07-14 19:49:10 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1178, TaskID=ApiRequestNode-node-execution-request-0-1178-1752502749.835156
2025-07-14 19:49:12 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1179, TaskID=ApiRequestNode-node-execution-request-0-1179-1752502752.7405078
2025-07-14 19:49:12 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1179, TaskID=ApiRequestNode-node-execution-request-0-1179-1752502752.7405078
2025-07-14 19:49:12 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1179-1752502752.7405078, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "0",
    "merge_strategy": "Structured Compose",
    "output_key_1": "hello"
  },
  "request_id": "b064df3f-f357-469c-b03a-866fbc7c25f9",
  "correlation_id": "52603d97-9b08-43f5-af03-5b6dc5c669c3",
  "transition_id": "transition-MergeDataComponent-*************",
  "node_label": "Merge Data"
}
2025-07-14 19:49:12 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1179-1752502752.7405078, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "0",
    "merge_strategy": "Structured Compose",
    "output_key_1": "hello"
  },
  "request_id": "b064df3f-f357-469c-b03a-866fbc7c25f9",
  "correlation_id": "52603d97-9b08-43f5-af03-5b6dc5c669c3",
  "transition_id": "transition-MergeDataComponent-*************",
  "node_label": "Merge Data"
}
2025-07-14 19:49:12 - ComponentSystem - INFO - [_process_message:713] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Executing tool MergeDataComponent for RequestID=b064df3f-f357-469c-b03a-866fbc7c25f9, TaskID=ApiRequestNode-node-execution-request-0-1179-1752502752.7405078
2025-07-14 19:49:12 - ComponentSystem - INFO - [_process_message] Executing tool MergeDataComponent for RequestID=b064df3f-f357-469c-b03a-866fbc7c25f9, TaskID=ApiRequestNode-node-execution-request-0-1179-1752502752.7405078
2025-07-14 19:49:12 - ToolExecutor - INFO - [execute_tool:94] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Executing tool for request_id: b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - ToolExecutor - INFO - [execute_tool:97] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "0",
    "merge_strategy": "Structured Compose",
    "output_key_1": "hello"
  },
  "request_id": "b064df3f-f357-469c-b03a-866fbc7c25f9",
  "correlation_id": "52603d97-9b08-43f5-af03-5b6dc5c669c3",
  "transition_id": "transition-MergeDataComponent-*************",
  "node_label": "Merge Data"
}
2025-07-14 19:49:12 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "0",
    "merge_strategy": "Structured Compose",
    "output_key_1": "hello"
  },
  "request_id": "b064df3f-f357-469c-b03a-866fbc7c25f9",
  "correlation_id": "52603d97-9b08-43f5-af03-5b6dc5c669c3",
  "transition_id": "transition-MergeDataComponent-*************",
  "node_label": "Merge Data"
}
2025-07-14 19:49:12 - ToolExecutor - INFO - [execute_tool:111] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Tool name: MergeDataComponent for request_id: b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - ToolExecutor - INFO - [execute_tool] Tool name: MergeDataComponent for request_id: b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - ToolExecutor - INFO - [execute_tool:144] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Processing payload with component MergeDataComponent for request_id: b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - ToolExecutor - INFO - [execute_tool] Processing payload with component MergeDataComponent for request_id: b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - MergeDataComponent - INFO - [process:278] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Processing merge data request for request_id: b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - MergeDataComponent - INFO - [process] Processing merge data request for request_id: b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - MergeDataComponent - INFO - [process:280] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'output_key_1', 'request_id']
2025-07-14 19:49:12 - MergeDataComponent - INFO - [process] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'output_key_1', 'request_id']
2025-07-14 19:49:12 - MergeDataComponent - INFO - [process:294] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'output_key_1', 'request_id']
2025-07-14 19:49:12 - MergeDataComponent - INFO - [process] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'output_key_1', 'request_id']
2025-07-14 19:49:12 - MergeDataComponent - INFO - [process:314] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Merging data for request_id b064df3f-f357-469c-b03a-866fbc7c25f9. Strategy: 'Structured Compose', Num additional inputs: 0
2025-07-14 19:49:12 - MergeDataComponent - INFO - [process] Merging data for request_id b064df3f-f357-469c-b03a-866fbc7c25f9. Strategy: 'Structured Compose', Num additional inputs: 0
2025-07-14 19:49:12 - MergeDataComponent - INFO - [process:325] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Added main input as 'hello' for request_id b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - MergeDataComponent - INFO - [process] Added main input as 'hello' for request_id b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - MergeDataComponent - INFO - [process:340] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Structured compose completed for request_id b064df3f-f357-469c-b03a-866fbc7c25f9 with keys: ['hello']
2025-07-14 19:49:12 - MergeDataComponent - INFO - [process] Structured compose completed for request_id b064df3f-f357-469c-b03a-866fbc7c25f9 with keys: ['hello']
2025-07-14 19:49:12 - ToolExecutor - INFO - [execute_tool:148] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Component MergeDataComponent processed payload successfully for request_id: b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - ToolExecutor - INFO - [execute_tool] Component MergeDataComponent processed payload successfully for request_id: b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - ToolExecutor - INFO - [execute_tool:154] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] ToolExecutor returning raw component result for request_id: b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - ComponentSystem - INFO - [_process_message:717] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Tool MergeDataComponent executed successfully for RequestID=b064df3f-f357-469c-b03a-866fbc7c25f9, TaskID=ApiRequestNode-node-execution-request-0-1179-1752502752.7405078
2025-07-14 19:49:12 - ComponentSystem - INFO - [_process_message] Tool MergeDataComponent executed successfully for RequestID=b064df3f-f357-469c-b03a-866fbc7c25f9, TaskID=ApiRequestNode-node-execution-request-0-1179-1752502752.7405078
2025-07-14 19:49:12 - ComponentSystem - INFO - [_send_result:1007] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Preparing to send result for component ApiRequestNode, RequestID=b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:12 - ComponentSystem - INFO - [_send_result:1105] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Sending Kafka response: RequestID=b064df3f-f357-469c-b03a-866fbc7c25f9, Response={
  "request_id": "b064df3f-f357-469c-b03a-866fbc7c25f9",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1752502752.745582,
  "transition_id": "transition-MergeDataComponent-*************",
  "result": {
    "hello": {}
  },
  "error": null
}
2025-07-14 19:49:12 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=b064df3f-f357-469c-b03a-866fbc7c25f9, Response={
  "request_id": "b064df3f-f357-469c-b03a-866fbc7c25f9",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1752502752.745582,
  "transition_id": "transition-MergeDataComponent-*************",
  "result": {
    "hello": {}
  },
  "error": null
}
2025-07-14 19:49:13 - ComponentSystem - INFO - [_send_result:1121] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Sent result for component ApiRequestNode to topic node_results for RequestID=b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:13 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=b064df3f-f357-469c-b03a-866fbc7c25f9
2025-07-14 19:49:13 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Successfully committed offset 1180 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1179-1752502752.7405078
2025-07-14 19:49:13 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1180 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1179-1752502752.7405078
2025-07-14 19:49:13 - ComponentSystem - INFO - [_process_message:936] [ReqID:b064df3f-f357-469c-b03a-866fbc7c25f9] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1179, TaskID=ApiRequestNode-node-execution-request-0-1179-1752502752.7405078
2025-07-14 19:49:13 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1179, TaskID=ApiRequestNode-node-execution-request-0-1179-1752502752.7405078
2025-07-14 19:49:15 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1180, TaskID=ApiRequestNode-node-execution-request-0-1180-1752502755.636488
2025-07-14 19:49:15 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1180, TaskID=ApiRequestNode-node-execution-request-0-1180-1752502755.636488
2025-07-14 19:49:15 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1180-1752502755.636488, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "hello": {}
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Overwrite",
    "input_1": {
      "true": "true"
    }
  },
  "request_id": "fdcc7f32-4dad-4b71-b829-21e1a7f83ce1",
  "correlation_id": "52603d97-9b08-43f5-af03-5b6dc5c669c3",
  "transition_id": "transition-MergeDataComponent-1752495367072",
  "node_label": "Merge Data"
}
2025-07-14 19:49:15 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1180-1752502755.636488, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "hello": {}
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Overwrite",
    "input_1": {
      "true": "true"
    }
  },
  "request_id": "fdcc7f32-4dad-4b71-b829-21e1a7f83ce1",
  "correlation_id": "52603d97-9b08-43f5-af03-5b6dc5c669c3",
  "transition_id": "transition-MergeDataComponent-1752495367072",
  "node_label": "Merge Data"
}
2025-07-14 19:49:15 - ComponentSystem - INFO - [_process_message:713] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Executing tool MergeDataComponent for RequestID=fdcc7f32-4dad-4b71-b829-21e1a7f83ce1, TaskID=ApiRequestNode-node-execution-request-0-1180-1752502755.636488
2025-07-14 19:49:15 - ComponentSystem - INFO - [_process_message] Executing tool MergeDataComponent for RequestID=fdcc7f32-4dad-4b71-b829-21e1a7f83ce1, TaskID=ApiRequestNode-node-execution-request-0-1180-1752502755.636488
2025-07-14 19:49:15 - ToolExecutor - INFO - [execute_tool:94] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Executing tool for request_id: fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ToolExecutor - INFO - [execute_tool:97] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "hello": {}
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Overwrite",
    "input_1": {
      "true": "true"
    }
  },
  "request_id": "fdcc7f32-4dad-4b71-b829-21e1a7f83ce1",
  "correlation_id": "52603d97-9b08-43f5-af03-5b6dc5c669c3",
  "transition_id": "transition-MergeDataComponent-1752495367072",
  "node_label": "Merge Data"
}
2025-07-14 19:49:15 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "hello": {}
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Overwrite",
    "input_1": {
      "true": "true"
    }
  },
  "request_id": "fdcc7f32-4dad-4b71-b829-21e1a7f83ce1",
  "correlation_id": "52603d97-9b08-43f5-af03-5b6dc5c669c3",
  "transition_id": "transition-MergeDataComponent-1752495367072",
  "node_label": "Merge Data"
}
2025-07-14 19:49:15 - ToolExecutor - INFO - [execute_tool:111] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Tool name: MergeDataComponent for request_id: fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ToolExecutor - INFO - [execute_tool] Tool name: MergeDataComponent for request_id: fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ToolExecutor - INFO - [execute_tool:144] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Processing payload with component MergeDataComponent for request_id: fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ToolExecutor - INFO - [execute_tool] Processing payload with component MergeDataComponent for request_id: fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - MergeDataComponent - INFO - [process:278] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Processing merge data request for request_id: fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - MergeDataComponent - INFO - [process] Processing merge data request for request_id: fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - MergeDataComponent - INFO - [process:280] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'request_id']
2025-07-14 19:49:15 - MergeDataComponent - INFO - [process] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'request_id']
2025-07-14 19:49:15 - MergeDataComponent - INFO - [process:294] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'request_id']
2025-07-14 19:49:15 - MergeDataComponent - INFO - [process] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'request_id']
2025-07-14 19:49:15 - MergeDataComponent - INFO - [process:314] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Merging data for request_id fdcc7f32-4dad-4b71-b829-21e1a7f83ce1. Strategy: 'Overwrite', Num additional inputs: 1
2025-07-14 19:49:15 - MergeDataComponent - INFO - [process] Merging data for request_id fdcc7f32-4dad-4b71-b829-21e1a7f83ce1. Strategy: 'Overwrite', Num additional inputs: 1
2025-07-14 19:49:15 - MergeDataComponent - INFO - [process:400] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Dictionary input_1 merged with overwrite strategy for request_id fdcc7f32-4dad-4b71-b829-21e1a7f83ce1. Current keys: ['hello', 'true']
2025-07-14 19:49:15 - MergeDataComponent - INFO - [process] Dictionary input_1 merged with overwrite strategy for request_id fdcc7f32-4dad-4b71-b829-21e1a7f83ce1. Current keys: ['hello', 'true']
2025-07-14 19:49:15 - MergeDataComponent - INFO - [process:453] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] All data merged successfully for request_id fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - MergeDataComponent - INFO - [process] All data merged successfully for request_id fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ToolExecutor - INFO - [execute_tool:148] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Component MergeDataComponent processed payload successfully for request_id: fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ToolExecutor - INFO - [execute_tool] Component MergeDataComponent processed payload successfully for request_id: fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ToolExecutor - INFO - [execute_tool:154] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] ToolExecutor returning raw component result for request_id: fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ComponentSystem - INFO - [_process_message:717] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Tool MergeDataComponent executed successfully for RequestID=fdcc7f32-4dad-4b71-b829-21e1a7f83ce1, TaskID=ApiRequestNode-node-execution-request-0-1180-1752502755.636488
2025-07-14 19:49:15 - ComponentSystem - INFO - [_process_message] Tool MergeDataComponent executed successfully for RequestID=fdcc7f32-4dad-4b71-b829-21e1a7f83ce1, TaskID=ApiRequestNode-node-execution-request-0-1180-1752502755.636488
2025-07-14 19:49:15 - ComponentSystem - INFO - [_send_result:1007] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Preparing to send result for component ApiRequestNode, RequestID=fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ComponentSystem - INFO - [_send_result:1105] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Sending Kafka response: RequestID=fdcc7f32-4dad-4b71-b829-21e1a7f83ce1, Response={
  "request_id": "fdcc7f32-4dad-4b71-b829-21e1a7f83ce1",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1752502755.640331,
  "transition_id": "transition-MergeDataComponent-1752495367072",
  "result": {
    "hello": {},
    "true": "true"
  },
  "error": null
}
2025-07-14 19:49:15 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=fdcc7f32-4dad-4b71-b829-21e1a7f83ce1, Response={
  "request_id": "fdcc7f32-4dad-4b71-b829-21e1a7f83ce1",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1752502755.640331,
  "transition_id": "transition-MergeDataComponent-1752495367072",
  "result": {
    "hello": {},
    "true": "true"
  },
  "error": null
}
2025-07-14 19:49:15 - ComponentSystem - INFO - [_send_result:1121] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Sent result for component ApiRequestNode to topic node_results for RequestID=fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:15 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=fdcc7f32-4dad-4b71-b829-21e1a7f83ce1
2025-07-14 19:49:16 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Successfully committed offset 1181 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1180-1752502755.636488
2025-07-14 19:49:16 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1181 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1180-1752502755.636488
2025-07-14 19:49:16 - ComponentSystem - INFO - [_process_message:936] [ReqID:fdcc7f32-4dad-4b71-b829-21e1a7f83ce1] [CorrID:52603d97-9b08-43f5-af03-5b6dc5c669c3] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1180, TaskID=ApiRequestNode-node-execution-request-0-1180-1752502755.636488
2025-07-14 19:49:16 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1180, TaskID=ApiRequestNode-node-execution-request-0-1180-1752502755.636488
2025-07-14 19:51:57 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1181, TaskID=ApiRequestNode-node-execution-request-0-1181-**********.2647302
2025-07-14 19:51:57 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1181, TaskID=ApiRequestNode-node-execution-request-0-1181-**********.2647302
2025-07-14 19:51:57 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1181-**********.2647302, Payload={
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "source": "node_output",
        "operator": "equals",
        "expected_value": "hello",
        "next_transition": "transition-ConditionalNode-1752495520951",
        "ends_at": [
          "transition-CombineTextComponent-1752495356019",
          "transition-MergeDataComponent-1752495557267"
        ]
      }
    ],
    "default_transition": "transition-MergeDataComponent-*************",
    "default_ends_at": [
      "transition-MergeDataComponent-1752495367072"
    ],
    "evaluation_strategy": "all_matches",
    "node_output": {},
    "global_context": {}
  },
  "request_id": "0640baee-0764-4540-98ee-f85671133f44",
  "correlation_id": "25c39fe0-379e-41aa-a491-dd5faf79327d",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-14 19:51:57 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1181-**********.2647302, Payload={
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "source": "node_output",
        "operator": "equals",
        "expected_value": "hello",
        "next_transition": "transition-ConditionalNode-1752495520951",
        "ends_at": [
          "transition-CombineTextComponent-1752495356019",
          "transition-MergeDataComponent-1752495557267"
        ]
      }
    ],
    "default_transition": "transition-MergeDataComponent-*************",
    "default_ends_at": [
      "transition-MergeDataComponent-1752495367072"
    ],
    "evaluation_strategy": "all_matches",
    "node_output": {},
    "global_context": {}
  },
  "request_id": "0640baee-0764-4540-98ee-f85671133f44",
  "correlation_id": "25c39fe0-379e-41aa-a491-dd5faf79327d",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-14 19:51:57 - ComponentSystem - INFO - [_process_message:713] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Executing tool conditional for RequestID=0640baee-0764-4540-98ee-f85671133f44, TaskID=ApiRequestNode-node-execution-request-0-1181-**********.2647302
2025-07-14 19:51:57 - ComponentSystem - INFO - [_process_message] Executing tool conditional for RequestID=0640baee-0764-4540-98ee-f85671133f44, TaskID=ApiRequestNode-node-execution-request-0-1181-**********.2647302
2025-07-14 19:51:57 - ToolExecutor - INFO - [execute_tool:94] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Executing tool for request_id: 0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: 0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ToolExecutor - INFO - [execute_tool:97] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] ToolExecutor received payload: {
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "source": "node_output",
        "operator": "equals",
        "expected_value": "hello",
        "next_transition": "transition-ConditionalNode-1752495520951",
        "ends_at": [
          "transition-CombineTextComponent-1752495356019",
          "transition-MergeDataComponent-1752495557267"
        ]
      }
    ],
    "default_transition": "transition-MergeDataComponent-*************",
    "default_ends_at": [
      "transition-MergeDataComponent-1752495367072"
    ],
    "evaluation_strategy": "all_matches",
    "node_output": {},
    "global_context": {}
  },
  "request_id": "0640baee-0764-4540-98ee-f85671133f44",
  "correlation_id": "25c39fe0-379e-41aa-a491-dd5faf79327d",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-14 19:51:57 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "source": "node_output",
        "operator": "equals",
        "expected_value": "hello",
        "next_transition": "transition-ConditionalNode-1752495520951",
        "ends_at": [
          "transition-CombineTextComponent-1752495356019",
          "transition-MergeDataComponent-1752495557267"
        ]
      }
    ],
    "default_transition": "transition-MergeDataComponent-*************",
    "default_ends_at": [
      "transition-MergeDataComponent-1752495367072"
    ],
    "evaluation_strategy": "all_matches",
    "node_output": {},
    "global_context": {}
  },
  "request_id": "0640baee-0764-4540-98ee-f85671133f44",
  "correlation_id": "25c39fe0-379e-41aa-a491-dd5faf79327d",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-14 19:51:57 - ToolExecutor - INFO - [execute_tool:111] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Tool name: conditional for request_id: 0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ToolExecutor - INFO - [execute_tool] Tool name: conditional for request_id: 0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ToolExecutor - INFO - [execute_tool:144] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Processing payload with component conditional for request_id: 0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ToolExecutor - INFO - [execute_tool] Processing payload with component conditional for request_id: 0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - app.components.conditional_component - INFO - [process] Processing conditional routing for request_id: 0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - app.components.conditional_component - INFO - [_evaluate_all_matches] No conditions matched, using default: transition-MergeDataComponent-*************
2025-07-14 19:51:57 - ToolExecutor - INFO - [execute_tool:148] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Component conditional processed payload successfully for request_id: 0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ToolExecutor - INFO - [execute_tool] Component conditional processed payload successfully for request_id: 0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ToolExecutor - INFO - [execute_tool:154] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] ToolExecutor returning raw component result for request_id: 0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: 0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ComponentSystem - INFO - [_process_message:717] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Tool conditional executed successfully for RequestID=0640baee-0764-4540-98ee-f85671133f44, TaskID=ApiRequestNode-node-execution-request-0-1181-**********.2647302
2025-07-14 19:51:57 - ComponentSystem - INFO - [_process_message] Tool conditional executed successfully for RequestID=0640baee-0764-4540-98ee-f85671133f44, TaskID=ApiRequestNode-node-execution-request-0-1181-**********.2647302
2025-07-14 19:51:57 - ComponentSystem - INFO - [_send_result:1007] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Preparing to send result for component ApiRequestNode, RequestID=0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ComponentSystem - INFO - [_send_result:1105] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Sending Kafka response: RequestID=0640baee-0764-4540-98ee-f85671133f44, Response={
  "request_id": "0640baee-0764-4540-98ee-f85671133f44",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.271198,
  "transition_id": "transition-ConditionalNode-*************",
  "result": {
    "routing_decision": {
      "target_transitions": [
        "transition-MergeDataComponent-*************"
      ],
      "matched_conditions": [],
      "condition_result": false,
      "execution_time_ms": 0.************
    },
    "metadata": {
      "total_conditions": 1,
      "total_matches": 0,
      "evaluation_strategy": "all_matches"
    },
    "input_data": {}
  },
  "error": null
}
2025-07-14 19:51:57 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=0640baee-0764-4540-98ee-f85671133f44, Response={
  "request_id": "0640baee-0764-4540-98ee-f85671133f44",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.271198,
  "transition_id": "transition-ConditionalNode-*************",
  "result": {
    "routing_decision": {
      "target_transitions": [
        "transition-MergeDataComponent-*************"
      ],
      "matched_conditions": [],
      "condition_result": false,
      "execution_time_ms": 0.************
    },
    "metadata": {
      "total_conditions": 1,
      "total_matches": 0,
      "evaluation_strategy": "all_matches"
    },
    "input_data": {}
  },
  "error": null
}
2025-07-14 19:51:57 - ComponentSystem - INFO - [_send_result:1121] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Sent result for component ApiRequestNode to topic node_results for RequestID=0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=0640baee-0764-4540-98ee-f85671133f44
2025-07-14 19:51:57 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Successfully committed offset 1182 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1181-**********.2647302
2025-07-14 19:51:57 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1182 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1181-**********.2647302
2025-07-14 19:51:57 - ComponentSystem - INFO - [_process_message:936] [ReqID:0640baee-0764-4540-98ee-f85671133f44] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1181, TaskID=ApiRequestNode-node-execution-request-0-1181-**********.2647302
2025-07-14 19:51:57 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1181, TaskID=ApiRequestNode-node-execution-request-0-1181-**********.2647302
2025-07-14 19:52:00 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1182, TaskID=ApiRequestNode-node-execution-request-0-1182-1752502920.185478
2025-07-14 19:52:00 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1182, TaskID=ApiRequestNode-node-execution-request-0-1182-1752502920.185478
2025-07-14 19:52:00 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1182-1752502920.185478, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "0",
    "merge_strategy": "Structured Compose",
    "output_key_1": "hello"
  },
  "request_id": "58c70f5f-8d1f-417c-b4d5-13622d6254b2",
  "correlation_id": "25c39fe0-379e-41aa-a491-dd5faf79327d",
  "transition_id": "transition-MergeDataComponent-*************",
  "node_label": "Merge Data"
}
2025-07-14 19:52:00 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1182-1752502920.185478, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "0",
    "merge_strategy": "Structured Compose",
    "output_key_1": "hello"
  },
  "request_id": "58c70f5f-8d1f-417c-b4d5-13622d6254b2",
  "correlation_id": "25c39fe0-379e-41aa-a491-dd5faf79327d",
  "transition_id": "transition-MergeDataComponent-*************",
  "node_label": "Merge Data"
}
2025-07-14 19:52:00 - ComponentSystem - INFO - [_process_message:713] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Executing tool MergeDataComponent for RequestID=58c70f5f-8d1f-417c-b4d5-13622d6254b2, TaskID=ApiRequestNode-node-execution-request-0-1182-1752502920.185478
2025-07-14 19:52:00 - ComponentSystem - INFO - [_process_message] Executing tool MergeDataComponent for RequestID=58c70f5f-8d1f-417c-b4d5-13622d6254b2, TaskID=ApiRequestNode-node-execution-request-0-1182-1752502920.185478
2025-07-14 19:52:00 - ToolExecutor - INFO - [execute_tool:94] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Executing tool for request_id: 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ToolExecutor - INFO - [execute_tool:97] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "0",
    "merge_strategy": "Structured Compose",
    "output_key_1": "hello"
  },
  "request_id": "58c70f5f-8d1f-417c-b4d5-13622d6254b2",
  "correlation_id": "25c39fe0-379e-41aa-a491-dd5faf79327d",
  "transition_id": "transition-MergeDataComponent-*************",
  "node_label": "Merge Data"
}
2025-07-14 19:52:00 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "0",
    "merge_strategy": "Structured Compose",
    "output_key_1": "hello"
  },
  "request_id": "58c70f5f-8d1f-417c-b4d5-13622d6254b2",
  "correlation_id": "25c39fe0-379e-41aa-a491-dd5faf79327d",
  "transition_id": "transition-MergeDataComponent-*************",
  "node_label": "Merge Data"
}
2025-07-14 19:52:00 - ToolExecutor - INFO - [execute_tool:111] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Tool name: MergeDataComponent for request_id: 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ToolExecutor - INFO - [execute_tool] Tool name: MergeDataComponent for request_id: 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ToolExecutor - INFO - [execute_tool:144] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Processing payload with component MergeDataComponent for request_id: 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ToolExecutor - INFO - [execute_tool] Processing payload with component MergeDataComponent for request_id: 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - MergeDataComponent - INFO - [process:278] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Processing merge data request for request_id: 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - MergeDataComponent - INFO - [process] Processing merge data request for request_id: 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - MergeDataComponent - INFO - [process:280] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'output_key_1', 'request_id']
2025-07-14 19:52:00 - MergeDataComponent - INFO - [process] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'output_key_1', 'request_id']
2025-07-14 19:52:00 - MergeDataComponent - INFO - [process:294] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'output_key_1', 'request_id']
2025-07-14 19:52:00 - MergeDataComponent - INFO - [process] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'output_key_1', 'request_id']
2025-07-14 19:52:00 - MergeDataComponent - INFO - [process:314] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Merging data for request_id 58c70f5f-8d1f-417c-b4d5-13622d6254b2. Strategy: 'Structured Compose', Num additional inputs: 0
2025-07-14 19:52:00 - MergeDataComponent - INFO - [process] Merging data for request_id 58c70f5f-8d1f-417c-b4d5-13622d6254b2. Strategy: 'Structured Compose', Num additional inputs: 0
2025-07-14 19:52:00 - MergeDataComponent - INFO - [process:325] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Added main input as 'hello' for request_id 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - MergeDataComponent - INFO - [process] Added main input as 'hello' for request_id 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - MergeDataComponent - INFO - [process:340] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Structured compose completed for request_id 58c70f5f-8d1f-417c-b4d5-13622d6254b2 with keys: ['hello']
2025-07-14 19:52:00 - MergeDataComponent - INFO - [process] Structured compose completed for request_id 58c70f5f-8d1f-417c-b4d5-13622d6254b2 with keys: ['hello']
2025-07-14 19:52:00 - ToolExecutor - INFO - [execute_tool:148] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Component MergeDataComponent processed payload successfully for request_id: 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ToolExecutor - INFO - [execute_tool] Component MergeDataComponent processed payload successfully for request_id: 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ToolExecutor - INFO - [execute_tool:154] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] ToolExecutor returning raw component result for request_id: 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: 58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ComponentSystem - INFO - [_process_message:717] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Tool MergeDataComponent executed successfully for RequestID=58c70f5f-8d1f-417c-b4d5-13622d6254b2, TaskID=ApiRequestNode-node-execution-request-0-1182-1752502920.185478
2025-07-14 19:52:00 - ComponentSystem - INFO - [_process_message] Tool MergeDataComponent executed successfully for RequestID=58c70f5f-8d1f-417c-b4d5-13622d6254b2, TaskID=ApiRequestNode-node-execution-request-0-1182-1752502920.185478
2025-07-14 19:52:00 - ComponentSystem - INFO - [_send_result:1007] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Preparing to send result for component ApiRequestNode, RequestID=58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ComponentSystem - INFO - [_send_result:1105] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Sending Kafka response: RequestID=58c70f5f-8d1f-417c-b4d5-13622d6254b2, Response={
  "request_id": "58c70f5f-8d1f-417c-b4d5-13622d6254b2",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1752502920.188168,
  "transition_id": "transition-MergeDataComponent-*************",
  "result": {
    "hello": {}
  },
  "error": null
}
2025-07-14 19:52:00 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=58c70f5f-8d1f-417c-b4d5-13622d6254b2, Response={
  "request_id": "58c70f5f-8d1f-417c-b4d5-13622d6254b2",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1752502920.188168,
  "transition_id": "transition-MergeDataComponent-*************",
  "result": {
    "hello": {}
  },
  "error": null
}
2025-07-14 19:52:00 - ComponentSystem - INFO - [_send_result:1121] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Sent result for component ApiRequestNode to topic node_results for RequestID=58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=58c70f5f-8d1f-417c-b4d5-13622d6254b2
2025-07-14 19:52:00 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Successfully committed offset 1183 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1182-1752502920.185478
2025-07-14 19:52:00 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1183 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1182-1752502920.185478
2025-07-14 19:52:00 - ComponentSystem - INFO - [_process_message:936] [ReqID:58c70f5f-8d1f-417c-b4d5-13622d6254b2] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1182, TaskID=ApiRequestNode-node-execution-request-0-1182-1752502920.185478
2025-07-14 19:52:00 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1182, TaskID=ApiRequestNode-node-execution-request-0-1182-1752502920.185478
2025-07-14 19:52:03 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1183, TaskID=ApiRequestNode-node-execution-request-0-1183-1752502923.084336
2025-07-14 19:52:03 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1183, TaskID=ApiRequestNode-node-execution-request-0-1183-1752502923.084336
2025-07-14 19:52:03 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1183-1752502923.084336, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "hello": {}
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Overwrite",
    "input_1": {
      "true": "true"
    }
  },
  "request_id": "30cd2427-8b3a-4583-8276-5daeba45de08",
  "correlation_id": "25c39fe0-379e-41aa-a491-dd5faf79327d",
  "transition_id": "transition-MergeDataComponent-1752495367072",
  "node_label": "Merge Data"
}
2025-07-14 19:52:03 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1183-1752502923.084336, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "hello": {}
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Overwrite",
    "input_1": {
      "true": "true"
    }
  },
  "request_id": "30cd2427-8b3a-4583-8276-5daeba45de08",
  "correlation_id": "25c39fe0-379e-41aa-a491-dd5faf79327d",
  "transition_id": "transition-MergeDataComponent-1752495367072",
  "node_label": "Merge Data"
}
2025-07-14 19:52:03 - ComponentSystem - INFO - [_process_message:713] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Executing tool MergeDataComponent for RequestID=30cd2427-8b3a-4583-8276-5daeba45de08, TaskID=ApiRequestNode-node-execution-request-0-1183-1752502923.084336
2025-07-14 19:52:03 - ComponentSystem - INFO - [_process_message] Executing tool MergeDataComponent for RequestID=30cd2427-8b3a-4583-8276-5daeba45de08, TaskID=ApiRequestNode-node-execution-request-0-1183-1752502923.084336
2025-07-14 19:52:03 - ToolExecutor - INFO - [execute_tool:94] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Executing tool for request_id: 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ToolExecutor - INFO - [execute_tool:97] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "hello": {}
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Overwrite",
    "input_1": {
      "true": "true"
    }
  },
  "request_id": "30cd2427-8b3a-4583-8276-5daeba45de08",
  "correlation_id": "25c39fe0-379e-41aa-a491-dd5faf79327d",
  "transition_id": "transition-MergeDataComponent-1752495367072",
  "node_label": "Merge Data"
}
2025-07-14 19:52:03 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "hello": {}
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Overwrite",
    "input_1": {
      "true": "true"
    }
  },
  "request_id": "30cd2427-8b3a-4583-8276-5daeba45de08",
  "correlation_id": "25c39fe0-379e-41aa-a491-dd5faf79327d",
  "transition_id": "transition-MergeDataComponent-1752495367072",
  "node_label": "Merge Data"
}
2025-07-14 19:52:03 - ToolExecutor - INFO - [execute_tool:111] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Tool name: MergeDataComponent for request_id: 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ToolExecutor - INFO - [execute_tool] Tool name: MergeDataComponent for request_id: 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ToolExecutor - INFO - [execute_tool:144] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Processing payload with component MergeDataComponent for request_id: 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ToolExecutor - INFO - [execute_tool] Processing payload with component MergeDataComponent for request_id: 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - MergeDataComponent - INFO - [process:278] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Processing merge data request for request_id: 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - MergeDataComponent - INFO - [process] Processing merge data request for request_id: 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - MergeDataComponent - INFO - [process:280] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'request_id']
2025-07-14 19:52:03 - MergeDataComponent - INFO - [process] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'request_id']
2025-07-14 19:52:03 - MergeDataComponent - INFO - [process:294] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'request_id']
2025-07-14 19:52:03 - MergeDataComponent - INFO - [process] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'request_id']
2025-07-14 19:52:03 - MergeDataComponent - INFO - [process:314] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Merging data for request_id 30cd2427-8b3a-4583-8276-5daeba45de08. Strategy: 'Overwrite', Num additional inputs: 1
2025-07-14 19:52:03 - MergeDataComponent - INFO - [process] Merging data for request_id 30cd2427-8b3a-4583-8276-5daeba45de08. Strategy: 'Overwrite', Num additional inputs: 1
2025-07-14 19:52:03 - MergeDataComponent - INFO - [process:400] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Dictionary input_1 merged with overwrite strategy for request_id 30cd2427-8b3a-4583-8276-5daeba45de08. Current keys: ['hello', 'true']
2025-07-14 19:52:03 - MergeDataComponent - INFO - [process] Dictionary input_1 merged with overwrite strategy for request_id 30cd2427-8b3a-4583-8276-5daeba45de08. Current keys: ['hello', 'true']
2025-07-14 19:52:03 - MergeDataComponent - INFO - [process:453] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] All data merged successfully for request_id 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - MergeDataComponent - INFO - [process] All data merged successfully for request_id 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ToolExecutor - INFO - [execute_tool:148] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Component MergeDataComponent processed payload successfully for request_id: 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ToolExecutor - INFO - [execute_tool] Component MergeDataComponent processed payload successfully for request_id: 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ToolExecutor - INFO - [execute_tool:154] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] ToolExecutor returning raw component result for request_id: 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: 30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ComponentSystem - INFO - [_process_message:717] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Tool MergeDataComponent executed successfully for RequestID=30cd2427-8b3a-4583-8276-5daeba45de08, TaskID=ApiRequestNode-node-execution-request-0-1183-1752502923.084336
2025-07-14 19:52:03 - ComponentSystem - INFO - [_process_message] Tool MergeDataComponent executed successfully for RequestID=30cd2427-8b3a-4583-8276-5daeba45de08, TaskID=ApiRequestNode-node-execution-request-0-1183-1752502923.084336
2025-07-14 19:52:03 - ComponentSystem - INFO - [_send_result:1007] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Preparing to send result for component ApiRequestNode, RequestID=30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ComponentSystem - INFO - [_send_result:1105] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Sending Kafka response: RequestID=30cd2427-8b3a-4583-8276-5daeba45de08, Response={
  "request_id": "30cd2427-8b3a-4583-8276-5daeba45de08",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1752502923.088907,
  "transition_id": "transition-MergeDataComponent-1752495367072",
  "result": {
    "hello": {},
    "true": "true"
  },
  "error": null
}
2025-07-14 19:52:03 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=30cd2427-8b3a-4583-8276-5daeba45de08, Response={
  "request_id": "30cd2427-8b3a-4583-8276-5daeba45de08",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1752502923.088907,
  "transition_id": "transition-MergeDataComponent-1752495367072",
  "result": {
    "hello": {},
    "true": "true"
  },
  "error": null
}
2025-07-14 19:52:03 - ComponentSystem - INFO - [_send_result:1121] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Sent result for component ApiRequestNode to topic node_results for RequestID=30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=30cd2427-8b3a-4583-8276-5daeba45de08
2025-07-14 19:52:03 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Successfully committed offset 1184 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1183-1752502923.084336
2025-07-14 19:52:03 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1184 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1183-1752502923.084336
2025-07-14 19:52:03 - ComponentSystem - INFO - [_process_message:936] [ReqID:30cd2427-8b3a-4583-8276-5daeba45de08] [CorrID:25c39fe0-379e-41aa-a491-dd5faf79327d] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1183, TaskID=ApiRequestNode-node-execution-request-0-1183-1752502923.084336
2025-07-14 19:52:03 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1183, TaskID=ApiRequestNode-node-execution-request-0-1183-1752502923.084336
