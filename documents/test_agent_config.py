#!/usr/bin/env python3

import sys
import json
import os

# Add the workflow-service to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'workflow-service'))

def test_agent_config_tools():
    """Test if tools are properly integrated into agent_config field_value"""
    print("🔍 Testing agent_config tool integration...")
    
    try:
        # Load the agentic schema
        with open('agentic_schema.json', 'r') as f:
            agentic_schema = json.load(f)
        
        workflow_data = agentic_schema.get('workflow_data', {})
        
        # Import the conversion function
        from app.services.workflow_builder.workflow_schema_converter import convert_workflow_to_transition_schema
        
        # Convert to transition schema
        result = convert_workflow_to_transition_schema(workflow_data)
        
        print(f"\n🎉 CONVERSION SUCCESSFUL!")
        
        # Find the agent transition
        agent_transition = None
        for transition in result['transitions']:
            if transition['execution_type'] == 'agent':
                agent_transition = transition
                break
        
        if not agent_transition:
            print("❌ No agent transition found!")
            return False
        
        print(f"✅ Found agent transition: {agent_transition['id']}")
        
        # Check tools_to_use
        tools_to_use = agent_transition['node_info']['tools_to_use']
        print(f"📋 Tools to use: {len(tools_to_use)}")
        
        # Find the AgenticAI tool
        agentic_tool = None
        for tool in tools_to_use:
            if tool['tool_name'] == 'AgenticAI':
                agentic_tool = tool
                break
        
        if not agentic_tool:
            print("❌ No AgenticAI tool found in tools_to_use!")
            return False
        
        print(f"✅ Found AgenticAI tool")
        
        # Check tool_params
        tool_params = agentic_tool.get('tool_params', {})
        items = tool_params.get('items', [])
        
        print(f"📋 Tool params items: {len(items)}")
        
        # Find agent_config item
        agent_config_item = None
        for item in items:
            if item['field_name'] == 'agent_config':
                agent_config_item = item
                break
        
        if not agent_config_item:
            print("❌ No agent_config item found in tool_params!")
            return False
        
        print(f"✅ Found agent_config item")
        
        # Check the field_value
        agent_config = agent_config_item.get('field_value', {})
        
        print(f"\n🔍 AGENT CONFIG CONTENTS:")
        print(json.dumps(agent_config, indent=2))
        
        # Check if agent_tools exists
        if 'agent_tools' not in agent_config:
            print("❌ No 'agent_tools' found in agent_config field_value!")
            return False
        
        agent_tools = agent_config['agent_tools']
        print(f"\n✅ Found agent_tools with {len(agent_tools)} tools")
        
        # Check each tool
        for i, tool in enumerate(agent_tools, 1):
            tool_type = tool.get('tool_type', 'unknown')
            component = tool.get('component', {})
            component_name = component.get('component_name', 'Unknown')
            component_type = component.get('component_type', 'Unknown')
            
            print(f"   {i}. {component_name} ({component_type}) - Type: {tool_type}")
            
            # Check if it has proper structure
            if tool_type == 'workflow_component':
                required_fields = ['component_id', 'component_type', 'component_name']
                missing_fields = [field for field in required_fields if field not in component]
                if missing_fields:
                    print(f"      ⚠️  Missing fields: {missing_fields}")
                else:
                    print(f"      ✅ All required fields present")
                
                # Check for MCP metadata
                if 'mcp_metadata' in component:
                    mcp_metadata = component['mcp_metadata']
                    tool_name = mcp_metadata.get('tool_name', 'unknown')
                    print(f"      🔧 MCP tool: {tool_name}")
        
        # Save the result for inspection
        with open('agent_config_test_result.json', 'w') as f:
            json.dump(agent_config, f, indent=2)
        
        print(f"\n💾 Agent config saved to agent_config_test_result.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in agent config test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting agent config tool integration test...")
    print("=" * 60)
    
    success = test_agent_config_tools()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 AGENT CONFIG TEST PASSED! Tools are properly integrated.")
    else:
        print("❌ AGENT CONFIG TEST FAILED! Tools are not properly integrated.")
