{"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"AgenticAI-1750240793895_query": {"node_id": "AgenticAI-1750240793895", "node_name": "AI Agent Executor", "input_name": "query", "connected_to_start": true, "required": true, "input_type": "string", "options": null}}}}, "width": 208, "height": 122, "selected": false, "dragging": false}, {"id": "AgenticAI-1750240793895", "type": "WorkflowNode", "position": {"x": 400, "y": 0}, "data": {"label": "AI Agent Executor", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "type": "component", "beta": true, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "input_type": "credential", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Determines if agent handles single response or multi-turn conversation.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "response", "options": ["response", "interactive"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"tools": [{"node_id": "MCP_Script_Generation_script_generate-1750240813026", "node_type": "MCP_Script_Generation_script_generate", "node_label": "Script Generation - script_generate", "component_id": "MCP_Script_Generation_script_generate-1750240813026", "component_type": "MCP_Script_Generation_script_generate", "component_name": "Script Generation - script_generate", "component_definition": {"name": "MCP_Script_Generation_script_generate", "display_name": "Script Generation - script_generate", "description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>", "inputs": [{"name": "topic", "display_name": "Topic", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "script_type", "display_name": "script type", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "TOPIC", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "keywords", "display_name": "keywords", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "video_type", "display_name": "video type", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "SHORT", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "link", "display_name": "Link", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "title", "display_name": "title", "output_type": "string"}, {"name": "script", "display_name": "script", "output_type": "string"}, {"name": "script_type", "display_name": "script_type", "output_type": "string"}, {"name": "video_type", "display_name": "video_type", "output_type": "string"}, {"name": "link", "display_name": "link", "output_type": "string"}], "type": "MCP", "path": "mcp.mcp_script_generation_script_generate"}, "component_config": {}, "mcp_server_id": "47ee1ee5-5460-4806-9abd-1599613792cf", "mcp_tool_name": "script_generate", "mcp_input_schema_required": ["topic"], "mcp_output_properties": ["title", "script", "script_type", "video_type", "link"]}, {"node_id": "MCP_video-generation-mcp_generate_video-1750240849705", "node_type": "MCP_video-generation-mcp_generate_video", "node_label": "video-generation-mcp - generate_video", "component_id": "MCP_video-generation-mcp_generate_video-1750240849705", "component_type": "MCP_video-generation-mcp_generate_video", "component_name": "video-generation-mcp - generate_video", "component_definition": {"name": "MCP_video-generation-mcp_generate_video", "display_name": "video-generation-mcp - generate_video", "description": "generate and process the video", "inputs": [{"name": "view_type", "display_name": "view type", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "stock_video_clips", "display_name": "Stock Video Clips", "info": "", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "stock_image_clips", "display_name": "Stock Image Clips", "info": "", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "event_stock_clips", "display_name": "Event Stock Clips", "info": "", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "audio_urls", "display_name": "Audio Urls", "info": "", "input_type": "array", "input_types": null, "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "avatar_video_urls", "display_name": "Avatar Video Urls", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "subtitles", "display_name": "Subtitles", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "thumbnail", "display_name": "thumbnail", "output_type": "object"}, {"name": "video_link", "display_name": "video_link", "output_type": "object"}, {"name": "duration", "display_name": "duration", "output_type": "number"}], "type": "MCP", "path": "mcp.mcp_video-generation-mcp_generate_video"}, "component_config": {}, "mcp_server_id": "56dfe8af-e982-4351-a669-0a03755b8c99", "mcp_tool_name": "generate_video", "mcp_input_schema_required": ["view_type", "audio_urls", "subtitles"], "mcp_output_properties": ["thumbnail", "video_link", "duration"]}], "id": "AgenticAI-1750240793895", "name": "AI Agent Executor", "model_provider": "OpenAI", "base_url": "", "api_key": {"use_credential_id": true, "value": "", "credential_id": "ea546b1e-42e5-4147-b43c-c190a99da7b9"}, "model_name": "gpt-4o-mini", "temperature": 0.7, "description": "the agent is responsible for running script and video", "execution_type": "response", "query": "", "system_message": "you are a helpful assistand agent which can execute tools", "termination_condition": "", "max_tokens": 1000, "input_variables": {}, "autogen_agent_type": "Assistant"}}, "width": 208, "height": 218, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Script_Generation_script_generate-1750240813026", "type": "WorkflowNode", "position": {"x": 40, "y": -200}, "data": {"label": "Script Generation - script_generate", "type": "mcp", "originalType": "MCP_Script_Generation_script_generate", "definition": {"name": "MCP_Script_Generation_script_generate", "display_name": "Script Generation - script_generate", "description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "topic", "display_name": "Topic", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "script_type", "display_name": "script type", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "TOPIC", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "keywords", "display_name": "keywords", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "video_type", "display_name": "video type", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "SHORT", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "link", "display_name": "Link", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "title", "display_name": "title", "output_type": "string"}, {"name": "script", "display_name": "script", "output_type": "string"}, {"name": "script_type", "display_name": "script_type", "output_type": "string"}, {"name": "video_type", "display_name": "video_type", "output_type": "string"}, {"name": "link", "display_name": "link", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_script_generation_script_generate", "type": "MCP", "mcp_info": {"server_id": "47ee1ee5-5460-4806-9abd-1599613792cf", "server_path": "", "tool_name": "script_generate", "input_schema": {"$defs": {"Keywords": {"properties": {"time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time"}, "objective": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Objective"}, "audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Audience"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Gender"}, "tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "speakers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Speakers"}}, "title": "Keywords", "type": "object"}, "ScriptType": {"enum": ["VIDEO", "TOPIC", "SCRIPT", "BLOG", "AI"], "title": "ScriptType", "type": "string"}, "VideoType": {"enum": ["SHORT", "LONG"], "title": "VideoType", "type": "string"}}, "properties": {"topic": {"title": "Topic", "type": "string"}, "script_type": {"$ref": "#/$defs/ScriptType", "default": "TOPIC"}, "keywords": {"$ref": "#/$defs/Keywords"}, "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"}, "link": {"anyOf": [{"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, {"type": "null"}], "default": null, "title": "Link"}}, "required": ["topic"], "title": "GenerateScriptInput", "type": "object"}, "output_schema": {"properties": {"title": {"type": "string", "description": "Title of the generated script", "title": "title"}, "script": {"type": "string", "description": "The generated script", "title": "script"}, "script_type": {"type": "string", "description": "Type of the script", "title": "script_type"}, "video_type": {"type": "string", "description": "The type of video", "title": "video_type"}, "link": {"type": "string", "format": "uri", "description": "Optional link for the script", "title": "link"}}}}}, "config": {}}, "width": 208, "height": 234, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_video-generation-mcp_generate_video-1750240849705", "type": "WorkflowNode", "position": {"x": -240, "y": -220}, "data": {"label": "video-generation-mcp - generate_video", "type": "mcp", "originalType": "MCP_video-generation-mcp_generate_video", "definition": {"name": "MCP_video-generation-mcp_generate_video", "display_name": "video-generation-mcp - generate_video", "description": "generate and process the video", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "view_type", "display_name": "view type", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "stock_video_clips", "display_name": "Stock Video Clips", "info": "", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "stock_image_clips", "display_name": "Stock Image Clips", "info": "", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "event_stock_clips", "display_name": "Event Stock Clips", "info": "", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "audio_urls", "display_name": "Audio Urls", "info": "", "input_type": "array", "input_types": null, "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "avatar_video_urls", "display_name": "Avatar Video Urls", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "subtitles", "display_name": "Subtitles", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "thumbnail", "display_name": "thumbnail", "output_type": "object"}, {"name": "video_link", "display_name": "video_link", "output_type": "object"}, {"name": "duration", "display_name": "duration", "output_type": "number"}], "is_valid": true, "path": "mcp.mcp_video-generation-mcp_generate_video", "type": "MCP", "mcp_info": {"server_id": "56dfe8af-e982-4351-a669-0a03755b8c99", "server_path": "", "tool_name": "generate_video", "input_schema": {"$defs": {"EventStockClip": {"properties": {"clip": {"minimum": 0, "title": "Clip", "type": "integer"}, "at_time": {"minimum": 0, "title": "At Time", "type": "number"}, "duration": {"exclusiveMinimum": 0, "title": "Duration", "type": "number"}}, "required": ["clip", "at_time", "duration"], "title": "EventStockClip", "type": "object"}, "StockImageClip": {"properties": {"at_time": {"minimum": 0, "title": "At Time", "type": "number"}, "url": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Url", "type": "string"}}, "required": ["at_time", "url"], "title": "StockImageClip", "type": "object"}, "StockVideoClip": {"properties": {"at_time": {"minimum": 0, "title": "At Time", "type": "number"}, "url": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Url", "type": "string"}}, "required": ["at_time", "url"], "title": "StockVideoClip", "type": "object"}, "VideoViewType": {"enum": ["LANDSCAPE", "PORTRAIT", "SQUARE"], "title": "VideoViewType", "type": "string"}}, "properties": {"view_type": {"$ref": "#/$defs/VideoViewType"}, "stock_video_clips": {"default": [], "items": {"$ref": "#/$defs/StockVideoClip"}, "title": "Stock Video Clips", "type": "array"}, "stock_image_clips": {"default": [], "items": {"$ref": "#/$defs/StockImageClip"}, "title": "Stock Image Clips", "type": "array"}, "event_stock_clips": {"default": [], "items": {"$ref": "#/$defs/EventStockClip"}, "title": "Event Stock Clips", "type": "array"}, "audio_urls": {"items": {"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, "title": "Audio Urls", "type": "array"}, "avatar_video_urls": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Avatar Video Urls"}, "subtitles": {"minLength": 1, "title": "Subtitles", "type": "string"}}, "required": ["view_type", "audio_urls", "subtitles"], "title": "GenerateVideoObject", "type": "object"}, "output_schema": {"properties": {"thumbnail": {"type": "object", "properties": {"url": {"type": "string", "description": "URL of the thumbnail", "title": "url"}, "mimetype": {"type": "string", "description": "MIME type of the thumbnail", "title": "mimetype"}}, "title": "thumbnail"}, "video_link": {"type": "object", "properties": {"url": {"type": "string", "description": "URL of the video", "title": "url"}, "mimetype": {"type": "string", "description": "MIME type of the video", "title": "mimetype"}}, "title": "video_link"}, "duration": {"type": "number", "description": "Duration of the video", "title": "duration"}}}}}, "config": {}}, "width": 208, "height": 302, "selected": false, "dragging": false, "style": {"opacity": 1}}], "edges": [{"id": "reactflow__edge-start-nodeflow-AgenticAI-1750240793895query", "source": "start-node", "sourceHandle": "flow", "target": "AgenticAI-1750240793895", "targetHandle": "query", "type": "default", "animated": true}, {"id": "reactflow__edge-MCP_Script_Generation_script_generate-1750240813026title-AgenticAI-1750240793895tools", "source": "MCP_Script_Generation_script_generate-1750240813026", "sourceHandle": "title", "target": "AgenticAI-1750240793895", "targetHandle": "tools", "type": "default", "animated": true}, {"id": "reactflow__edge-MCP_video-generation-mcp_generate_video-1750240849705video_link-AgenticAI-1750240793895tools", "source": "MCP_video-generation-mcp_generate_video-1750240849705", "sourceHandle": "video_link", "target": "AgenticAI-1750240793895", "targetHandle": "tools", "type": "default", "animated": true}]}