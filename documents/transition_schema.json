{"nodes": [{"id": "AgenticAI", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AgenticAI", "input_schema": {"predefined_fields": [{"field_name": "model_provider", "data_type": {"type": "string", "description": "The AI model provider to use."}, "required": false}, {"field_name": "base_url", "data_type": {"type": "string", "description": "Base URL for the API (leave empty for default provider URL)."}, "required": false}, {"field_name": "api_key", "data_type": {"type": "string", "description": "API key for the model provider. Can be entered directly or referenced from secure storage."}, "required": false}, {"field_name": "model_name", "data_type": {"type": "string", "description": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama."}, "required": false}, {"field_name": "temperature", "data_type": {"type": "number", "description": "Controls randomness: 0 is deterministic, higher values are more random."}, "required": false}, {"field_name": "description", "data_type": {"type": "string", "description": "Description of the agent for UI display."}, "required": false}, {"field_name": "execution_type", "data_type": {"type": "string", "description": "Determines if agent handles single response or multi-turn conversation."}, "required": false}, {"field_name": "query", "data_type": {"type": "string", "description": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "system_message", "data_type": {"type": "string", "description": "System prompt/instructions for the agent. If empty, will use default based on query."}, "required": false}, {"field_name": "termination_condition", "data_type": {"type": "string", "description": "Defines when multi-turn conversations should end. Required for interactive execution type."}, "required": false}, {"field_name": "max_tokens", "data_type": {"type": "number", "description": "Maximum response length in tokens."}, "required": false}, {"field_name": "input_variables", "data_type": {"type": "object", "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "memory", "data_type": {"type": "string", "description": "Connect a memory object from another node."}, "required": false}, {"field_name": "autogen_agent_type", "data_type": {"type": "string", "description": "The type of AutoGen agent to create internally."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "final_answer", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "intermediate_steps", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "updated_memory", "data_type": {"type": "string", "description": "", "format": "datetime"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}], "transitions": [{"id": "transition-AgenticAI-1750240793895", "sequence": 1, "transition_type": "initial", "execution_type": "agent", "node_info": {"node_id": "AgenticAI", "tools_to_use": [{"tool_id": 1, "tool_name": "AgenticAI", "tool_params": {"items": [{"field_name": "agent_type", "data_type": "string", "field_value": "component"}, {"field_name": "execution_type", "data_type": "string", "field_value": "response"}, {"field_name": "query", "data_type": "string", "field_value": ""}, {"field_name": "agent_config", "data_type": "object", "field_value": {"agent_tools": [{"mcp_id": "47ee1ee5-5460-4806-9abd-1599613792cf", "mcp_server_path": "", "tool_name": "script_generate", "tool_schema": {"$defs": {"Keywords": {"properties": {"time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time"}, "objective": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Objective"}, "audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Audience"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Gender"}, "tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "speakers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Speakers"}}, "title": "Keywords", "type": "object"}, "ScriptType": {"enum": ["VIDEO", "TOPIC", "SCRIPT", "BLOG", "AI"], "title": "ScriptType", "type": "string"}, "VideoType": {"enum": ["SHORT", "LONG"], "title": "VideoType", "type": "string"}}, "properties": {"topic": {"title": "Topic", "type": "string"}, "script_type": {"$ref": "#/$defs/ScriptType", "default": "TOPIC"}, "keywords": {"$ref": "#/$defs/Keywords"}, "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"}, "link": {"anyOf": [{"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, {"type": "null"}], "default": null, "title": "Link"}}, "required": ["topic"], "title": "GenerateScriptInput", "type": "object"}}, {"mcp_id": "56dfe8af-e982-4351-a669-0a03755b8c99", "mcp_server_path": "", "tool_name": "generate_video", "tool_schema": {"$defs": {"EventStockClip": {"properties": {"clip": {"minimum": 0, "title": "Clip", "type": "integer"}, "at_time": {"minimum": 0, "title": "At Time", "type": "number"}, "duration": {"exclusiveMinimum": 0, "title": "Duration", "type": "number"}}, "required": ["clip", "at_time", "duration"], "title": "EventStockClip", "type": "object"}, "StockImageClip": {"properties": {"at_time": {"minimum": 0, "title": "At Time", "type": "number"}, "url": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Url", "type": "string"}}, "required": ["at_time", "url"], "title": "StockImageClip", "type": "object"}, "StockVideoClip": {"properties": {"at_time": {"minimum": 0, "title": "At Time", "type": "number"}, "url": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Url", "type": "string"}}, "required": ["at_time", "url"], "title": "StockVideoClip", "type": "object"}, "VideoViewType": {"enum": ["LANDSCAPE", "PORTRAIT", "SQUARE"], "title": "VideoViewType", "type": "string"}}, "properties": {"view_type": {"$ref": "#/$defs/VideoViewType"}, "stock_video_clips": {"default": [], "items": {"$ref": "#/$defs/StockVideoClip"}, "title": "Stock Video Clips", "type": "array"}, "stock_image_clips": {"default": [], "items": {"$ref": "#/$defs/StockImageClip"}, "title": "Stock Image Clips", "type": "array"}, "event_stock_clips": {"default": [], "items": {"$ref": "#/$defs/EventStockClip"}, "title": "Event Stock Clips", "type": "array"}, "audio_urls": {"items": {"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, "title": "Audio Urls", "type": "array"}, "avatar_video_urls": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Avatar Video Urls"}, "subtitles": {"minLength": 1, "title": "Subtitles", "type": "string"}}, "required": ["view_type", "audio_urls", "subtitles"], "title": "GenerateVideoObject", "type": "object"}}]}}]}}], "input_data": [], "output_data": []}, "result_resolution": {"node_type": "agent", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "query", "handle_name": "Query/Objective", "data_type": "string", "required": true, "description": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."}, {"handle_id": "input_variables", "handle_name": "Input Variables", "data_type": "object", "required": false, "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, {"handle_id": "tools", "handle_name": "Tools", "data_type": "string", "required": false, "description": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle."}, {"handle_id": "memory", "handle_name": "Memory Object", "data_type": "string", "required": false, "description": "Connect a memory object from another node."}], "output_handles": [{"handle_id": "final_answer", "handle_name": "Final Answer", "data_type": "string", "description": ""}, {"handle_id": "intermediate_steps", "handle_name": "Intermediate Steps", "data_type": "string", "description": ""}, {"handle_id": "updated_memory", "handle_name": "Updated Memory", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"final_answer": "final_answer", "intermediate_steps": "intermediate_steps", "updated_memory": "updated_memory", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.final_answer", "output_data.final_answer", "response.final_answer", "data.final_answer", "result.intermediate_steps", "output_data.intermediate_steps", "response.intermediate_steps", "data.intermediate_steps", "result.updated_memory", "output_data.updated_memory", "response.updated_memory", "data.updated_memory", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "final_answer"}}, "approval_required": false, "end": true}]}