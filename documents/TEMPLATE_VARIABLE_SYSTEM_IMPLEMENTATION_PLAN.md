# Template Variable System Implementation Plan

## Project Overview
Implementation of a universal template variable system for workflow components that allows users to reference connected input variables in query fields using `{variable_name}` syntax.

## Investigation Summary ✅

### Schema Converter Analysis (workflow-service) ✅
- **Tool Parameter Processing** (lines 1556-1589): Already extracts field values from node configs
- **Handle Mapping Processing** (lines 3140-3164): Sets field values to null when handle connections exist  
- **Universal Result Resolver** (lines 589-637): Provides dynamic field resolution for complex workflows
- **Input/Output Data Creation** (lines 1594-1681): Processes connection metadata comprehensively

### Orchestration Engine Analysis (orchestration-engine) ✅
- **Universal Parameter Resolution** (`workflow_utils.py`): Handle-based mappings, schema-based formatting, and existing placeholder resolution with `${variable_name}` syntax
- **Transition Schema Support** (`transition_schema.json`): Comprehensive tool parameter structure, handle mapping system, and result resolution metadata
- **Enhanced Execution Pipeline** (`transition_handler.py`): Universal tool parameter resolution with enhanced metadata support

### Key Finding: Existing Template Infrastructure 🎯
The orchestration engine **already has a template resolution system** that processes `${variable_name}` placeholders in `_process_params_for_placeholders()` method. This can be easily extended to support our `{variable_name}` syntax.

---

## Phase I: Pre-Implementation Impact Assessment & Planning

### Affected Modules Analysis

#### Direct Dependencies:
1. **workflow-service/app/utils/workflow_builder/input_helpers.py** - Core input creation functions
2. **workflow-service/app/services/workflow_builder/workflow_schema_converter.py** - Schema transformation pipeline
3. **orchestration-engine/app/core_/workflow_utils.py** - Parameter resolution system
4. **orchestration-engine/app/shared/json_schemas/transition_schema.json** - Schema definitions
5. **workflow-builder-app/src/components/inspector/FormField.tsx** - Frontend input components

#### Transitive Dependencies:
1. **All workflow components** (AgenticAI, Processing, HITL) - Will inherit template support
2. **Frontend validation system** - Template syntax validation
3. **Workflow execution pipeline** - Parameter resolution integration
4. **Component definition system** - Enhanced input specifications

#### High-Risk Areas:
- **Parameter Resolution Pipeline**: Critical for all workflow executions
- **Schema Validation**: Must maintain backward compatibility
- **Handle Mapping System**: Core connection infrastructure
- **Component Input Processing**: Affects all existing components

### Risk Assessment:
- **Low Risk**: Implementation builds on existing infrastructure
- **Zero Breaking Changes**: Backward compatibility maintained
- **Isolated Changes**: Template features are additive, not destructive

---

## Phase II: Test-Driven Development (TDD) Task List

### 🧪 TDD Cycle 1: Frontend Template System

| Task ID | Description | Owner | Priority | Estimated Hours | Dependencies |
|---------|-------------|-------|----------|-----------------|--------------|
| TDD-F1 | Write failing tests for `create_template_enabled_input()` function | Frontend Dev | High | 2 | None | ✅ |
| TDD-F2 | Write failing tests for `TemplateVariableMixin` methods | Frontend Dev | High | 3 | TDD-F1 | ✅ |
| TDD-F3 | Write failing tests for template syntax validation | Frontend Dev | High | 2 | TDD-F2 | ✅ |
| TDD-F4 | Implement minimal `create_template_enabled_input()` to pass tests | Frontend Dev | High | 1 | TDD-F1 | ✅ |
| TDD-F5 | Implement minimal `TemplateVariableMixin` to pass tests | Frontend Dev | High | 2 | TDD-F2 | ✅ |
| TDD-F6 | Implement minimal template validation to pass tests | Frontend Dev | High | 1 | TDD-F3 | ✅ |
| TDD-F7 | Refactor frontend template code for DRY/SOLID principles | Frontend Dev | Medium | 2 | TDD-F4,F5,F6 |  |
| TDD-F8 | Run complete frontend test suite | QA | High | 1 | TDD-F7 |  |

### 🧪 TDD Cycle 2: Backend Schema Converter

| Task ID | Description | Owner | Priority | Estimated Hours | Dependencies |
|---------|-------------|-------|----------|-----------------|--------------|
| TDD-B1 | Write failing tests for `_detect_template_variables()` method | Backend Dev | High | 2 | None | ✅ |
| TDD-B2 | Write failing tests for `_process_template_enabled_field()` method | Backend Dev | High | 3 | TDD-B1 | ✅ |
| TDD-B3 | Write failing tests for `_process_tool_parameters_with_templates()` method | Backend Dev | High | 3 | TDD-B2 | ✅ |
| TDD-B4 | Write failing tests for schema converter integration | Backend Dev | High | 4 | TDD-B3 |  |
| TDD-B5 | Implement minimal template variable detection to pass tests | Backend Dev | High | 2 | TDD-B1 |  |
| TDD-B6 | Implement minimal template field processing to pass tests | Backend Dev | High | 3 | TDD-B2 |  |
| TDD-B7 | Implement minimal tool parameter processing to pass tests | Backend Dev | High | 3 | TDD-B3 |  |
| TDD-B8 | Implement minimal schema converter integration to pass tests | Backend Dev | High | 4 | TDD-B4 |  |
| TDD-B9 | Refactor backend schema converter code for clean architecture | Backend Dev | Medium | 3 | TDD-B5,B6,B7,B8 |  |
| TDD-B10 | Run complete backend schema converter test suite | QA | High | 1 | TDD-B9 |  |

### 🧪 TDD Cycle 3: Orchestration Engine Template Resolution

| Task ID | Description | Owner | Priority | Estimated Hours | Dependencies |
|---------|-------------|-------|----------|-----------------|--------------|
| TDD-O1 | Write failing tests for `_resolve_template_variables()` method | Backend Dev | High | 2 | None |
| TDD-O2 | Write failing tests for `_process_template_enabled_parameters()` method | Backend Dev | High | 3 | TDD-O1 |
| TDD-O3 | Write failing tests for `_format_tool_parameters_with_templates()` method | Backend Dev | High | 4 | TDD-O2 |
| TDD-O4 | Write failing tests for orchestration engine integration | Backend Dev | High | 4 | TDD-O3 |
| TDD-O5 | Implement minimal template variable resolution to pass tests | Backend Dev | High | 2 | TDD-O1 |
| TDD-O6 | Implement minimal template parameter processing to pass tests | Backend Dev | High | 3 | TDD-O2 |
| TDD-O7 | Implement minimal enhanced parameter formatting to pass tests | Backend Dev | High | 4 | TDD-O3 |
| TDD-O8 | Implement minimal orchestration engine integration to pass tests | Backend Dev | High | 4 | TDD-O4 |
| TDD-O9 | Refactor orchestration engine code for performance optimization | Backend Dev | Medium | 3 | TDD-O5,O6,O7,O8 |
| TDD-O10 | Run complete orchestration engine test suite | QA | High | 1 | TDD-O9 |

---

## Phase III: Implementation Task List

### 📋 Core Implementation Tasks

| Task ID | Description | Owner | Priority | Estimated Hours | Dependencies | Status |
|---------|-------------|-------|----------|-----------------|--------------|--------|
| IMPL-1 | **Dependency Analysis**: Identify all affected files and modules (direct and indirect) | Lead Developer | High | 4 | Investigation Complete | ✅ Complete |
| IMPL-2 | **Static Analysis**: Use static analysis + runtime tracing for impact assessment | DevOps | High | 3 | IMPL-1 | ✅ Complete |
| IMPL-3 | **Test Environment Setup**: Configure test environment for template variable testing | QA + DevOps | High | 2 | IMPL-1 | ⏳ Pending |
| IMPL-4 | **Frontend Template Helper Implementation**: Create `create_template_enabled_input()` function | Frontend Dev | High | 3 | TDD-F8 | ✅ Complete |
| IMPL-5 | **Frontend Template Mixin Implementation**: Create `TemplateVariableMixin` with validation methods | Frontend Dev | High | 4 | IMPL-4 | ✅ Complete |
| IMPL-6 | **Frontend Form Component Enhancement**: Update FormField.tsx for template syntax support | Frontend Dev | Medium | 3 | IMPL-5 | ⏳ Pending |
| IMPL-7 | **Backend Schema Converter Enhancement**: Add template variable detection and processing | Backend Dev | High | 6 | TDD-B10 | ⏳ Pending |
| IMPL-8 | **Backend Parameter Processing**: Enhance tool parameter processing with template support | Backend Dev | High | 5 | IMPL-7 | ⏳ Pending |
| IMPL-9 | **Orchestration Engine Template Resolution**: Extend existing placeholder resolution for `{variable_name}` syntax | Backend Dev | High | 6 | TDD-O10 | ⏳ Pending |
| IMPL-10 | **Transition Schema Updates**: Add template metadata support to transition schema | Backend Dev | Medium | 2 | IMPL-9 | ⏳ Pending |
| IMPL-11 | **Component Integration**: Update AgenticAI component with template support example | Backend Dev | Medium | 3 | IMPL-8, IMPL-9 | ⏳ Pending |
| IMPL-12 | **Universal Component Pattern**: Create documentation and examples for template integration | Tech Writer | Medium | 4 | IMPL-11 | ⏳ Pending |

### 🧪 Testing & Validation Tasks

| Task ID | Description | Owner | Priority | Estimated Hours | Dependencies | Status |
|---------|-------------|-------|----------|-----------------|--------------|--------|
| TEST-1 | **Unit Test Suite**: Write comprehensive unit tests for all new functionality | QA + Dev | High | 8 | All TDD cycles | ⏳ Pending |
| TEST-2 | **Integration Test Suite**: Write integration tests for end-to-end template variable flow | QA | High | 6 | IMPL-11 | ⏳ Pending |
| TEST-3 | **Edge Case Testing**: Test edge cases, failure modes, and error handling | QA | High | 4 | TEST-1 | ⏳ Pending |
| TEST-4 | **Performance Testing**: Benchmark template variable resolution performance | QA + DevOps | Medium | 3 | TEST-2 | ⏳ Pending |
| TEST-5 | **Regression Testing**: Ensure no existing functionality is broken | QA | High | 4 | TEST-3 | ⏳ Pending |
| TEST-6 | **Cross-Component Testing**: Test template variables across different component types | QA | Medium | 5 | TEST-5 | ⏳ Pending |
| TEST-7 | **Browser Compatibility Testing**: Test frontend template features across browsers | QA | Low | 2 | TEST-6 | ⏳ Pending |

### 🔍 Code Quality & Review Tasks

| Task ID | Description | Owner | Priority | Estimated Hours | Dependencies | Status |
|---------|-------------|-------|----------|-----------------|--------------|--------|
| REVIEW-1 | **Code Review**: Peer review of all implementation changes | Senior Dev | High | 6 | All IMPL tasks | ⏳ Pending |
| REVIEW-2 | **Architecture Compliance Check**: Ensure changes follow existing patterns | Architect | High | 3 | REVIEW-1 | ⏳ Pending |
| REVIEW-3 | **Security Review**: Review template variable processing for security vulnerabilities | Security Engineer | High | 2 | REVIEW-1 | ⏳ Pending |
| REVIEW-4 | **Performance Review**: Review code for performance implications | Senior Dev | Medium | 2 | REVIEW-2 | ⏳ Pending |
| REVIEW-5 | **Documentation Review**: Review all documentation and examples | Tech Writer | Medium | 2 | IMPL-12 | ⏳ Pending |

### 📚 Documentation & Deployment Tasks

| Task ID | Description | Owner | Priority | Estimated Hours | Dependencies | Status |
|---------|-------------|-------|----------|-----------------|--------------|--------|
| DOC-1 | **API Documentation**: Document new template variable APIs and methods | Tech Writer | Medium | 4 | REVIEW-5 | ⏳ Pending |
| DOC-2 | **User Guide**: Create user guide for template variable usage | Tech Writer | Medium | 3 | DOC-1 | ⏳ Pending |
| DOC-3 | **Developer Guide**: Create developer guide for adding template support to components | Tech Writer | Medium | 4 | DOC-2 | ⏳ Pending |
| DOC-4 | **Migration Guide**: Create migration guide for existing components | Tech Writer | Low | 2 | DOC-3 | ⏳ Pending |
| DEPLOY-1 | **Staging Deployment**: Deploy to staging environment for final testing | DevOps | High | 2 | All TEST tasks | ⏳ Pending |
| DEPLOY-2 | **Production Deployment**: Deploy to production with rollback strategy | DevOps | High | 3 | DEPLOY-1 | ⏳ Pending |
| DEPLOY-3 | **Post-Deployment Monitoring**: Monitor system performance and error rates | DevOps | High | 2 | DEPLOY-2 | ⏳ Pending |

---

## Implementation Timeline

### Sprint 1 (Week 1-2): Foundation & TDD
- Complete all TDD cycles (TDD-F1 through TDD-O10)
- Set up test environment (IMPL-3)
- Complete dependency analysis (IMPL-1, IMPL-2)

### Sprint 2 (Week 3-4): Core Implementation
- Frontend template system (IMPL-4, IMPL-5, IMPL-6)
- Backend schema converter enhancement (IMPL-7, IMPL-8)
- Begin orchestration engine work (IMPL-9)

### Sprint 3 (Week 5-6): Backend Completion & Integration
- Complete orchestration engine implementation (IMPL-9, IMPL-10)
- Component integration and examples (IMPL-11, IMPL-12)
- Begin comprehensive testing (TEST-1, TEST-2)

### Sprint 4 (Week 7-8): Testing & Quality Assurance
- Complete all testing tasks (TEST-3 through TEST-7)
- Code reviews and compliance checks (REVIEW-1 through REVIEW-5)
- Documentation completion (DOC-1 through DOC-4)

### Sprint 5 (Week 9): Deployment & Monitoring
- Staging deployment and final validation (DEPLOY-1)
- Production deployment with monitoring (DEPLOY-2, DEPLOY-3)

---

## Success Criteria

### Functional Requirements ✅
- [ ] Users can use `{variable_name}` syntax in any template-enabled input field
- [ ] Template variables are validated in real-time against available connections
- [ ] Template resolution works seamlessly in the orchestration engine
- [ ] All existing functionality remains unaffected (zero breaking changes)

### Technical Requirements ✅
- [ ] 100% test coverage for new functionality
- [ ] Performance impact < 5% for non-template workflows
- [ ] Memory usage increase < 10% for template-enabled workflows
- [ ] All code follows existing architectural patterns

### Quality Requirements ✅
- [ ] All tests pass (unit, integration, regression)
- [ ] Code review approval from senior developers
- [ ] Architecture compliance verification
- [ ] Security review clearance
- [ ] Documentation completeness verification

---

## Risk Mitigation

### Technical Risks
- **Parameter Resolution Complexity**: Mitigated by building on existing infrastructure
- **Performance Impact**: Mitigated by lazy evaluation and caching strategies
- **Backward Compatibility**: Mitigated by additive-only changes

### Project Risks
- **Timeline Delays**: Mitigated by phased implementation and parallel development
- **Resource Constraints**: Mitigated by clear task dependencies and priority levels
- **Integration Issues**: Mitigated by comprehensive testing strategy

### Rollback Strategy
- **Immediate Rollback**: Feature flags allow instant disabling of template functionality
- **Partial Rollback**: Individual components can be reverted independently
- **Full Rollback**: Complete revert possible due to backward compatibility design

---

## Contact & Ownership

| Role | Name | Responsibilities |
|------|------|------------------|
| **Project Lead** | TBD | Overall project coordination and delivery |
| **Frontend Lead** | TBD | Frontend template system implementation |
| **Backend Lead** | TBD | Schema converter and orchestration engine work |
| **QA Lead** | TBD | Testing strategy and execution |
| **DevOps Lead** | TBD | Deployment and monitoring |
| **Architect** | TBD | Architecture compliance and technical guidance |

---

**Total Estimated Hours**: 142 hours
**Estimated Timeline**: 9 weeks
**Team Size**: 6-8 developers
**Risk Level**: Low (building on existing infrastructure)