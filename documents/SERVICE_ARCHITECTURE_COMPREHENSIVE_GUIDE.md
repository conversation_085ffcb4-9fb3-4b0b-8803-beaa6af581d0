# SERVICE ARCHITECTURE COMPREHENSIVE GUIDE

**Workflow Automation Platform - Complete System Documentation**

---

## Table of Contents

1. [Executive Summary](#executive-summary)
2. [System Architecture Overview](#system-architecture-overview)
3. [Service Deep Dive](#service-deep-dive)
4. [Data Flow Architecture](#data-flow-architecture)
5. [Communication Patterns](#communication-patterns)
6. [State Management](#state-management)
7. [Security Architecture](#security-architecture)
8. [Scalability and Performance](#scalability-and-performance)
9. [Development and Deployment](#development-and-deployment)
10. [Troubleshooting Guide](#troubleshooting-guide)

---

## Executive Summary

### System Overview
This document describes a comprehensive workflow automation platform built using a microservices architecture. The system enables users to create, manage, and execute complex workflows through a visual interface, with support for AI agents, MCP (Model Context Protocol) tools, and various data processing components.

### Key Architectural Decisions
- **Microservices Architecture**: Clear separation of concerns with independent services
- **Event-Driven Communication**: Kafka-based messaging for reliable workflow execution
- **Multi-Protocol Support**: REST, gRPC, WebSocket, SSH, and SSE communications
- **Visual Workflow Editor**: React Flow-based frontend for intuitive workflow creation
- **Tool Integration**: Comprehensive support for MCP tools and external APIs

### Technology Stack
- **Frontend**: Next.js 15, React Flow, TypeScript, Zustand, TailwindCSS
- **Backend**: Python 3.11+, FastAPI, gRPC, Kafka, Redis, PostgreSQL
- **Infrastructure**: Docker, Kubernetes, SSH, WebSocket, SSE
- **Protocols**: REST, gRPC, Kafka, MCP, SSH, WebSocket

---

## System Architecture Overview

### Service Interaction Diagram

```
┌─────────────────┐    HTTP/REST     ┌─────────────────┐    gRPC    ┌─────────────────┐
│                 │ ────────────────▶ │                 │ ──────────▶ │                 │
│ Workflow Builder│                  │   API Gateway   │            │ Workflow Service│
│    (Frontend)   │ ◀──────────────── │                 │ ◀────────── │                 │
└─────────────────┘                  └─────────────────┘            └─────────────────┘
                                              │                               │
                                              │ HTTP/REST                     │ Database
                                              ▼                               ▼
                                      ┌─────────────────┐              ┌─────────────────┐
                                      │                 │              │   PostgreSQL    │
                                      │   Orchestration │              │   (Workflows)   │
                                      │     Engine      │              │                 │
                                      │                 │              └─────────────────┘
                                      └─────────────────┘
                                              │
                                              │ Kafka Messages
                                              ▼
                        ┌─────────────────────┴─────────────────────┐
                        │                                           │
                        ▼                                           ▼
              ┌─────────────────┐                         ┌─────────────────┐
              │                 │                         │                 │
              │ Node Executor   │                         │ MCP Executor    │
              │    Service      │                         │    Service      │
              │                 │                         │                 │
              └─────────────────┘                         └─────────────────┘
                        │                                           │
                        │ Components                                │ SSH/WebSocket
                        ▼                                           ▼
              ┌─────────────────┐                         ┌─────────────────┐
              │   API Calls,    │                         │  External MCP   │
              │ Data Processing,│                         │     Servers     │
              │ AI Components   │                         │                 │
              └─────────────────┘                         └─────────────────┘
```

### Communication Matrix

| From Service | To Service | Protocol | Purpose |
|-------------|------------|----------|---------|
| Workflow Builder | API Gateway | HTTP/REST | User interactions, CRUD operations |
| API Gateway | Workflow Service | gRPC | Workflow management |
| API Gateway | External Services | HTTP/REST | Execution requests |
| Orchestration Engine | Node Executor | Kafka | Component execution |
| Orchestration Engine | MCP Executor | Kafka | Tool execution |
| Node Executor | External APIs | HTTP/REST | API calls |
| MCP Executor | MCP Servers | SSH/WebSocket/SSE | Tool execution |
| All Services | Redis | TCP | State caching |
| All Services | PostgreSQL | TCP | Data persistence |

---

## Service Deep Dive

### 1. Workflow Builder App (Frontend)

**Purpose**: Visual workflow creation and management interface

**Key Technologies**:
- Next.js 15 with TypeScript
- React Flow for visual workflow editing
- Zustand for state management
- TailwindCSS for styling
- Axios for API communication

**Core Components**:
```typescript
// Main structure
src/
├── app/                    # Next.js app router
├── components/            
│   ├── canvas/            # Workflow visual editor
│   ├── inspector/         # Node configuration panel
│   ├── nodes/            # Custom workflow nodes
│   └── validation/       # Form validation
├── lib/
│   ├── api.ts            # API communication layer
│   └── validation/       # Workflow validation logic
└── store/                # Zustand state stores
```

**Data Handled**:
- Workflow definitions (nodes, edges, metadata)
- Component configurations
- User authentication state
- MCP tool configurations
- Validation states

**Communication Patterns**:
- REST API calls to API Gateway (port 8000)
- Real-time validation of workflow structure
- Credential management integration
- WebSocket connections for real-time updates

**Key Features**:
- Drag-and-drop workflow builder with React Flow
- Dynamic component input generation
- Template variable system
- Tool connection management
- Real-time validation and error feedback

---

### 2. API Gateway

**Purpose**: Central request router, authentication, and service orchestration

**Key Technologies**:
- FastAPI with Python 3.11+
- gRPC clients for backend services
- JWT authentication with Redis sessions
- CORS middleware for cross-origin requests

**Core Structure**:
```python
app/
├── api/routers/           # Route definitions
│   ├── workflow_routes.py # Workflow CRUD operations
│   ├── user_routes.py     # Authentication
│   └── mcp_routes.py      # MCP management
├── core/
│   ├── auth_guard.py      # JWT validation
│   └── security.py        # Security utilities
├── grpc_/                 # Generated gRPC clients
├── services/              # Business logic layer
└── schemas/               # Pydantic models
```

**Authentication Flow**:
1. JWT token validation
2. Redis session verification
3. Role-based access control
4. Request forwarding to appropriate services

**Key Responsibilities**:
- Route HTTP requests to appropriate microservices
- Handle authentication and authorization
- Validate request payloads
- Transform responses for frontend consumption
- Manage CORS and security headers

**Communication Patterns**:
- HTTP/REST endpoints for frontend
- gRPC calls to backend services
- Redis for session management
- Error handling and response transformation

---

### 3. Workflow Service

**Purpose**: Workflow lifecycle management and persistence

**Key Technologies**:
- Python gRPC server
- PostgreSQL for data persistence
- SQLAlchemy ORM
- Component registry system

**Core Structure**:
```python
app/
├── services/
│   ├── workflow_service.py      # Main gRPC service
│   ├── workflow_functions.py    # CRUD operations
│   └── workflow_builder/        # Component management
├── models/                      # Database models
├── components/                  # Workflow components
│   ├── ai/                     # AI components
│   ├── data_interaction/       # API components  
│   ├── processing/             # Data processing
│   └── tools/                  # MCP definitions
└── db/                         # Database management
```

**Data Models**:
- Workflows: Core workflow metadata
- Workflow versions: Version management
- Components: Available workflow components
- Templates: Workflow templates

**gRPC Service Methods**:
- `createWorkflow`: Create new workflows
- `getWorkflow`: Retrieve workflow details
- `updateWorkflow`: Modify existing workflows
- `listWorkflows`: Query workflows with pagination
- `toggleWorkflowVisibility`: Manage workflow visibility

**Component System**:
- Dynamic component discovery and registration
- Schema-based component definitions
- Input/output type validation
- Dependency management

---

### 4. Orchestration Engine

**Purpose**: Workflow execution coordination and state management

**Key Technologies**:
- Python asyncio for concurrent execution
- Kafka consumers and producers
- Redis for state management
- PostgreSQL for execution history
- Advanced workflow state machine

**Core Architecture**:
```python
app/
├── core_/
│   ├── executor_core.py           # Main workflow engine
│   ├── state_manager.py           # State management
│   ├── transition_handler.py      # Node transitions
│   └── conditional_routing_handler.py # Conditional logic
├── execution/
│   ├── executor_server_kafka.py   # Kafka consumer
│   └── run_engine.py             # Engine runner
├── services/
│   ├── agent_executor.py         # AI agent execution
│   ├── node_executor.py          # Component execution
│   └── kafka_tool_executor.py    # Tool execution
└── utils/
    └── enhanced_logger.py        # Comprehensive logging
```

**Execution Flow**:
1. Consume workflow execution requests from Kafka
2. Initialize workflow state in Redis
3. Execute nodes based on dependency graph
4. Handle conditional routing and loops
5. Manage parallel execution paths
6. Store execution results in PostgreSQL

**State Management**:
- Redis for real-time workflow state
- PostgreSQL for persistent execution history
- Event-driven state transitions
- Automatic error recovery and retries

**Key Features**:
- Parallel node execution
- Conditional branching support
- Loop execution with aggregation
- Real-time state monitoring
- Comprehensive error handling

---

### 5. Node Executor Service

**Purpose**: Individual workflow node execution

**Key Technologies**:
- Python asyncio with dynamic component system
- Kafka consumers for task distribution
- Component registry for extensibility
- Tool-based execution architecture

**Core Architecture**:
```python
app/
├── core_/
│   ├── component_system.py       # Component management
│   ├── base_component.py         # Base component class
│   └── tool_executor.py          # Tool execution
├── components/                   # Individual components
│   ├── api_component.py          # API requests
│   ├── data_to_dataframe_component.py # Data processing
│   ├── conditional_component.py   # Conditional logic
│   └── ai_component.py           # AI integrations
└── utils/
    └── logging_config.py         # Logging setup
```

**Component Types**:
- **API Components**: HTTP request execution
- **Data Processing**: Text manipulation, data transformation
- **AI Components**: LLM integration, sentiment analysis
- **Conditional Components**: Routing logic
- **Integration Components**: External service connections

**Execution Model**:
1. Register all available components dynamically
2. Consume execution requests from Kafka
3. Route requests to appropriate component
4. Execute component with provided configuration
5. Return results via Kafka producer

**Error Handling**:
- Component-level error isolation
- Retry mechanisms with exponential backoff
- Detailed error logging and reporting
- Graceful degradation strategies

---

### 6. MCP Execution Service

**Purpose**: Model Context Protocol tool execution

**Key Technologies**:
- Python asyncio for concurrent connections
- SSH client for remote tool execution
- WebSocket and SSE for real-time communication
- Credential management integration

**Core Architecture**:
```python
app/
├── core_/
│   ├── kafka_service.py          # Kafka integration
│   ├── mcp_executor.py           # MCP execution logic
│   ├── client.py                 # MCP client
│   └── custom_ssh_client.py      # SSH management
├── services/
│   ├── ssh_manager.py            # SSH connection management
│   ├── credential_service.py     # Credential handling
│   └── authentication_manager.py # Auth management
└── schemas/                      # Data models
```

**Connection Types**:
- **SSH**: Remote server tool execution
- **WebSocket**: Real-time MCP communication
- **SSE**: Server-sent events for streaming
- **HTTP**: Direct API integration

**MCP Protocol Support**:
- Tool discovery and schema retrieval
- Parameter validation and type checking
- Streaming response handling
- Error propagation and recovery

**Security Features**:
- SSH key management with global key store
- Credential encryption and secure storage
- Connection pooling and reuse
- Audit logging for security compliance

---

## Data Flow Architecture

### Workflow Creation Flow

```
User Interface → API Gateway → Workflow Service → PostgreSQL
     │                │              │               │
     │                │              │               └─ Store workflow
     │                │              └─ Validate schema
     │                └─ Authenticate request
     └─ Create workflow visually
```

1. **User Creates Workflow**: Visual editor captures nodes and edges
2. **Frontend Validation**: Client-side validation of workflow structure
3. **API Request**: POST to `/api/v1/workflows`
4. **Authentication**: JWT validation and session check
5. **gRPC Call**: Forward to Workflow Service
6. **Schema Validation**: Validate workflow definition
7. **Database Storage**: Store in PostgreSQL
8. **Response**: Return workflow ID and metadata

### Workflow Execution Flow

```
Frontend → External API Gateway → Orchestration Engine → Kafka → Executors
    │           │              │                 │         │
    │           │              │                 │         ├─ Node Executor
    │           │              │                 │         └─ MCP Executor
    │           │              │                 │
    │           │              └─ State Management (Redis)
    │           └─ Authentication & Routing
    └─ Trigger execution
```

**Detailed Execution Steps**:

1. **Execution Trigger**: User clicks "Run" in frontend
2. **Payload Preparation**: Frontend builds execution payload
3. **API Gateway**: Validates request and forwards
4. **Orchestration Engine**: 
   - Receives execution request
   - Initializes workflow state in Redis
   - Analyzes dependency graph
   - Starts node execution

5. **Node Execution**:
   - Send node execution requests to Kafka
   - Node Executor consumes and processes
   - Results stored in Redis

6. **Tool Execution**:
   - MCP tool requests sent to Kafka
   - MCP Executor establishes connections
   - Executes tools on remote servers
   - Returns results

7. **State Updates**:
   - Real-time state updates via Redis
   - Execution history in PostgreSQL
   - Frontend receives updates via polling/WebSocket

### Error Handling Flow

```
Error Occurs → Error Handler → Retry Logic → Failure Recovery
     │             │              │              │
     │             │              │              ├─ Log Error
     │             │              │              ├─ Notify User
     │             │              │              └─ Archive State
     │             │              │
     │             │              └─ Exponential Backoff
     │             │
     │             └─ Error Classification
     │
     └─ Component/Service Level Error
```

---

## Communication Patterns

### 1. REST API Patterns

**Frontend ↔ API Gateway**:
```typescript
// Workflow operations
GET    /api/v1/workflows          // List workflows
POST   /api/v1/workflows          // Create workflow
GET    /api/v1/workflows/{id}     // Get workflow
PATCH  /api/v1/workflows/{id}     // Update workflow
DELETE /api/v1/workflows/{id}     // Delete workflow

// Execution operations
POST   /api/v1/execute            // Execute workflow
POST   /api/v1/workflows/{id}/execute // Execute specific workflow

// MCP operations
GET    /api/v1/mcp-tools          // List MCP tools
POST   /api/v1/mcp-tools/fetch    // Fetch tool schemas
```

**Request/Response Format**:
```json
{
  "success": true,
  "data": {
    "workflow": {
      "id": "workflow-uuid",
      "name": "Sample Workflow",
      "workflow_data": {
        "nodes": [...],
        "edges": [...]
      }
    }
  },
  "pagination": {
    "page": 1,
    "total": 100
  }
}
```

### 2. gRPC Communication

**API Gateway ↔ Workflow Service**:
```protobuf
service WorkflowService {
  rpc CreateWorkflow(CreateWorkflowRequest) returns (CreateWorkflowResponse);
  rpc GetWorkflow(GetWorkflowRequest) returns (WorkflowResponse);
  rpc UpdateWorkflow(UpdateWorkflowRequest) returns (UpdateWorkflowResponse);
  rpc ListWorkflows(ListWorkflowsRequest) returns (ListWorkflowsResponse);
}

message CreateWorkflowRequest {
  string name = 1;
  string description = 2;
  google.protobuf.Struct workflow_data = 3;
  repeated google.protobuf.Struct start_nodes = 4;
  Owner owner = 5;
  WorkflowVisibility visibility = 6;
}
```

### 3. Kafka Message Patterns

**Topic Structure**:
- `workflow-execution-requests`: Workflow execution commands
- `node-execution-requests`: Individual node execution
- `mcp-tool-requests`: MCP tool execution
- `execution-results`: Execution results and status updates

**Message Format**:
```json
{
  "correlation_id": "exec-uuid",
  "workflow_id": "workflow-uuid", 
  "node_id": "node-uuid",
  "component_type": "ApiRequestNode",
  "config": {
    "url": "https://api.example.com",
    "method": "GET"
  },
  "timestamp": "2024-07-02T10:00:00Z"
}
```

### 4. WebSocket Communication

**Real-time Updates**:
```typescript
// Frontend establishes WebSocket connection
const ws = new WebSocket('ws://localhost:8000/ws/execution/{correlationId}');

ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  // Handle execution status updates
  updateExecutionState(update);
};
```

---

## State Management

### Redis State Architecture

**Key Patterns**:
```
workflow:{workflow_id}:state         // Overall workflow state
workflow:{workflow_id}:nodes         // Node execution states  
workflow:{workflow_id}:results       // Execution results
workflow:{workflow_id}:errors        // Error information
workflow:{workflow_id}:metadata      // Execution metadata
```

**State Structure**:
```json
{
  "workflow_id": "workflow-uuid",
  "status": "running|completed|failed",
  "current_node": "node-uuid",
  "completed_nodes": ["node1", "node2"],
  "failed_nodes": [],
  "start_time": "2024-07-02T10:00:00Z",
  "execution_context": {
    "variables": {},
    "outputs": {}
  }
}
```

### PostgreSQL Data Models

**Workflows Table**:
```sql
CREATE TABLE workflows (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    workflow_data JSONB NOT NULL,
    start_node_data JSONB,
    owner_id UUID NOT NULL,
    visibility VARCHAR(20) DEFAULT 'private',
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Execution History**:
```sql
CREATE TABLE workflow_executions (
    id UUID PRIMARY KEY,
    workflow_id UUID REFERENCES workflows(id),
    correlation_id VARCHAR(255) UNIQUE,
    status VARCHAR(20),
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    execution_data JSONB,
    error_details JSONB
);
```

---

## Security Architecture

### Authentication and Authorization

**JWT Token Flow**:
1. User logs in with credentials
2. Server validates and creates JWT token
3. Token stored in Redis session store
4. All requests validated against JWT + Redis session
5. Role-based access control enforced

**Token Structure**:
```json
{
  "user_id": "user-uuid",
  "email": "<EMAIL>", 
  "role": "user|admin",
  "organization_id": "org-uuid",
  "exp": 1672531200
}
```

### Credential Management

**Secure Storage**:
- Credentials encrypted at rest
- Environment variable injection
- Secure credential retrieval for MCP tools
- Audit logging for credential access

**MCP Security**:
- SSH key management with global key store
- Connection pooling to reduce attack surface
- Encrypted communication channels
- Access control for tool execution

### Network Security

**Internal Communication**:
- gRPC with TLS in production
- Kafka with SASL authentication
- Redis with password protection
- PostgreSQL with SSL connections

**External Communication**:
- HTTPS enforcement
- CORS configuration
- Rate limiting and throttling
- Input validation and sanitization

---

## Scalability and Performance

### Horizontal Scaling Patterns

**Service Scaling**:
- Each service can scale independently
- Stateless service design enables easy replication
- Load balancing across service instances
- Auto-scaling based on CPU/memory metrics

**Database Scaling**:
- PostgreSQL read replicas for query distribution
- Redis clustering for distributed caching
- Connection pooling to optimize database connections

**Kafka Scaling**:
- Topic partitioning for parallel processing
- Consumer group scaling for increased throughput
- Producer batching for efficiency

### Performance Optimizations

**Frontend Optimizations**:
- Code splitting with Next.js
- Component lazy loading
- State management optimization
- API request batching and caching

**Backend Optimizations**:
- Async/await patterns throughout
- Database query optimization
- Redis caching strategies
- Connection pooling

**Execution Optimizations**:
- Parallel node execution
- Intelligent workflow scheduling
- Resource-aware task distribution
- Efficient state serialization

### Monitoring and Observability

**Logging Strategy**:
- Structured logging with correlation IDs
- Centralized log aggregation
- Log level configuration per service
- Performance metric logging

**Metrics Collection**:
- Service health metrics
- Execution performance metrics
- Resource utilization monitoring
- Error rate tracking

**Alerting**:
- Service availability alerts
- Performance degradation alerts
- Error threshold alerts
- Resource utilization alerts

---

## Development and Deployment

### Local Development Setup

**Prerequisites**:
```bash
# Required tools
- Docker & Docker Compose
- Python 3.11+
- Node.js 18+
- PostgreSQL 14+
- Redis 7+
- Apache Kafka
```

**Service Startup**:
```bash
# Backend services
cd workflow-service && poetry run python app/main.py
cd api-gateway && poetry run uvicorn app.main:app --reload
cd orchestration-engine && poetry run python -m app.main --mode server
cd node-executor-service && poetry run python app/main.py
cd mcp-execution-service && poetry run python app/main.py

# Frontend
cd workflow-builder-app && npm run dev
```

### Docker Deployment

**Service Configuration**:
```yaml
version: '3.8'
services:
  api-gateway:
    build: ./api-gateway
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=************************************/db
      - REDIS_URL=redis://redis:6379
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
    
  workflow-service:
    build: ./workflow-service
    ports:
      - "50056:50056"
    environment:
      - DATABASE_URL=************************************
      
  orchestration-engine:
    build: ./orchestration-engine
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - REDIS_URL=redis://redis:6379
```

### Kubernetes Deployment

**Service Manifests**:
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: api-gateway:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
```

### CI/CD Pipeline

**Build Pipeline**:
1. Code quality checks (linting, formatting)
2. Unit and integration tests
3. Security vulnerability scanning
4. Docker image building
5. Image vulnerability scanning
6. Deployment to staging
7. End-to-end testing
8. Production deployment approval
9. Production deployment with blue-green strategy

---

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Service Connection Issues

**Symptom**: Services cannot communicate with each other
**Diagnosis**:
```bash
# Check service health
curl http://localhost:8000/health

# Check gRPC connectivity
grpcurl -plaintext localhost:50056 list

# Check Kafka connectivity
kafka-console-consumer --bootstrap-server localhost:9092 --topic workflow-execution-requests
```

**Solutions**:
- Verify environment variables are set correctly
- Check Docker network connectivity
- Validate service discovery configuration
- Review firewall and port settings

#### 2. Workflow Execution Failures

**Symptom**: Workflows fail during execution
**Diagnosis**:
```bash
# Check orchestration engine logs
docker logs orchestration-engine

# Check Redis state
redis-cli
> GET workflow:{workflow_id}:state

# Check Kafka message flow
kafka-console-consumer --bootstrap-server localhost:9092 --topic execution-results
```

**Solutions**:
- Review component configuration
- Check external API connectivity
- Validate input data formats
- Verify credential configuration

#### 3. MCP Tool Execution Issues

**Symptom**: MCP tools fail to execute
**Diagnosis**:
```bash
# Check SSH connectivity
ssh -i ~/.ssh/mcp_key user@remote-server

# Check MCP service logs
docker logs mcp-execution-service

# Test tool schemas
curl -X POST http://localhost:8000/api/v1/mcp-tools/fetch
```

**Solutions**:
- Verify SSH key configuration
- Check remote server accessibility
- Validate MCP server configurations
- Review credential permissions

#### 4. Database Connection Issues

**Symptom**: Services cannot connect to databases
**Diagnosis**:
```bash
# Test PostgreSQL connection
psql -h localhost -U user -d workflow_db

# Test Redis connection
redis-cli ping

# Check connection pools
# Review service logs for connection errors
```

**Solutions**:
- Verify database credentials
- Check connection string format
- Review connection pool settings
- Validate network connectivity

### Performance Debugging

#### Slow Workflow Execution

**Investigation Steps**:
1. Check Redis response times
2. Review database query performance
3. Analyze Kafka consumer lag
4. Monitor service resource usage
5. Profile component execution times

**Optimization Strategies**:
- Increase Kafka partitions for parallelism
- Optimize database queries with indexes
- Implement connection pooling
- Scale resource-intensive services
- Use Redis clustering for distributed load

#### Memory Issues

**Monitoring**:
```bash
# Monitor service memory usage
docker stats

# Check Redis memory usage
redis-cli INFO memory

# Monitor PostgreSQL connections
SELECT count(*) FROM pg_stat_activity;
```

**Mitigation**:
- Implement memory limits in Docker
- Optimize Redis key expiration
- Review object serialization efficiency
- Scale services horizontally

### Error Code Reference

| Error Code | Category | Description | Resolution |
|------------|----------|-------------|------------|
| WF_001 | Validation | Invalid workflow schema | Review workflow definition |
| WF_002 | Execution | Node execution timeout | Increase timeout or optimize component |
| WF_003 | State | State corruption detected | Restart workflow execution |
| MCP_001 | Connection | SSH connection failed | Verify SSH credentials and connectivity |
| MCP_002 | Tool | Tool schema validation failed | Check tool parameter types |
| DB_001 | Database | Connection pool exhausted | Scale database connections |
| KF_001 | Kafka | Consumer lag detected | Scale consumer instances |

---

## Conclusion

This comprehensive architecture provides a robust, scalable platform for workflow automation with the following key strengths:

1. **Modularity**: Clear separation of concerns enables independent development and scaling
2. **Reliability**: Event-driven architecture with proper error handling and recovery
3. **Extensibility**: Plugin-based component system for easy feature additions
4. **Performance**: Parallel execution and efficient state management
5. **Security**: Comprehensive authentication, authorization, and credential management

The platform successfully addresses complex workflow automation needs while maintaining simplicity for end users through the visual workflow builder interface.

---

**Document Version**: 1.0  
**Last Updated**: July 2, 2025  
**Authors**: System Architecture Team  
**Review Cycle**: Quarterly