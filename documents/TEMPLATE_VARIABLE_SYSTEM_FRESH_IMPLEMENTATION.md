# Template Variable System - Fresh Implementation Plan

## Overview
This document outlines a fresh implementation plan for the Template Variable System based on comprehensive codebase analysis. The system will extend the existing `${variable_name}` placeholder infrastructure to support `{variable_name}` syntax across all workflow components.

## Architecture Analysis Summary

### Current Infrastructure
- **Orchestration Engine**: Already has `_process_params_for_placeholders()` method handling `${variable_name}` syntax
- **Parameter Resolution**: Three-tier approach (Handle-based → Placeholder → AI fallback)
- **Universal Input System**: Dynamic input rendering with validation in workflow-builder-app
- **Schema Conversion**: Robust pipeline between workflow_capture_schema.json and transition_schema.json
- **Component Execution**: Parameter processing through `tool_parameters` in node-executor-service

### Key Integration Points
1. **Orchestration Engine**: `workflow_utils.py` - `_process_params_for_placeholders()` method
2. **Frontend**: Universal input system with validation wrappers
3. **Schema Conversion**: Field value processing in workflow_schema_converter.py
4. **Component Execution**: Parameter extraction in node-executor-service components

## Implementation Task List

### Phase 1: Core Template Variable Engine (Backend)
- [ ] **Extend Placeholder Resolution Engine** (4-6 hours)
  - Modify `_process_params_for_placeholders()` to support `{variable_name}` syntax alongside `${variable_name}`
  - Add template variable pattern matching for both syntaxes
  - Ensure backward compatibility with existing workflows
  - Add comprehensive logging for template variable resolution

- [ ] **Create Template Variable Registry** (3-4 hours)
  - Implement workflow-scoped variable storage and retrieval
  - Add variable validation and type checking
  - Create variable lifecycle management (creation, update, deletion)
  - Add variable scope resolution (workflow-level, node-level)

- [ ] **Enhance Schema Conversion for Template Variables** (2-3 hours)
  - Update workflow_schema_converter.py to preserve template variable syntax
  - Ensure template variables are not overwritten during schema conversion
  - Add template variable detection in field values
  - Maintain null field values for handle connections while preserving template variables

### Phase 2: Frontend Integration
- [ ] **Add Template Variable Input Support** (4-5 hours)
  - Extend universal input system to recognize and highlight `{variable_name}` syntax
  - Add template variable autocomplete/suggestion functionality
  - Implement template variable validation in ValidationWrapper
  - Add visual indicators for template variables in input fields

- [ ] **Create Template Variable Management UI** (5-6 hours)
  - Design and implement template variable creation/editing interface
  - Add variable browser/picker component
  - Implement variable usage tracking and references
  - Add template variable documentation and help system

- [ ] **Enhance Input Rendering for Template Variables** (3-4 hours)
  - Update InputRenderer to handle template variable display
  - Add template variable syntax highlighting
  - Implement template variable preview/evaluation
  - Ensure proper handling of mixed content (text + template variables)

### Phase 3: Component Integration
- [ ] **Update Component Parameter Processing** (2-3 hours)
  - Ensure all components in node-executor-service properly handle template variables
  - Add template variable resolution logging in component execution
  - Test template variable processing across different component types
  - Verify template variables work with existing parameter extraction patterns

- [ ] **Add Template Variable Support to Input Helpers** (2-3 hours)
  - Update input_helpers.py to support template variable syntax
  - Ensure dual-purpose inputs work with template variables
  - Add template variable validation to input creation utilities
  - Test template variables with different input types (string, number, dropdown, etc.)

### Phase 4: Testing and Validation
- [ ] **Create Comprehensive Test Suite** (6-8 hours)
  - Unit tests for template variable resolution engine
  - Integration tests for frontend template variable functionality
  - End-to-end tests for complete workflow execution with template variables
  - Performance tests for template variable processing overhead

- [ ] **Add Template Variable Documentation** (2-3 hours)
  - Create user documentation for template variable syntax and usage
  - Add developer documentation for template variable implementation
  - Create example workflows demonstrating template variable capabilities
  - Document best practices and limitations

### Phase 5: Advanced Features
- [ ] **Implement Template Variable Validation** (3-4 hours)
  - Add real-time validation for template variable syntax
  - Implement variable existence checking
  - Add type compatibility validation between variables and input fields
  - Create validation error reporting and user feedback

- [ ] **Add Template Variable Debugging Tools** (2-3 hours)
  - Implement template variable resolution tracing
  - Add debugging interface for variable values during execution
  - Create template variable usage analytics
  - Add troubleshooting tools for template variable issues

## Estimated Timeline
- **Phase 1**: 9-13 hours (1.5-2 weeks)
- **Phase 2**: 12-15 hours (2-2.5 weeks)  
- **Phase 3**: 4-6 hours (1 week)
- **Phase 4**: 8-11 hours (1.5-2 weeks)
- **Phase 5**: 5-7 hours (1 week)

**Total Estimated Effort**: 38-52 hours (7-9 weeks)

## Risk Assessment
- **Low Risk**: Extending existing placeholder infrastructure
- **Medium Risk**: Frontend integration complexity
- **Low Risk**: Component integration (leverages existing patterns)

## Success Criteria
1. Template variables work seamlessly alongside existing handle-based connections
2. `{variable_name}` syntax is supported across all component input fields
3. Template variables are properly resolved during workflow execution
4. Frontend provides intuitive template variable creation and management
5. Comprehensive test coverage ensures reliability
6. Documentation enables easy adoption by users and developers

## Next Steps
1. Begin with Phase 1: Core Template Variable Engine implementation
2. Start with extending the placeholder resolution engine in orchestration-engine
3. Create comprehensive tests for each implemented feature
4. Iterate based on testing feedback and user requirements
