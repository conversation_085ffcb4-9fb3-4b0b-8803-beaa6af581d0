from aiokafka.errors import KafkaError  # type: ignore
from app.config.config import settings
from app.utils.enhanced_logger import get_logger
from app.utils.tool_extractor import ToolExtractor

logger = get_logger("AgentExecutor")

            "agent_config", self._get_default_agent_config()
        )

        # Extract and validate tool schemas from agent config
        agent_config = self._extract_and_validate_tool_schemas(agent_config)

        # Validate agent_type
        valid_agent_types = ["component", "employee", "A2A", "ACP"]
        if agent_type not in valid_agent_types:
            self.logger.debug(
                f"Sending request to topic '{self._request_topic}': {message_request}"
            )
            await self.producer.send(self._request_topic, value=message_request)
            await self._send_kafka_message(self._request_topic, message_request)
            self.logger.debug(
                f"Request {request_id} sent successfully using provided producer."
            )
            f"Building component agent request for execution_type: {execution_type}"
        )

        # CRITICAL FIX: Extract execution tool data from tool_parameters
        # This ensures that tool connections from the schema converter flow through to the agent platform
        enhanced_agent_config = agent_config.copy()

        # Check if tool_parameters contains execution tool data from schema conversion
        execution_tool_data = tool_parameters.get("_execution_tool_data")
        if execution_tool_data:
            self.logger.info(f"Found {len(execution_tool_data)} execution tools in tool_parameters")

            # Convert execution tool data to agent-compatible tool format
            agent_tools = []
            for tool_data in execution_tool_data:
                agent_tool = {
                    "tool_type": "workflow_component",
                    "component": {
                        "component_id": tool_data.get("component_id", ""),
                        "component_type": tool_data.get("component_type", ""),
                        "component_name": tool_data.get("component_name", ""),
                        "component_definition": tool_data.get("component_definition", {}),
                        "node_id": tool_data.get("node_id", ""),
                        "node_type": tool_data.get("node_type", ""),
                        "node_label": tool_data.get("node_label", "")
                    }
                }
                agent_tools.append(agent_tool)

            # Add tools to agent config
            if "tools" not in enhanced_agent_config:
                enhanced_agent_config["tools"] = []
            enhanced_agent_config["tools"].extend(agent_tools)

            self.logger.info(f"Enhanced agent config with {len(agent_tools)} workflow component tools")
        else:
            self.logger.debug("No execution tool data found in tool_parameters")

        # Base request structure for component agents
        message_request = {
            "agent_type": "component",
            "execution_type": execution_type,
            "agent_config": agent_config,
            "agent_config": enhanced_agent_config,  # Use enhanced config with tools
            "run_id": request_id,
            "query": query,
            "user_id": self._current_user_id or "orchestration_user",
            "tools": [],
            "capabilities": ["general_assistance", "workflow_support"],
        }

    def _extract_and_validate_tool_schemas(self, agent_config: dict) -> dict:
        """
        Extract and validate tool schemas from agent configuration.

        Args:
            agent_config: Agent configuration dictionary

        Returns:
            Agent configuration with validated tool schemas
        """
        try:
            # Initialize tool extractor
            tool_extractor = ToolExtractor()

            # Extract tools from agent config if present
            if "tools" in agent_config and agent_config["tools"]:
                self.logger.info(f"Extracting tool schemas from agent config with {len(agent_config['tools'])} tools")

                # Extract and validate tools
                extracted_tools = tool_extractor.extract_tools_from_component_data(agent_config)

                # Update agent config with validated tools
                agent_config["tools"] = extracted_tools

                self.logger.info(f"Successfully validated {len(extracted_tools)} tool schemas")
            else:
                self.logger.debug("No tools found in agent config")
                agent_config["tools"] = []

            return agent_config

        except Exception as e:
            self.logger.warning(f"Error extracting tool schemas: {str(e)}. Using original agent config.")
            # Ensure tools field exists even if extraction fails
            if "tools" not in agent_config:
                agent_config["tools"] = []
            return agent_config

    async def _send_kafka_message(self, topic: str, message: dict) -> None:
        """
        Send a message to Kafka topic.

        Args:
            topic: Kafka topic name
            message: Message dictionary to send
        """
        await self.producer.send(topic, value=message)
