"""
Tool extraction utility for orchestration engine
"""

import time
from typing import Dict, Any, List, Optional
from app.utils.enhanced_logger import get_logger

logger = get_logger("ToolExtractor")


class ToolExtractor:
    """
    Utility class for extracting tool schemas from workflow components.
    """
    
    def __init__(self):
        """Initialize the tool extractor."""
        self.logger = logger
    
    def extract_tool_schemas(self, component_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract tool schemas from component data.
        
        Args:
            component_data: Component data dictionary containing tools
            
        Returns:
            List of extracted tool schemas
        """
        return self.extract_tools_from_component_data(component_data)
    
    def extract_tools_from_component_data(self, component_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract tools from component data with validation and error handling.
        
        Args:
            component_data: Component data dictionary
            
        Returns:
            List of validated tool configurations
        """
        try:
            # Check if component has tools
            if "tools" not in component_data:
                self.logger.debug(f"No tools found in component {component_data.get('component_id', 'unknown')}")
                return []
            
            tools = component_data["tools"]
            if not isinstance(tools, list):
                self.logger.warning(f"Tools field is not a list in component {component_data.get('component_id', 'unknown')}")
                return []
            
            extracted_tools = []
            
            for i, tool in enumerate(tools):
                try:
                    validated_tool = self._validate_and_extract_tool(tool, i)
                    if validated_tool:
                        extracted_tools.append(validated_tool)
                except Exception as e:
                    self.logger.warning(f"Failed to extract tool {i}: {str(e)}")
                    continue
            
            self.logger.info(f"Extracted {len(extracted_tools)} tools from component {component_data.get('component_id', 'unknown')}")
            return extracted_tools
            
        except Exception as e:
            self.logger.error(f"Error extracting tools from component data: {str(e)}")
            return []
    
    def _validate_and_extract_tool(self, tool: Dict[str, Any], index: int) -> Optional[Dict[str, Any]]:
        """
        Validate and extract a single tool configuration.
        
        Args:
            tool: Tool configuration dictionary
            index: Tool index for error reporting
            
        Returns:
            Validated tool configuration or None if invalid
        """
        if not isinstance(tool, dict):
            self.logger.warning(f"Tool {index} is not a dictionary")
            return None
        
        # Validate required fields
        if "tool_type" not in tool:
            self.logger.warning(f"Tool {index} missing tool_type field")
            return None
        
        if "component" not in tool:
            self.logger.warning(f"Tool {index} missing component field")
            return None
        
        component = tool["component"]
        if not isinstance(component, dict):
            self.logger.warning(f"Tool {index} component field is not a dictionary")
            return None
        
        # Validate component fields
        required_component_fields = ["component_id", "component_type", "component_name"]
        for field in required_component_fields:
            if field not in component:
                self.logger.warning(f"Tool {index} component missing required field: {field}")
                return None
        
        # Validate component_schema if present
        if "component_schema" in component:
            schema = component["component_schema"]
            if not isinstance(schema, dict):
                self.logger.warning(f"Tool {index} component_schema is not a dictionary")
                return None
            
            # Validate AutoGen schema structure
            if not self._validate_autogen_schema(schema, index):
                return None
        
        # Tool is valid, return it
        return tool
    
    def _validate_autogen_schema(self, schema: Dict[str, Any], tool_index: int) -> bool:
        """
        Validate AutoGen tool schema structure.
        
        Args:
            schema: Tool schema dictionary
            tool_index: Tool index for error reporting
            
        Returns:
            True if schema is valid, False otherwise
        """
        required_fields = ["name", "description", "parameters"]
        for field in required_fields:
            if field not in schema:
                self.logger.warning(f"Tool {tool_index} schema missing required field: {field}")
                return False
        
        # Validate parameters structure
        parameters = schema["parameters"]
        if not isinstance(parameters, dict):
            self.logger.warning(f"Tool {tool_index} schema parameters is not a dictionary")
            return False
        
        # Check for required parameter fields
        param_required_fields = ["type", "properties"]
        for field in param_required_fields:
            if field not in parameters:
                self.logger.warning(f"Tool {tool_index} schema parameters missing required field: {field}")
                return False
        
        # Validate parameter type
        if parameters["type"] != "object":
            self.logger.warning(f"Tool {tool_index} schema parameters type must be 'object'")
            return False
        
        # Validate properties structure
        properties = parameters["properties"]
        if not isinstance(properties, dict):
            self.logger.warning(f"Tool {tool_index} schema parameters properties is not a dictionary")
            return False
        
        return True
    
    def extract_tools_with_performance_tracking(self, component_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract tools with performance tracking for monitoring.
        
        Args:
            component_data: Component data dictionary
            
        Returns:
            Dictionary containing extracted tools and performance metrics
        """
        start_time = time.time()
        
        extracted_tools = self.extract_tools_from_component_data(component_data)
        
        end_time = time.time()
        extraction_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        result = {
            "tools": extracted_tools,
            "extraction_time_ms": extraction_time,
            "tool_count": len(extracted_tools),
            "component_id": component_data.get("component_id", "unknown")
        }
        
        self.logger.debug(f"Tool extraction completed in {extraction_time:.2f}ms for {len(extracted_tools)} tools")
        
        return result
    
    def extract_mcp_tools(self, component_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract MCP marketplace component tools specifically.
        
        Args:
            component_data: Component data dictionary
            
        Returns:
            List of MCP tool configurations
        """
        all_tools = self.extract_tools_from_component_data(component_data)
        
        # Filter for MCP components
        mcp_tools = []
        for tool in all_tools:
            component = tool.get("component", {})
            if component.get("component_type") == "MCPMarketplace":
                mcp_tools.append(tool)
        
        self.logger.info(f"Extracted {len(mcp_tools)} MCP tools from component {component_data.get('component_id', 'unknown')}")
        return mcp_tools
    
    def extract_regular_tools(self, component_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract regular (non-MCP) workflow component tools.
        
        Args:
            component_data: Component data dictionary
            
        Returns:
            List of regular tool configurations
        """
        all_tools = self.extract_tools_from_component_data(component_data)
        
        # Filter for non-MCP components
        regular_tools = []
        for tool in all_tools:
            component = tool.get("component", {})
            if component.get("component_type") != "MCPMarketplace":
                regular_tools.append(tool)
        
        self.logger.info(f"Extracted {len(regular_tools)} regular tools from component {component_data.get('component_id', 'unknown')}")
        return regular_tools
    
    def validate_extracted_tools(self, tools: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate a list of extracted tools and provide validation report.
        
        Args:
            tools: List of tool configurations
            
        Returns:
            Validation report dictionary
        """
        validation_report = {
            "total_tools": len(tools),
            "valid_tools": 0,
            "invalid_tools": 0,
            "validation_errors": [],
            "tool_types": {},
            "component_types": {}
        }
        
        for i, tool in enumerate(tools):
            try:
                if self._validate_and_extract_tool(tool, i):
                    validation_report["valid_tools"] += 1
                    
                    # Track tool types
                    tool_type = tool.get("tool_type", "unknown")
                    validation_report["tool_types"][tool_type] = validation_report["tool_types"].get(tool_type, 0) + 1
                    
                    # Track component types
                    component_type = tool.get("component", {}).get("component_type", "unknown")
                    validation_report["component_types"][component_type] = validation_report["component_types"].get(component_type, 0) + 1
                else:
                    validation_report["invalid_tools"] += 1
                    validation_report["validation_errors"].append(f"Tool {i} failed validation")
            except Exception as e:
                validation_report["invalid_tools"] += 1
                validation_report["validation_errors"].append(f"Tool {i} validation error: {str(e)}")
        
        return validation_report


def extract_tool_schemas_from_agent_config(agent_config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Convenience function to extract tool schemas from agent configuration.
    
    Args:
        agent_config: Agent configuration dictionary
        
    Returns:
        List of extracted tool schemas
    """
    extractor = ToolExtractor()
    return extractor.extract_tool_schemas(agent_config)


def validate_tool_extraction_performance(component_data: Dict[str, Any], max_time_ms: float = 100.0) -> bool:
    """
    Validate that tool extraction meets performance requirements.
    
    Args:
        component_data: Component data dictionary
        max_time_ms: Maximum allowed extraction time in milliseconds
        
    Returns:
        True if performance requirements are met, False otherwise
    """
    extractor = ToolExtractor()
    result = extractor.extract_tools_with_performance_tracking(component_data)
    
    extraction_time = result["extraction_time_ms"]
    meets_requirement = extraction_time <= max_time_ms
    
    if not meets_requirement:
        logger.warning(f"Tool extraction took {extraction_time:.2f}ms, exceeds limit of {max_time_ms}ms")
    
    return meets_requirement
