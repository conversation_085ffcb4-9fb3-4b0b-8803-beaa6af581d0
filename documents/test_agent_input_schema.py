#!/usr/bin/env python3

import sys
import json
import os

# Add the workflow-service to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'workflow-service'))

def test_agent_input_schema_cleanup():
    """Test if tool-related inputs are properly excluded from agent input schema"""
    print("🔍 Testing agent input schema cleanup...")
    
    try:
        # Load the agentic schema
        with open('agentic_schema.json', 'r') as f:
            agentic_schema = json.load(f)
        
        workflow_data = agentic_schema.get('workflow_data', {})
        
        # Import the conversion function
        from app.services.workflow_builder.workflow_schema_converter import convert_workflow_to_transition_schema
        
        # Convert to transition schema
        result = convert_workflow_to_transition_schema(workflow_data)
        
        print(f"\n🎉 CONVERSION SUCCESSFUL!")
        
        # Find the agent node in the nodes array
        agent_node = None
        for node in result['nodes']:
            if node['server_tools'][0]['tool_name'] == 'AgenticAI':
                agent_node = node
                break
        
        if not agent_node:
            print("❌ No AgenticAI node found!")
            return False
        
        print(f"✅ Found AgenticAI node")
        
        # Check the input schema
        input_schema = agent_node['server_tools'][0]['input_schema']
        predefined_fields = input_schema.get('predefined_fields', [])
        
        print(f"\n🔍 AGENT INPUT SCHEMA ANALYSIS:")
        print(f"   - Total input fields: {len(predefined_fields)}")
        
        # List all fields
        field_names = []
        for field in predefined_fields:
            field_name = field.get('field_name', 'unknown')
            field_names.append(field_name)
            required = field.get('required', False)
            data_type = field.get('data_type', {}).get('type', 'unknown')
            print(f"   - {field_name} ({data_type}) {'[REQUIRED]' if required else '[OPTIONAL]'}")
        
        # Check for problematic fields
        problematic_fields = []
        
        # Check if tools field is present
        if 'tools' in field_names:
            problematic_fields.append('tools')
        
        # Check for any other tool-related fields
        tool_related_fields = [f for f in field_names if 'tool' in f.lower()]
        problematic_fields.extend(tool_related_fields)
        
        print(f"\n📋 VALIDATION RESULTS:")
        
        if problematic_fields:
            print(f"❌ PROBLEM: Found {len(problematic_fields)} tool-related fields in agent input schema:")
            for field in problematic_fields:
                print(f"   - {field} (should be excluded)")
            return False
        else:
            print(f"✅ SUCCESS: No tool-related fields found in agent input schema!")
        
        # Verify expected legitimate fields are present
        expected_fields = ['query', 'model_provider', 'execution_type', 'input_variables']
        missing_expected = [f for f in expected_fields if f not in field_names]
        
        if missing_expected:
            print(f"⚠️  WARNING: Some expected fields are missing:")
            for field in missing_expected:
                print(f"   - {field}")
        else:
            print(f"✅ All expected legitimate workflow input fields are present")
        
        # Check agent configuration has tools
        agent_transition = None
        for transition in result['transitions']:
            if transition['execution_type'] == 'agent':
                agent_transition = transition
                break
        
        if agent_transition:
            tools_to_use = agent_transition['node_info']['tools_to_use']
            for tool in tools_to_use:
                if tool['tool_name'] == 'AgenticAI':
                    tool_params = tool['tool_params']['items']
                    for item in tool_params:
                        if item['field_name'] == 'agent_config':
                            agent_config = item['field_value']
                            if 'agent_tools' in agent_config:
                                tool_count = len(agent_config['agent_tools'])
                                print(f"✅ Agent configuration contains {tool_count} integrated tools")
                            else:
                                print(f"❌ Agent configuration missing agent_tools")
                            break
                    break
        
        return len(problematic_fields) == 0
        
    except Exception as e:
        print(f"❌ Error in agent input schema test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting agent input schema cleanup test...")
    print("=" * 60)
    
    success = test_agent_input_schema_cleanup()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 AGENT INPUT SCHEMA TEST PASSED!")
        print("   ✅ Tool-related inputs properly excluded from agent input schema")
        print("   ✅ Tools properly integrated into agent configuration")
        print("   ✅ Agent input schema only contains legitimate workflow inputs")
    else:
        print("❌ AGENT INPUT SCHEMA TEST FAILED!")
        print("   ❌ Tool-related inputs still present in agent input schema")
        print("   ❌ Architecture violation: tools should be callable functions, not input sources")
