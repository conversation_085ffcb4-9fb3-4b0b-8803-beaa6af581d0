"""
Comprehensive test suite for all type conversion fixes
Tests all components with string/number conversion issues
"""

import pytest
import os
import sys
import json

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.utils.type_conversion import safe_int_convert, safe_float_convert, safe_bool_convert
from app.services.workflow_builder.workflow_schema_converter import convert_workflow_to_transition_schema

class TestTypeConversionComprehensive:
    """Test all components for proper type conversion"""
    
    def test_safe_int_convert_valid_strings(self):
        """Test converting valid string numbers to integers"""
        assert safe_int_convert("60", 30) == 60
        assert safe_int_convert("0", 30) == 0
        assert safe_int_convert("999", 30) == 999
        assert safe_int_convert("-5", 30) == -5
        
    def test_safe_int_convert_invalid_strings(self):
        """Test converting invalid strings with fallback"""
        assert safe_int_convert("invalid", 30) == 30
        assert safe_int_convert("", 30) == 30
        assert safe_int_convert(None, 30) == 30
        assert safe_int_convert("12.5", 30) == 30  # Float strings should fallback
        
    def test_safe_int_convert_numeric_values(self):
        """Test converting numeric values"""
        assert safe_int_convert(60, 30) == 60
        assert safe_int_convert(0, 30) == 0
        assert safe_int_convert(60.0, 30) == 60  # Float to int
        assert safe_int_convert(-5, 30) == -5
        
    def test_safe_float_convert_all_cases(self):
        """Test float conversion with all edge cases"""
        assert safe_float_convert("1.5", 0.0) == 1.5
        assert safe_float_convert("60", 0.0) == 60.0
        assert safe_float_convert("invalid", 0.0) == 0.0
        assert safe_float_convert(None, 0.0) == 0.0
        assert safe_float_convert(1.5, 0.0) == 1.5
        assert safe_float_convert(60, 0.0) == 60.0
        
    def test_safe_bool_convert_all_cases(self):
        """Test boolean conversion with all edge cases"""
        # String true values
        assert safe_bool_convert("true", False) == True
        assert safe_bool_convert("True", False) == True
        assert safe_bool_convert("1", False) == True
        assert safe_bool_convert("yes", False) == True
        assert safe_bool_convert("on", False) == True
        
        # String false values
        assert safe_bool_convert("false", True) == False
        assert safe_bool_convert("False", True) == False
        assert safe_bool_convert("0", True) == False
        assert safe_bool_convert("no", True) == False
        
        # Boolean values
        assert safe_bool_convert(True, False) == True
        assert safe_bool_convert(False, True) == False
        
        # Invalid values
        assert safe_bool_convert("invalid", True) == False
        assert safe_bool_convert(None, True) == True
        assert safe_bool_convert("", True) == True
        
    def test_loop_node_timeout_conversion(self):
        """Test LoopNode iteration_timeout converts string to int"""
        workflow_data = {
            "nodes": [{
                "id": "loop_1",
                "type": "LoopNode",
                "loop_config": {
                    "iteration_timeout": "60",     # String input
                    "max_concurrent": "3",        # String input
                    "parallel_execution": "true", # String input
                    "preserve_order": "false"     # String input
                }
            }]
        }
        
        result = convert_workflow_to_transition_schema(workflow_data)
        
        # Find the loop transition
        loop_transition = None
        for transition in result["transitions"]:
            if transition.get("execution_type") == "loop":
                loop_transition = transition
                break
        
        assert loop_transition is not None, "Loop transition not found"
        
        # Verify all fields are converted to proper types
        loop_config = loop_transition["loop_config"]
        assert isinstance(loop_config["iteration_timeout"], int)
        assert loop_config["iteration_timeout"] == 60
        assert isinstance(loop_config["max_concurrent"], int)
        assert loop_config["max_concurrent"] == 3
        assert isinstance(loop_config["parallel_execution"], bool)
        assert loop_config["parallel_execution"] == True
        assert isinstance(loop_config["preserve_order"], bool)
        assert loop_config["preserve_order"] == False
        
    def test_backward_compatibility_with_numeric_inputs(self):
        """Test that existing numeric inputs still work"""
        workflow_data = {
            "nodes": [{
                "id": "loop_1",
                "type": "LoopNode", 
                "loop_config": {
                    "iteration_timeout": 60,      # Integer input (existing)
                    "max_concurrent": 3,          # Integer input (existing)
                    "parallel_execution": True,   # Boolean input (existing)
                    "preserve_order": False       # Boolean input (existing)
                }
            }]
        }
        
        result = convert_workflow_to_transition_schema(workflow_data)
        
        # Find the loop transition
        loop_transition = None
        for transition in result["transitions"]:
            if transition.get("execution_type") == "loop":
                loop_transition = transition
                break
        
        assert loop_transition is not None, "Loop transition not found"
        
        # Verify all fields remain the correct types
        loop_config = loop_transition["loop_config"]
        assert isinstance(loop_config["iteration_timeout"], int)
        assert loop_config["iteration_timeout"] == 60
        assert isinstance(loop_config["max_concurrent"], int)
        assert loop_config["max_concurrent"] == 3
        assert isinstance(loop_config["parallel_execution"], bool)
        assert loop_config["parallel_execution"] == True
        assert isinstance(loop_config["preserve_order"], bool)
        assert loop_config["preserve_order"] == False
        
    def test_mixed_input_types(self):
        """Test workflows with mixed string and numeric inputs"""
        workflow_data = {
            "nodes": [{
                "id": "loop_1", 
                "type": "LoopNode",
                "loop_config": {
                    "iteration_timeout": "45",    # String input
                    "max_concurrent": 5,          # Integer input
                    "parallel_execution": True,   # Boolean input
                    "preserve_order": "true"      # String input
                }
            }]
        }
        
        result = convert_workflow_to_transition_schema(workflow_data)
        
        # Find the loop transition
        loop_transition = None
        for transition in result["transitions"]:
            if transition.get("execution_type") == "loop":
                loop_transition = transition
                break
        
        assert loop_transition is not None, "Loop transition not found"
        
        # Verify all fields are converted to proper types regardless of input type
        loop_config = loop_transition["loop_config"]
        assert isinstance(loop_config["iteration_timeout"], int)
        assert loop_config["iteration_timeout"] == 45
        assert isinstance(loop_config["max_concurrent"], int)
        assert loop_config["max_concurrent"] == 5
        assert isinstance(loop_config["parallel_execution"], bool)
        assert loop_config["parallel_execution"] == True
        assert isinstance(loop_config["preserve_order"], bool)
        assert loop_config["preserve_order"] == True
        
    def test_invalid_input_fallbacks(self):
        """Test that invalid inputs fallback to safe defaults"""
        workflow_data = {
            "nodes": [{
                "id": "loop_1",
                "type": "LoopNode",
                "loop_config": {
                    "iteration_timeout": "invalid",  # Invalid string
                    "max_concurrent": "not_a_number", # Invalid string
                    "parallel_execution": "maybe",   # Invalid boolean string
                    "preserve_order": "unknown"      # Invalid boolean string
                }
            }]
        }
        
        result = convert_workflow_to_transition_schema(workflow_data)
        
        # Find the loop transition
        loop_transition = None
        for transition in result["transitions"]:
            if transition.get("execution_type") == "loop":
                loop_transition = transition
                break
        
        assert loop_transition is not None, "Loop transition not found"
        
        # Verify all fields fallback to safe defaults
        loop_config = loop_transition["loop_config"]
        assert isinstance(loop_config["iteration_timeout"], int)
        assert loop_config["iteration_timeout"] == 30  # Default fallback
        assert isinstance(loop_config["max_concurrent"], int)
        assert loop_config["max_concurrent"] == 3  # Default fallback
        assert isinstance(loop_config["parallel_execution"], bool)
        assert loop_config["parallel_execution"] == False  # Default fallback
        assert isinstance(loop_config["preserve_order"], bool)
        assert loop_config["preserve_order"] == True  # Default fallback


if __name__ == "__main__":
    # Run tests directly if pytest is not available
    test_instance = TestTypeConversionComprehensive()
    
    print("Running comprehensive type conversion tests...")
    
    try:
        test_instance.test_safe_int_convert_valid_strings()
        print("✅ test_safe_int_convert_valid_strings PASSED")
        
        test_instance.test_safe_int_convert_invalid_strings()
        print("✅ test_safe_int_convert_invalid_strings PASSED")
        
        test_instance.test_safe_int_convert_numeric_values()
        print("✅ test_safe_int_convert_numeric_values PASSED")
        
        test_instance.test_safe_float_convert_all_cases()
        print("✅ test_safe_float_convert_all_cases PASSED")
        
        test_instance.test_safe_bool_convert_all_cases()
        print("✅ test_safe_bool_convert_all_cases PASSED")
        
        # Note: Workflow tests will initially fail until we fix the schema converter
        print("\n🔄 Testing workflow conversion (may fail initially)...")
        try:
            test_instance.test_loop_node_timeout_conversion()
            print("✅ test_loop_node_timeout_conversion PASSED")
        except Exception as e:
            print(f"❌ test_loop_node_timeout_conversion FAILED (expected): {e}")
            
        try:
            test_instance.test_backward_compatibility_with_numeric_inputs()
            print("✅ test_backward_compatibility_with_numeric_inputs PASSED")
        except Exception as e:
            print(f"❌ test_backward_compatibility_with_numeric_inputs FAILED: {e}")
            
        print("\n🎉 Type conversion utility tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()