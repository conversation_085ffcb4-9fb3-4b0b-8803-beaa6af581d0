import pytest
import json
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.services.workflow_functions import WorkflowFunctions
from app.grpc_ import workflow_pb2
from app.utils.constants.constants import WorkflowVisibilityEnum, WorkflowStatusEnum
from app.helpers.helpers import _create_marketplace_listing_from_workflow


class TestToggleVisibilityLatestVersion:
    """Test that toggleWorkflowVisibility creates marketplace listing with latest version"""

    @pytest.fixture
    def db_session(self):
        """Create a test database session - NO CLEANUP TO PREVENT DATA LOSS"""
        db = SessionLocal()
        try:
            yield db
        finally:
            # NO CLEANUP - Leaving test data to prevent accidental deletion of production data
            # Test data will have test-specific names/IDs that won't conflict with real data
            db.close()

    @pytest.fixture
    def sample_workflow_data(self):
        """Sample workflow data for testing"""
        return {
            "nodes": [
                {
                    "id": "start_node",
                    "type": "start_node",
                    "data": {"label": "Start"},
                    "position": {"x": 100, "y": 100}
                }
            ],
            "edges": []
        }

    def test_toggle_visibility_uses_latest_version(self, db_session: Session, sample_workflow_data):
        """Test that toggleWorkflowVisibility creates marketplace listing with latest version"""
        
        # Create a workflow with initial version (v1.0.0)
        workflow = Workflow(
            name="Test Workflow",
            description="Test Description",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start_node"}],
            available_nodes=[],
            owner_id="test_user_123",
            owner_type="user",
            visibility=WorkflowVisibilityEnum.PRIVATE,
            status=WorkflowStatusEnum.ACTIVE,
            is_updated=False,
            is_customizable=True,
            tags=["test"]
        )
        
        db_session.add(workflow)
        db_session.flush()
        
        # Create v1.0.0 version
        v1_version = WorkflowVersion(
            workflow_id=workflow.id,
            version_number="1.0.0",
            name="Test Workflow",
            description="Initial version",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start_node"}],
            available_nodes=[],
            category=None,
            tags=["test"],
            changelog="Initial version",
            created_at=datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc)
        )
        
        db_session.add(v1_version)
        db_session.flush()
        
        # Set current version to v1.0.0
        workflow.current_version_id = v1_version.id
        
        # Create v1.1.0 version (latest) - created later
        v1_1_version = WorkflowVersion(
            workflow_id=workflow.id,
            version_number="1.1.0",
            name="Test Workflow Updated",
            description="Updated version",
            workflow_url="https://example.com/workflow-v1.1.json",
            builder_url="https://example.com/builder-v1.1.json",
            start_nodes=[{"id": "start", "type": "start_node"}, {"id": "new_node", "type": "process"}],
            available_nodes=[],
            category=None,
            tags=["test", "updated"],
            changelog="Added new features",
            created_at=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc)  # Later timestamp
        )
        
        db_session.add(v1_1_version)
        db_session.flush()
        
        # Create v1.2.0 version (latest) - created even later
        v1_2_version = WorkflowVersion(
            workflow_id=workflow.id,
            version_number="1.2.0",
            name="Test Workflow Latest",
            description="Latest version",
            workflow_url="https://example.com/workflow-v1.2.json",
            builder_url="https://example.com/builder-v1.2.json",
            start_nodes=[{"id": "start", "type": "start_node"}, {"id": "new_node", "type": "process"}, {"id": "final_node", "type": "end"}],
            available_nodes=[],
            category=None,
            tags=["test", "updated", "latest"],
            changelog="Final improvements",
            created_at=datetime(2024, 1, 3, 10, 0, 0, tzinfo=timezone.utc)  # Latest timestamp
        )
        
        db_session.add(v1_2_version)
        db_session.flush()
        
        # NOTE: current_version_id is still pointing to v1.0.0, not the latest version
        # This simulates the scenario where user has made updates but hasn't created a new version yet
        
        db_session.commit()
        db_session.refresh(workflow)
        
        # Verify setup: current_version_id should be v1.0.0, but latest version should be v1.2.0
        assert workflow.current_version_id == v1_version.id
        
        # Get latest version by created_at
        latest_version = (
            db_session.query(WorkflowVersion)
            .filter(WorkflowVersion.workflow_id == workflow.id)
            .order_by(WorkflowVersion.created_at.desc())
            .first()
        )
        assert latest_version.id == v1_2_version.id
        assert latest_version.version_number == "1.2.0"
        
        # Now test the marketplace listing creation
        # This should use the CURRENT WORKFLOW STATE, not any specific version data
        marketplace_listing = _create_marketplace_listing_from_workflow(db_session, workflow)
        
        # Verify the marketplace listing was created
        assert marketplace_listing is not None
        
        # The key assertion: marketplace listing should reference the latest version for FK
        # but use current workflow state for content
        assert marketplace_listing.workflow_version_id == v1_2_version.id, (
            f"Expected marketplace listing to reference latest version {v1_2_version.id} (v1.2.0), "
            f"but got {marketplace_listing.workflow_version_id}"
        )
        
        # Verify fields come from CURRENT WORKFLOW STATE, not version data
        assert marketplace_listing.title == workflow.name  # "Test Workflow" from current workflow
        assert marketplace_listing.description == workflow.description  # "Test Description" from current workflow
        assert marketplace_listing.tags == workflow.tags  # ["test"] from current workflow
        
        print(f"✅ Test passed: Marketplace listing correctly uses current workflow state with latest version reference")

    def test_toggle_visibility_endpoint_uses_latest_version(self, db_session: Session, sample_workflow_data):
        """Test the actual toggleWorkflowVisibility endpoint uses latest version"""
        
        # Create a workflow with multiple versions
        workflow = Workflow(
            name="Test Workflow",
            description="Test Description",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start_node"}],
            available_nodes=[],
            owner_id="test_user_123",
            owner_type="user",
            visibility=WorkflowVisibilityEnum.PRIVATE,
            status=WorkflowStatusEnum.ACTIVE,
            is_updated=False,
            is_customizable=True,
            tags=["test"]
        )
        
        db_session.add(workflow)
        db_session.flush()
        
        # Create v1.0.0 version (older)
        v1_version = WorkflowVersion(
            workflow_id=workflow.id,
            version_number="1.0.0",
            name="Test Workflow",
            description="Initial version",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start_node"}],
            available_nodes=[],
            changelog="Initial version",
            created_at=datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc)
        )
        
        db_session.add(v1_version)
        db_session.flush()
        
        # Set current version to v1.0.0
        workflow.current_version_id = v1_version.id
        
        # Create v2.0.0 version (latest)
        v2_version = WorkflowVersion(
            workflow_id=workflow.id,
            version_number="2.0.0",
            name="Test Workflow Major Update",
            description="Major update version",
            workflow_url="https://example.com/workflow-v2.json",
            builder_url="https://example.com/builder-v2.json",
            start_nodes=[{"id": "start", "type": "start_node"}, {"id": "major_update", "type": "process"}],
            available_nodes=[],
            changelog="Major update with new features",
            created_at=datetime(2024, 1, 5, 10, 0, 0, tzinfo=timezone.utc)  # Latest timestamp
        )
        
        db_session.add(v2_version)
        db_session.commit()
        
        # Create the workflow service
        workflow_service = WorkflowFunctions()

        # Create the toggle visibility request
        owner = workflow_pb2.Owner()
        owner.id = "test_user_123"

        request = workflow_pb2.ToggleWorkflowVisibilityRequest()
        request.workflow_id = workflow.id
        request.owner.CopyFrom(owner)
        
        # Mock gRPC context
        class MockContext:
            def set_code(self, code):
                self.code = code
            def set_details(self, details):
                self.details = details
        
        context = MockContext()
        
        # Call toggleWorkflowVisibility
        response = workflow_service.toggleWorkflowVisibility(request, context)
        
        # Verify the response was successful
        assert response.success == True
        assert "PUBLIC" in response.message
        
        # Check that marketplace listing was created with latest version
        marketplace_listing = (
            db_session.query(WorkflowMarketplaceListing)
            .filter(WorkflowMarketplaceListing.workflow_id == workflow.id)
            .first()
        )
        
        assert marketplace_listing is not None
        
        # The key assertion: should use latest version (v2.0.0), not current version (v1.0.0)
        assert marketplace_listing.workflow_version_id == v2_version.id, (
            f"Expected marketplace listing to use latest version {v2_version.id} (v2.0.0), "
            f"but got {marketplace_listing.workflow_version_id}"
        )
        
        # Verify fields come from CURRENT WORKFLOW STATE, not version data
        assert marketplace_listing.title == workflow.name  # "Test Workflow" from current workflow
        assert marketplace_listing.description == workflow.description  # "Test Description" from current workflow
        
        print(f"✅ Test passed: toggleWorkflowVisibility correctly uses current workflow state with latest version reference")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])