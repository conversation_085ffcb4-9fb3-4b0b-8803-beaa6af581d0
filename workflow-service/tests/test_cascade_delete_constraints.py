"""
Test CASCADE DELETE constraints for workflow relationships
"""
import pytest
import os
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine, text
from app.models.workflow import Base, Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.utils.constants.constants import (
    WorkflowOwnerTypeEnum,
    WorkflowVisibilityEnum,
    WorkflowStatusEnum,
    WorkflowCategoryEnum,
)


@pytest.fixture
def db_session():
    """Create a test PostgreSQL database for testing CASCADE DELETE constraints"""
    # Use PostgreSQL for proper ARRAY and CASCADE DELETE testing
    database_url = os.getenv("TEST_DATABASE_URL", "postgresql://postgres:password@localhost:5432/test_workflow_service")
    
    try:
        engine = create_engine(database_url)
        # Test connection
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        
        # Drop and recreate all tables for clean test
        Base.metadata.drop_all(engine)
        Base.metadata.create_all(engine)
        
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
        
        # Clean up after test
        Base.metadata.drop_all(engine)
        
    except Exception as e:
        # If PostgreSQL is not available, skip the tests
        pytest.skip(f"PostgreSQL database not available for CASCADE DELETE testing: {e}")


def test_delete_cloned_workflow_without_affecting_source(db_session):
    """
    Test that deleting a cloned workflow doesn't affect the source workflow
    """
    # Create source workflow
    source_workflow = Workflow(
        name="Source Workflow",
        description="Original workflow",
        workflow_url="http://example.com/source",
        builder_url="http://example.com/source/builder",
        owner_id="source_owner",
        owner_type=WorkflowOwnerTypeEnum.USER,
        visibility=WorkflowVisibilityEnum.PUBLIC,
    )
    db_session.add(source_workflow)
    db_session.commit()
    
    # Create source version
    source_version = WorkflowVersion(
        workflow_id=source_workflow.id,
        name="Source Workflow",
        workflow_url="http://example.com/source",
        builder_url="http://example.com/source/builder",
        version_number="1.0.0",
    )
    db_session.add(source_version)
    db_session.commit()
    
    # Update source workflow to reference current version
    source_workflow.current_version_id = source_version.id
    db_session.commit()
    
    # Create cloned workflow
    cloned_workflow = Workflow(
        name="Cloned Workflow",
        description="Cloned from source",
        workflow_url="http://example.com/cloned",
        builder_url="http://example.com/cloned/builder",
        owner_id="cloned_owner",
        owner_type=WorkflowOwnerTypeEnum.USER,
        is_imported=True,
        workflow_template_id=source_workflow.id,
        source_version_id=source_version.id,
        visibility=WorkflowVisibilityEnum.PRIVATE,
    )
    db_session.add(cloned_workflow)
    db_session.commit()
    
    # Create cloned version
    cloned_version = WorkflowVersion(
        workflow_id=cloned_workflow.id,
        name="Cloned Workflow",
        workflow_url="http://example.com/cloned",
        builder_url="http://example.com/cloned/builder",
        version_number="1.0.0",
    )
    db_session.add(cloned_version)
    db_session.commit()
    
    # Update cloned workflow to reference current version
    cloned_workflow.current_version_id = cloned_version.id
    db_session.commit()
    
    # Verify relationships are set up correctly
    assert cloned_workflow.workflow_template_id == source_workflow.id
    assert cloned_workflow.source_version_id == source_version.id
    assert len(source_workflow.derived_workflows) == 1
    assert source_workflow.derived_workflows[0].id == cloned_workflow.id
    
    # Delete the cloned workflow
    db_session.delete(cloned_workflow)
    db_session.commit()
    
    # Verify source workflow and version still exist
    remaining_source = db_session.query(Workflow).filter_by(id=source_workflow.id).first()
    remaining_source_version = db_session.query(WorkflowVersion).filter_by(id=source_version.id).first()
    
    assert remaining_source is not None
    assert remaining_source_version is not None
    assert remaining_source.name == "Source Workflow"
    assert remaining_source_version.version_number == "1.0.0"
    
    # Verify cloned workflow is deleted
    deleted_cloned = db_session.query(Workflow).filter_by(id=cloned_workflow.id).first()
    assert deleted_cloned is None


def test_delete_source_workflow_sets_references_to_null(db_session):
    """
    Test that deleting a source workflow sets workflow_template_id to NULL in cloned workflows
    """
    # Create source workflow
    source_workflow = Workflow(
        name="Source Workflow",
        description="Original workflow",
        workflow_url="http://example.com/source",
        builder_url="http://example.com/source/builder",
        owner_id="source_owner",
        owner_type=WorkflowOwnerTypeEnum.USER,
        visibility=WorkflowVisibilityEnum.PUBLIC,
    )
    db_session.add(source_workflow)
    db_session.commit()
    
    # Create source version
    source_version = WorkflowVersion(
        workflow_id=source_workflow.id,
        name="Source Workflow",
        workflow_url="http://example.com/source",
        builder_url="http://example.com/source/builder",
        version_number="1.0.0",
    )
    db_session.add(source_version)
    db_session.commit()
    
    # Create cloned workflow
    cloned_workflow = Workflow(
        name="Cloned Workflow",
        description="Cloned from source",
        workflow_url="http://example.com/cloned",
        builder_url="http://example.com/cloned/builder",
        owner_id="cloned_owner",
        owner_type=WorkflowOwnerTypeEnum.USER,
        is_imported=True,
        workflow_template_id=source_workflow.id,
        source_version_id=source_version.id,
        visibility=WorkflowVisibilityEnum.PRIVATE,
    )
    db_session.add(cloned_workflow)
    db_session.commit()
    
    # Verify relationships are set up
    assert cloned_workflow.workflow_template_id == source_workflow.id
    assert cloned_workflow.source_version_id == source_version.id
    
    # Delete the source workflow (this should cascade delete the source version too)
    db_session.delete(source_workflow)
    db_session.commit()
    
    # Refresh the cloned workflow from database
    db_session.refresh(cloned_workflow)
    
    # Verify cloned workflow still exists but references are set to NULL
    assert cloned_workflow.workflow_template_id is None
    assert cloned_workflow.source_version_id is None
    assert cloned_workflow.name == "Cloned Workflow"  # Cloned workflow should still exist


def test_delete_version_sets_current_version_to_null(db_session):
    """
    Test that deleting a version sets current_version_id to NULL in workflows
    """
    # Create workflow
    workflow = Workflow(
        name="Test Workflow",
        description="Test workflow",
        workflow_url="http://example.com/test",
        builder_url="http://example.com/test/builder",
        owner_id="test_owner",
        owner_type=WorkflowOwnerTypeEnum.USER,
        visibility=WorkflowVisibilityEnum.PRIVATE,
    )
    db_session.add(workflow)
    db_session.commit()
    
    # Create version
    version = WorkflowVersion(
        workflow_id=workflow.id,
        name="Test Workflow",
        workflow_url="http://example.com/test",
        builder_url="http://example.com/test/builder",
        version_number="1.0.0",
    )
    db_session.add(version)
    db_session.commit()
    
    # Set current version
    workflow.current_version_id = version.id
    db_session.commit()
    
    # Verify current version is set
    assert workflow.current_version_id == version.id
    
    # Delete the version directly (not through workflow cascade)
    db_session.delete(version)
    db_session.commit()
    
    # Refresh workflow from database
    db_session.refresh(workflow)
    
    # Verify current_version_id is set to NULL
    assert workflow.current_version_id is None
    assert workflow.name == "Test Workflow"  # Workflow should still exist


def test_cascade_delete_workflow_versions_and_listings(db_session):
    """
    Test that deleting a workflow cascades to delete its versions and marketplace listings
    """
    # Create workflow
    workflow = Workflow(
        name="Test Workflow",
        description="Test workflow",
        workflow_url="http://example.com/test",
        builder_url="http://example.com/test/builder",
        owner_id="test_owner",
        owner_type=WorkflowOwnerTypeEnum.USER,
        visibility=WorkflowVisibilityEnum.PUBLIC,
    )
    db_session.add(workflow)
    db_session.commit()
    
    # Create multiple versions
    version1 = WorkflowVersion(
        workflow_id=workflow.id,
        name="Test Workflow",
        workflow_url="http://example.com/test",
        builder_url="http://example.com/test/builder",
        version_number="1.0.0",
    )
    version2 = WorkflowVersion(
        workflow_id=workflow.id,
        name="Test Workflow",
        workflow_url="http://example.com/test",
        builder_url="http://example.com/test/builder",
        version_number="2.0.0",
    )
    db_session.add_all([version1, version2])
    db_session.commit()
    
    # Create marketplace listings
    listing1 = WorkflowMarketplaceListing(
        workflow_id=workflow.id,
        workflow_version_id=version1.id,
        listed_by_user_id="test_user",
        title="Test Listing 1",
        description="Test listing for version 1",
    )
    listing2 = WorkflowMarketplaceListing(
        workflow_id=workflow.id,
        workflow_version_id=version2.id,
        listed_by_user_id="test_user",
        title="Test Listing 2",
        description="Test listing for version 2",
    )
    db_session.add_all([listing1, listing2])
    db_session.commit()
    
    # Verify everything exists
    assert len(workflow.versions) == 2
    assert len(workflow.marketplace_listings) == 2
    
    # Delete the workflow
    db_session.delete(workflow)
    db_session.commit()
    
    # Verify everything is cascade deleted
    remaining_versions = db_session.query(WorkflowVersion).filter_by(workflow_id=workflow.id).all()
    remaining_listings = db_session.query(WorkflowMarketplaceListing).filter_by(workflow_id=workflow.id).all()
    
    assert len(remaining_versions) == 0
    assert len(remaining_listings) == 0


if __name__ == "__main__":
    pytest.main([__file__])