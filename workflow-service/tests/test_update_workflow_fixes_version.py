import pytest
import json
from sqlalchemy.orm import Session
from app.db.session import <PERSON><PERSON>ocal
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.utils.constants.constants import WorkflowVisibilityEnum, WorkflowStatusEnum
from app.services.workflow_functions import WorkflowFunctions
from app.grpc_ import workflow_pb2
from app.helpers.helpers import _create_marketplace_listing_from_workflow


class TestUpdateWorkflowFixesVersion:
    """Test that updateWorkflow properly updates both workflow and current version"""

    @pytest.fixture
    def db_session(self):
        """Create a test database session - NO CLEANUP TO PREVENT DATA LOSS"""
        db = SessionLocal()
        try:
            yield db
        finally:
            # NO CLEANUP - Leaving test data to prevent accidental deletion of production data
            # Test data will have test-specific names/IDs that won't conflict with real data
            db.close()

    @pytest.fixture
    def workflow_service(self):
        return WorkflowFunctions()

    @pytest.fixture
    def mock_context(self):
        class MockContext:
            def set_code(self, code):
                self.code = code

            def set_details(self, details):
                self.details = details

        return MockContext()

    def test_update_workflow_updates_current_version(self, db_session: Session, workflow_service: WorkflowFunctions, mock_context):
        """
        Test that when updateWorkflow is called, it updates both the main workflow
        and the current version, so marketplace listings show the correct data.
        """
        # Create workflow
        workflow = Workflow(
            name="untitled_workflow",
            description="initial description",
            owner_id="test_user_123",
            user_ids=["test_user_123"],
            owner_type="user",
            visibility=WorkflowVisibilityEnum.PRIVATE,
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start_node"}],
            available_nodes=[],
            tags=["test"],
            category="automation",
            is_updated=False,
            is_customizable=True,
        )

        db_session.add(workflow)
        db_session.flush()

        # Create initial version
        version = WorkflowVersion(
            workflow_id=workflow.id,
            version_number="1.0.0",
            name="untitled_workflow",
            description="initial description",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start_node"}],
            available_nodes=[],
            category="automation",
            tags=["test"],
            changelog="Initial version",
        )

        db_session.add(version)
        db_session.flush()

        workflow.current_version_id = version.id
        db_session.commit()

        print(f"✅ Created workflow {workflow.id} with name '{workflow.name}'")
        print(f"✅ Created version {version.id} with name '{version.name}'")

        # Call updateWorkflow to update name and description
        owner = workflow_pb2.Owner()
        owner.id = "test_user_123"

        update_request = workflow_pb2.UpdateWorkflowRequest()
        update_request.id = workflow.id
        update_request.name = "Nikhil Test updated"
        update_request.description = "test description updated"
        update_request.owner.CopyFrom(owner)
        
        # Set update mask to specify which fields to update
        update_request.update_mask.paths.append("name")
        update_request.update_mask.paths.append("description")

        response = workflow_service.updateWorkflow(update_request, mock_context)

        assert response.success == True
        print(f"✅ updateWorkflow successful: {response.message}")

        # Refresh objects from database
        db_session.refresh(workflow)
        db_session.refresh(version)

        # Verify both workflow AND current version were updated
        assert workflow.name == "Nikhil Test updated"
        assert workflow.description == "test description updated"
        assert workflow.is_updated == True

        # This is the key test - current version should also be updated
        assert version.name == "Nikhil Test updated"
        assert version.description == "test description updated"

        print(f"✅ Workflow updated: name='{workflow.name}', description='{workflow.description}'")
        print(f"✅ Version updated: name='{version.name}', description='{version.description}'")

        # Now test marketplace listing creation
        workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        marketplace_listing = _create_marketplace_listing_from_workflow(db_session, workflow)
        db_session.commit()

        # Marketplace listing should use current version data (which is now updated)
        assert marketplace_listing is not None
        assert marketplace_listing.title == "Nikhil Test updated"
        assert marketplace_listing.description == "test description updated"

        print(f"✅ Marketplace listing created with correct data: title='{marketplace_listing.title}'")