"""
Integration tests for template variable processing in workflow save pipeline.

This module tests the complete integration of template variable preprocessing and validation
into the workflow creation and update processes.
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock

from app.services.workflow_builder.workflow_schema_converter import (
    validate_workflow_template_variables,
    convert_workflow_to_transition_schema,
)


class TestWorkflowTemplateVariableIntegration:
    """Test cases for template variable integration in workflow pipeline."""

    def setup_method(self):
        """Set up test fixtures."""
        pass
        
        # Sample workflow data with template variables
        self.sample_workflow_data = {
            "nodes": [
                {
                    "id": "node1",
                    "type": "WorkflowNode",
                    "data": {
                        "label": "API Request",
                        "type": "component",
                        "originalType": "APIRequestNode",
                        "definition": {
                            "name": "APIRequestNode",
                            "display_name": "API Request",
                            "description": "Make HTTP API requests",
                            "category": "Network",
                            "icon": "Globe",
                            "beta": False,
                            "inputs": [
                                {"name": "url", "input_type": "string"},
                                {"name": "method", "input_type": "string"},
                                {"name": "headers", "input_type": "object"},
                                {"name": "body", "input_type": "object"}
                            ],
                            "outputs": [
                                {"name": "response", "output_type": "object"}
                            ],
                            "is_valid": True,
                            "path": "components.network.api_request"
                        },
                        "config": {
                            "url": "https://api.example.com/{endpoint}",
                            "method": "POST",
                            "headers": {
                                "Authorization": "Bearer ${api_token}",
                                "Content-Type": "application/json"
                            },
                            "body": {
                                "user_id": "{user_id}",
                                "message": "Hello {user_name}!"
                            }
                        }
                    }
                },
                {
                    "id": "node2",
                    "type": "WorkflowNode",
                    "data": {
                        "label": "Condition",
                        "type": "component",
                        "originalType": "ConditionalNode",
                        "definition": {
                            "name": "ConditionalNode",
                            "display_name": "Condition",
                            "description": "Conditional routing logic",
                            "category": "Control Flow",
                            "icon": "GitBranch",
                            "beta": False,
                            "inputs": [
                                {"name": "condition", "input_type": "string"}
                            ],
                            "outputs": [
                                {"name": "true", "output_type": "boolean"},
                                {"name": "false", "output_type": "boolean"}
                            ],
                            "is_valid": True,
                            "path": "components.control_flow.conditional"
                        },
                        "config": {
                            "condition": "{response_status} == 200",
                            "true_message": "Success for {user_name}",
                            "false_message": "Failed for {user_name}"
                        }
                    }
                }
            ],
            "edges": [
                {
                    "id": "edge1",
                    "source": "node1",
                    "target": "node2"
                }
            ],
            "mcp_configs": []
        }

    def test_template_variable_validation_success(self):
        """Test successful template variable validation."""
        validation_result = validate_workflow_template_variables(self.sample_workflow_data)
        
        assert validation_result["valid"] is True
        assert len(validation_result["detected_variables"]) > 0
        assert validation_result["variable_summary"]["total_count"] > 0
        
        # Check that both template and dollar syntax are detected
        syntax_breakdown = validation_result["variable_summary"]["syntax_breakdown"]
        assert syntax_breakdown["template"] > 0  # {variable} syntax
        assert syntax_breakdown["dollar"] > 0    # ${variable} syntax

    def test_template_variable_validation_with_invalid_variables(self):
        """Test template variable validation with invalid variable names."""
        invalid_workflow_data = {
            "nodes": [
                {
                    "id": "node1",
                    "type": "WorkflowNode",
                    "data": {
                        "label": "Invalid Node",
                        "type": "component",
                        "originalType": "TestNode",
                        "definition": {
                            "name": "TestNode",
                            "display_name": "Test Node",
                            "description": "Test node with invalid template variables",
                            "category": "Test",
                            "icon": "Test",
                            "beta": False,
                            "inputs": [],
                            "outputs": [],
                            "is_valid": True,
                            "path": "components.test.test_node"
                        },
                        "config": {
                            "message": "Hello {123invalid}!",  # Invalid: starts with number
                            "url": "https://api.com/{invalid-name}",  # Invalid: contains hyphen
                            "empty": "Value: {}"  # Invalid: empty variable name
                        }
                    }
                }
            ],
            "edges": [],
            "mcp_configs": []
        }
        
        validation_result = validate_workflow_template_variables(invalid_workflow_data)
        
        assert validation_result["valid"] is False
        assert len(validation_result["errors"]) > 0
        
        # Check that specific errors are present
        error_text = " ".join(validation_result["errors"])
        assert "123invalid" in error_text
        assert "invalid-name" in error_text

    def test_workflow_conversion_with_template_variables(self):
        """Test that workflow conversion properly preprocesses template variables."""
        converted_workflow = convert_workflow_to_transition_schema(self.sample_workflow_data)

        # Convert to JSON string for easier searching
        converted_json = json.dumps(converted_workflow)

        # Debug: Print the converted JSON to see what's happening
        print(f"Converted JSON: {converted_json}")

        # Check that {variable} syntax has been converted to ${variable}
        assert "${endpoint}" in converted_json
        assert "${user_id}" in converted_json
        assert "${user_name}" in converted_json
        assert "${response_status}" in converted_json

        # Check that existing ${variable} syntax is preserved
        assert "${api_token}" in converted_json

        # The original {variable} patterns might still exist in the definition or other metadata
        # Let's focus on checking that the field_value parameters have been converted
        # We'll check the transitions section specifically
        transitions = converted_workflow.get("transitions", [])
        assert len(transitions) > 0

        # Check that template variables in tool parameters have been converted
        for transition in transitions:
            tools_to_use = transition.get("node_info", {}).get("tools_to_use", [])
            for tool in tools_to_use:
                tool_params = tool.get("tool_params", {})
                if "items" in tool_params:
                    for item in tool_params["items"]:
                        field_value = item.get("field_value")
                        if field_value and isinstance(field_value, str):
                            # Template variables in field_value should be converted to ${variable}
                            if "endpoint" in field_value:
                                assert "${endpoint}" in field_value, f"Template variable not converted in field_value: {field_value}"
                            if "user_id" in field_value:
                                assert "${user_id}" in field_value, f"Template variable not converted in field_value: {field_value}"

    def test_end_to_end_template_variable_processing(self):
        """Test end-to-end template variable processing pipeline."""
        # First validate the workflow
        validation_result = validate_workflow_template_variables(self.sample_workflow_data)
        assert validation_result["valid"] is True

        # Then convert the workflow (which includes preprocessing)
        converted_workflow = convert_workflow_to_transition_schema(self.sample_workflow_data)

        # Verify that the conversion was successful and template variables were processed
        converted_json = json.dumps(converted_workflow)

        # Check that template variables were converted
        assert "${endpoint}" in converted_json
        assert "${user_id}" in converted_json
        assert "${user_name}" in converted_json

        # Check that existing dollar syntax is preserved
        assert "${api_token}" in converted_json

        # Verify the structure is maintained
        assert "nodes" in converted_workflow
        assert len(converted_workflow["nodes"]) == 2

    def test_template_variable_preprocessing_preserves_functionality(self):
        """Test that template variable preprocessing doesn't break existing functionality."""
        # Test with workflow that has no template variables
        simple_workflow_data = {
            "nodes": [
                {
                    "id": "node1",
                    "type": "WorkflowNode",
                    "data": {
                        "label": "Simple API Request",
                        "type": "component",
                        "originalType": "APIRequestNode",
                        "definition": {
                            "name": "APIRequestNode",
                            "display_name": "API Request",
                            "description": "Make HTTP API requests",
                            "category": "Network",
                            "icon": "Globe",
                            "beta": False,
                            "inputs": [
                                {"name": "url", "input_type": "string"},
                                {"name": "method", "input_type": "string"}
                            ],
                            "outputs": [
                                {"name": "response", "output_type": "object"}
                            ],
                            "is_valid": True,
                            "path": "components.network.api_request"
                        },
                        "config": {
                            "url": "https://api.example.com/static",
                            "method": "GET"
                        }
                    }
                }
            ],
            "edges": [],
            "mcp_configs": []
        }
        
        # Should not raise any errors
        validation_result = validate_workflow_template_variables(simple_workflow_data)
        assert validation_result["valid"] is True
        assert validation_result["variable_summary"]["total_count"] == 0
        
        # Conversion should still work
        converted_workflow = convert_workflow_to_transition_schema(simple_workflow_data)
        assert converted_workflow is not None
        assert "nodes" in converted_workflow

    def test_mixed_template_variable_syntax_warning(self):
        """Test that mixed template variable syntax generates appropriate warnings."""
        mixed_syntax_workflow = {
            "nodes": [
                {
                    "id": "node1",
                    "type": "WorkflowNode",
                    "data": {
                        "label": "Mixed Syntax Node",
                        "type": "component",
                        "originalType": "TestNode",
                        "definition": {
                            "name": "TestNode",
                            "display_name": "Test Node",
                            "description": "Test node with mixed template variable syntax",
                            "category": "Test",
                            "icon": "Test",
                            "beta": False,
                            "inputs": [],
                            "outputs": [],
                            "is_valid": True,
                            "path": "components.test.test_node"
                        },
                        "config": {
                            "template_var": "Hello {user_name}",  # Template syntax
                            "dollar_var": "Token: ${api_token}"   # Dollar syntax
                        }
                    }
                }
            ],
            "edges": [],
            "mcp_configs": []
        }
        
        validation_result = validate_workflow_template_variables(mixed_syntax_workflow)
        
        assert validation_result["valid"] is True  # Should still be valid
        assert len(validation_result["warnings"]) > 0
        
        # Check for mixed syntax warning
        warning_text = " ".join(validation_result["warnings"])
        assert "Mixed template variable syntax" in warning_text
