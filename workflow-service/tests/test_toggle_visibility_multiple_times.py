import pytest
from sqlalchemy.orm import Session
from app.db.session import <PERSON><PERSON><PERSON><PERSON>
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.helpers.helpers import _create_marketplace_listing_from_workflow
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum
from datetime import datetime, timezone


class TestToggleVisibilityMultipleTimes:
    """Test that toggling visibility multiple times properly updates marketplace listings"""

    @pytest.fixture
    def db_session(self):
        """Create a test database session - NO CLEANUP TO PREVENT DATA LOSS"""
        db = SessionLocal()
        try:
            yield db
        finally:
            # NO CLEANUP - Leaving test data to prevent accidental deletion of production data
            # Test data will have test-specific names/IDs that won't conflict with real data
            db.close()

    def test_toggle_visibility_multiple_times_updates_marketplace_listing(self, db_session: Session):
        """
        Test that toggling PRIVATE→PUBLIC→PRIVATE→PUBLIC properly updates marketplace listing
        with current workflow changes each time.
        """
        # Create a workflow
        workflow = Workflow(
            name="Original Name",
            description="Original description",
            owner_id="user123",
            user_ids=["user123"],
            owner_type="user",
            visibility=WorkflowVisibilityEnum.PRIVATE,
            workflow_url="https://example.com/workflow1.json",
            builder_url="https://example.com/builder1.json",
            start_nodes=[{"id": "start", "type": "start"}],
            available_nodes=[{"id": "node1", "type": "action"}],
            tags=["original", "test"],
            category="automation",
            is_updated=False,
            is_customizable=True,
        )
        db_session.add(workflow)
        db_session.flush()

        # Create initial version
        version = WorkflowVersion(
            workflow_id=workflow.id,
            version_number="1.0.0",
            name="Original Name",
            description="Original description",
            workflow_url="https://example.com/workflow1.json",
            builder_url="https://example.com/builder1.json",
            start_nodes=[{"id": "start", "type": "start"}],
            available_nodes=[{"id": "node1", "type": "action"}],
            category="automation",
            tags=["original", "test"],
            changelog="Initial version",
        )
        db_session.add(version)
        db_session.flush()
        
        workflow.current_version_id = version.id
        db_session.commit()

        # Step 1: Toggle PRIVATE → PUBLIC (first time)
        workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        marketplace_listing = _create_marketplace_listing_from_workflow(db_session, workflow)
        db_session.commit()
        
        assert marketplace_listing is not None
        assert marketplace_listing.title == "Original Name"
        assert marketplace_listing.description == "Original description"
        assert marketplace_listing.status == WorkflowStatusEnum.ACTIVE
        listing_id = marketplace_listing.id

        # Step 2: Update workflow while PUBLIC (simulate proper updateWorkflow behavior)
        workflow.name = "Updated Name"
        workflow.description = "Updated description"
        workflow.tags = ["updated", "test"]
        workflow.is_updated = True
        
        # Also update current version (this is what the fixed updateWorkflow method now does)
        version.name = "Updated Name"
        version.description = "Updated description"
        version.tags = ["updated", "test"]
        
        db_session.commit()

        # Step 3: Toggle PUBLIC → PRIVATE
        workflow.visibility = WorkflowVisibilityEnum.PRIVATE
        marketplace_listing.status = WorkflowStatusEnum.INACTIVE
        db_session.commit()

        # Verify listing is inactive
        db_session.refresh(marketplace_listing)
        assert marketplace_listing.status == WorkflowStatusEnum.INACTIVE

        # Step 4: Make more changes while PRIVATE (simulate proper updateWorkflow behavior)
        workflow.name = "Final Updated Name"
        workflow.description = "Final updated description"
        workflow.tags = ["final", "updated", "test"]
        
        # Also update current version (this is what the fixed updateWorkflow method now does)
        version.name = "Final Updated Name"
        version.description = "Final updated description"
        version.tags = ["final", "updated", "test"]
        
        db_session.commit()

        # Step 5: Toggle PRIVATE → PUBLIC (second time) - This should update with latest changes
        workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        updated_listing = _create_marketplace_listing_from_workflow(db_session, workflow)
        db_session.commit()

        # Verify the same listing was reactivated and updated with current workflow state
        assert updated_listing is not None
        assert updated_listing.id == listing_id  # Same listing reused
        assert updated_listing.title == "Final Updated Name"  # Should use current workflow name
        assert updated_listing.description == "Final updated description"  # Should use current description
        assert updated_listing.tags == ["final", "updated", "test"]  # Should use current tags
        assert updated_listing.status == WorkflowStatusEnum.ACTIVE
        assert updated_listing.visibility == WorkflowVisibilityEnum.PUBLIC

        # Verify there's still only one marketplace listing for this workflow
        all_listings = db_session.query(WorkflowMarketplaceListing).filter(
            WorkflowMarketplaceListing.workflow_id == workflow.id
        ).all()
        assert len(all_listings) == 1


    def test_marketplace_listing_uses_current_version_data_after_update(self, db_session: Session):
        """
        Test that marketplace listings use current version data, and that updateWorkflow
        properly updates both the workflow and current version.
        This is the core fix for the original issue.
        """
        # Create workflow
        workflow = Workflow(
            name="untitled_workflow",  # Original name
            description="Original description",
            owner_id="user123",
            user_ids=["user123"],
            owner_type="user",
            visibility=WorkflowVisibilityEnum.PRIVATE,
            workflow_url="https://example.com/workflow1.json",
            builder_url="https://example.com/builder1.json",
            start_nodes=[{"id": "start", "type": "start"}],
            available_nodes=[{"id": "node1", "type": "action"}],
            tags=["original"],
            category="automation",
            is_updated=False,
            is_customizable=True,
        )
        db_session.add(workflow)
        db_session.flush()

        # Create version with original data
        version = WorkflowVersion(
            workflow_id=workflow.id,
            version_number="1.0.0",
            name="untitled_workflow",  # Version has original name
            description="Original description",
            workflow_url="https://example.com/workflow1.json",
            builder_url="https://example.com/builder1.json",
            start_nodes=[{"id": "start", "type": "start"}],
            available_nodes=[{"id": "node1", "type": "action"}],
            category="automation",
            tags=["original"],
            changelog="Initial version",
        )
        db_session.add(version)
        db_session.flush()
        
        workflow.current_version_id = version.id
        db_session.commit()

        # Simulate proper updateWorkflow behavior - update BOTH workflow AND current version
        # This is what the fixed updateWorkflow method now does
        workflow.name = "test workflow"  # Updated name
        workflow.description = "Updated description"
        workflow.tags = ["updated", "test"]
        workflow.is_updated = True  # Indicates pending changes
        
        # Also update current version (this is the key fix)
        version.name = "test workflow"
        version.description = "Updated description"
        version.tags = ["updated", "test"]
        
        db_session.commit()

        # Verify both workflow and version have updated data
        db_session.refresh(version)
        assert version.name == "test workflow"  # Version now updated
        assert workflow.name == "test workflow"  # Workflow updated

        # Toggle to PUBLIC - marketplace listing should use current version data
        workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        marketplace_listing = _create_marketplace_listing_from_workflow(db_session, workflow)
        db_session.commit()

        # Verify marketplace listing uses current version data (which is now updated)
        assert marketplace_listing is not None
        assert marketplace_listing.title == "test workflow"  # Should use current version name
        assert marketplace_listing.description == "Updated description"  # Should use current version description
        assert marketplace_listing.tags == ["updated", "test"]  # Should use current version tags
        assert marketplace_listing.workflow_version_id == version.id  # References current version


if __name__ == "__main__":
    pytest.main([__file__])