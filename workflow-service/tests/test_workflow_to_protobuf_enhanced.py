import pytest
import uuid
from datetime import datetime, timezone
from unittest.mock import Mock, patch
from app.helpers.workflow_to_protobuf import _workflow_to_protobuf
from app.models.workflow import Workflow, WorkflowVersion


def test_workflow_to_protobuf_with_source_version_id():
    """Test that _workflow_to_protobuf includes source_version_id field."""
    # Create mock workflow with source_version_id
    workflow = Mock(spec=Workflow)
    workflow.id = uuid.uuid4()
    workflow.name = "Test Workflow"
    workflow.description = "Test Description"
    workflow.workflow_url = "test_url"
    workflow.builder_url = "builder_url"
    workflow.owner_id = "owner123"
    workflow.user_ids = ["user1", "user2"]
    workflow.owner_type = "user"
    workflow.start_nodes = [{"id": "start1"}]
    workflow.available_nodes = [{"id": "node1"}]
    workflow.workflow_template_id = uuid.uuid4()
    workflow.template_owner_id = "template_owner"
    workflow.is_imported = True
    workflow.source_version_id = str(uuid.uuid4())  # Enhanced version sync field
    workflow.is_updated = False
    workflow.is_customizable = True
    workflow.visibility = "private"
    workflow.category = "automation"
    workflow.tags = ["tag1", "tag2"]
    workflow.status = "active"
    workflow.created_at = datetime.now(timezone.utc)
    workflow.updated_at = datetime.now(timezone.utc)
    workflow.current_version_id = None

    # Convert to protobuf
    protobuf_workflow = _workflow_to_protobuf(workflow)

    # Verify all fields including source_version_id
    assert protobuf_workflow.id == str(workflow.id)
    assert protobuf_workflow.name == workflow.name
    assert protobuf_workflow.description == workflow.description
    assert protobuf_workflow.workflow_template_id == str(workflow.workflow_template_id)
    assert protobuf_workflow.template_owner_id == workflow.template_owner_id
    assert protobuf_workflow.is_imported == workflow.is_imported
    assert protobuf_workflow.source_version_id == workflow.source_version_id  # Enhanced field
    assert protobuf_workflow.visibility == workflow.visibility
    assert protobuf_workflow.category == workflow.category
    assert protobuf_workflow.tags == workflow.tags
    assert protobuf_workflow.status == workflow.status


def test_workflow_to_protobuf_without_source_version_id():
    """Test that _workflow_to_protobuf handles None source_version_id gracefully."""
    # Create mock workflow without source_version_id
    workflow = Mock(spec=Workflow)
    workflow.id = uuid.uuid4()
    workflow.name = "Test Workflow"
    workflow.description = "Test Description"
    workflow.workflow_url = "test_url"
    workflow.builder_url = "builder_url"
    workflow.owner_id = "owner123"
    workflow.user_ids = ["user1", "user2"]
    workflow.owner_type = "user"
    workflow.start_nodes = [{"id": "start1"}]
    workflow.available_nodes = [{"id": "node1"}]
    workflow.workflow_template_id = None
    workflow.template_owner_id = None
    workflow.is_imported = False
    workflow.source_version_id = None  # No source version
    workflow.is_updated = False
    workflow.is_customizable = True
    workflow.visibility = "private"
    workflow.category = "automation"
    workflow.tags = ["tag1", "tag2"]
    workflow.status = "active"
    workflow.created_at = datetime.now(timezone.utc)
    workflow.updated_at = datetime.now(timezone.utc)
    workflow.current_version_id = None

    # Convert to protobuf
    protobuf_workflow = _workflow_to_protobuf(workflow)

    # Verify source_version_id is None when not set
    assert protobuf_workflow.source_version_id is None
    assert protobuf_workflow.workflow_template_id is None
    assert protobuf_workflow.template_owner_id is None
    assert protobuf_workflow.is_imported == False


def test_workflow_to_protobuf_with_current_version():
    """Test that _workflow_to_protobuf correctly fetches current version number."""
    # Create mock workflow with current_version_id
    workflow = Mock(spec=Workflow)
    workflow.id = uuid.uuid4()
    workflow.name = "Test Workflow"
    workflow.description = "Test Description"
    workflow.workflow_url = "test_url"
    workflow.builder_url = "builder_url"
    workflow.owner_id = "owner123"
    workflow.user_ids = []
    workflow.owner_type = "user"
    workflow.start_nodes = []
    workflow.available_nodes = []
    workflow.workflow_template_id = None
    workflow.template_owner_id = None
    workflow.is_imported = False
    workflow.source_version_id = str(uuid.uuid4())
    workflow.is_updated = False
    workflow.is_customizable = True
    workflow.visibility = "private"
    workflow.category = "automation"
    workflow.tags = []
    workflow.status = "active"
    workflow.created_at = datetime.now(timezone.utc)
    workflow.updated_at = datetime.now(timezone.utc)
    workflow.current_version_id = str(uuid.uuid4())

    # Mock database session and version query
    mock_version = Mock(spec=WorkflowVersion)
    mock_version.version_number = "2.1.0"
    
    mock_db = Mock()
    mock_query = Mock()
    mock_filter = Mock()
    mock_db.query.return_value = mock_query
    mock_query.filter.return_value = mock_filter
    mock_filter.first.return_value = mock_version

    # Convert to protobuf with mocked db
    protobuf_workflow = _workflow_to_protobuf(workflow, db=mock_db)

    # Verify version number is correctly fetched
    assert protobuf_workflow.version == "2.1.0"
    assert protobuf_workflow.source_version_id == workflow.source_version_id


if __name__ == "__main__":
    pytest.main([__file__])