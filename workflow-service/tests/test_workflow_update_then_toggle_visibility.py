import pytest
import json
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import Session<PERSON>ocal
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.services.workflow_functions import WorkflowFunctions
from app.grpc_ import workflow_pb2
from app.utils.constants.constants import WorkflowVisibilityEnum, WorkflowStatusEnum
from app.helpers.helpers import _create_marketplace_listing_from_workflow


class TestWorkflowUpdateThenToggleVisibility:
    """Test the exact user scenario: update workflow name then toggle visibility"""

    @pytest.fixture
    def db_session(self):
        """Create a test database session - NO CLEANUP TO PREVENT DATA LOSS"""
        db = SessionLocal()
        try:
            yield db
        finally:
            # NO CLEANUP - Leaving test data to prevent accidental deletion of production data
            # Test data will have test-specific names/IDs that won't conflict with real data
            db.close()

    def test_user_scenario_update_name_then_toggle_visibility(self, db_session: Session):
        """
        Test the exact user scenario:
        1. Create workflow "untitled_workflow" (private)
        2. Update workflow name to "test workflow" 
        3. Toggle visibility to PUBLIC
        4. Marketplace listing should show "test workflow", not "untitled_workflow"
        """
        
        # Step 1: Create workflow "untitled_workflow" (private)
        workflow = Workflow(
            name="untitled_workflow",  # Original name
            description="Test Description",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start_node"}],
            available_nodes=[],
            owner_id="test_user_123",
            owner_type="user",
            visibility=WorkflowVisibilityEnum.PRIVATE,  # Initially private
            status=WorkflowStatusEnum.ACTIVE,
            is_updated=False,
            is_customizable=True,
            tags=["test"]
        )
        
        db_session.add(workflow)
        db_session.flush()
        
        # Create initial v1.0.0 version with original name
        v1_version = WorkflowVersion(
            workflow_id=workflow.id,
            version_number="1.0.0",
            name="untitled_workflow",  # Version has original name
            description="Initial version",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start_node"}],
            available_nodes=[],
            changelog="Initial version",
            created_at=datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc)
        )
        
        db_session.add(v1_version)
        db_session.flush()
        
        # Set current version to v1.0.0
        workflow.current_version_id = v1_version.id
        db_session.commit()
        
        # Verify initial state
        assert workflow.name == "untitled_workflow"
        assert workflow.visibility == WorkflowVisibilityEnum.PRIVATE
        assert v1_version.name == "untitled_workflow"
        
        # Step 2: Update workflow name to "test workflow"
        # This simulates what happens when user updates workflow via updateWorkflow endpoint
        workflow.name = "test workflow"  # Updated name in main workflow record
        workflow.is_updated = True  # Set by updateWorkflow when version-relevant fields change
        db_session.commit()
        
        # Verify workflow state after update
        assert workflow.name == "test workflow"  # Workflow record has new name
        assert workflow.is_updated == True  # Has pending changes
        assert workflow.current_version_id == v1_version.id  # Still points to v1.0.0
        assert v1_version.name == "untitled_workflow"  # Version still has old name
        
        # Step 3: Toggle visibility to PUBLIC
        # Create the workflow service
        workflow_service = WorkflowFunctions()

        # Create the toggle visibility request
        owner = workflow_pb2.Owner()
        owner.id = "test_user_123"

        request = workflow_pb2.ToggleWorkflowVisibilityRequest()
        request.workflow_id = workflow.id
        request.owner.CopyFrom(owner)
        
        # Mock gRPC context
        class MockContext:
            def set_code(self, code):
                self.code = code
            def set_details(self, details):
                self.details = details
        
        context = MockContext()
        
        # Call toggleWorkflowVisibility
        response = workflow_service.toggleWorkflowVisibility(request, context)
        
        # Verify the response was successful
        assert response.success == True
        assert "PUBLIC" in response.message
        
        # Step 4: Verify marketplace listing shows updated name
        marketplace_listing = (
            db_session.query(WorkflowMarketplaceListing)
            .filter(WorkflowMarketplaceListing.workflow_id == workflow.id)
            .first()
        )
        
        assert marketplace_listing is not None
        
        # THE KEY ASSERTION: Marketplace listing should show "test workflow" (current state)
        # NOT "untitled_workflow" (old version state)
        assert marketplace_listing.title == "test workflow", (
            f"Expected marketplace listing to show updated name 'test workflow', "
            f"but got '{marketplace_listing.title}'"
        )
        
        # Verify other fields come from current workflow state
        assert marketplace_listing.description == workflow.description
        assert marketplace_listing.tags == workflow.tags
        
        # Verify it references the latest version for FK constraint
        assert marketplace_listing.workflow_version_id == v1_version.id
        
        print(f"✅ User scenario test passed: Marketplace listing shows updated name '{marketplace_listing.title}'")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])