"""
Test suite for template variable support in Agentic AI component.

This test suite verifies that the Agentic AI component correctly processes
template variables in query and input_variables fields, converting {variable_name}
syntax to ${variable_name} format for orchestration engine compatibility.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from app.components.ai.agentic_ai import AgenticA<PERSON>
from app.models.workflow_builder.context import WorkflowContext


class TestAgenticAITemplateVariables:
    """Test cases for template variable support in Agentic AI component."""

    def setup_method(self):
        """Set up test fixtures."""
        self.component = AgenticAI()
        self.context = Mock(spec=WorkflowContext)
        self.context.current_node_id = "test_node_123"
        self.context.node_outputs = {}
        self.context.log = Mock()

    def test_resolve_template_variables_string_query(self):
        """Test template variable resolution in string query field."""
        # Test data with {variable_name} syntax
        original_query = "Analyze the {document_type} and provide {analysis_type}"
        expected_query = "Analyze the ${document_type} and provide ${analysis_type}"
        
        # Mock the preprocessing function
        with patch('app.components.ai.agentic_ai.preprocess_template_variables') as mock_preprocess:
            mock_preprocess.return_value = expected_query
            
            result = self.component._resolve_template_variables(
                original_query, self.context, "query"
            )
            
            # Verify the preprocessing function was called correctly
            mock_preprocess.assert_called_once_with(original_query, "AgenticAI.query")
            
            # Verify the result
            assert result == expected_query
            
            # Verify logging was called since value changed
            self.context.log.assert_called_once_with("Processed template variables in query field")

    def test_resolve_template_variables_dict_input_variables(self):
        """Test template variable resolution in dict input_variables field."""
        # Test data with mixed template variables
        original_variables = {
            "task": "Process {data_source}",
            "context": "Analysis for ${client_name}",
            "deadline": "2024-12-31"
        }
        expected_variables = {
            "task": "Process ${data_source}",
            "context": "Analysis for ${client_name}",
            "deadline": "2024-12-31"
        }
        
        with patch('app.components.ai.agentic_ai.preprocess_template_variables') as mock_preprocess:
            mock_preprocess.return_value = expected_variables
            
            result = self.component._resolve_template_variables(
                original_variables, self.context, "input_variables"
            )
            
            mock_preprocess.assert_called_once_with(original_variables, "AgenticAI.input_variables")
            assert result == expected_variables

    def test_resolve_template_variables_no_changes(self):
        """Test template variable resolution when no template variables are present."""
        original_query = "Simple query without template variables"
        
        with patch('app.components.ai.agentic_ai.preprocess_template_variables') as mock_preprocess:
            mock_preprocess.return_value = original_query  # No changes
            
            result = self.component._resolve_template_variables(
                original_query, self.context, "query"
            )
            
            assert result == original_query
            # Verify no logging was called since value didn't change
            self.context.log.assert_not_called()

    def test_resolve_template_variables_error_handling(self):
        """Test error handling in template variable resolution."""
        original_query = "Query with {invalid_syntax"
        
        with patch('app.components.ai.agentic_ai.preprocess_template_variables') as mock_preprocess:
            mock_preprocess.side_effect = Exception("Template variable processing error")
            
            result = self.component._resolve_template_variables(
                original_query, self.context, "query"
            )
            
            # Should return original value on error
            assert result == original_query
            
            # Should log the error
            self.context.log.assert_called_once_with(
                "Error processing template variables in query: Template variable processing error"
            )

    def test_execute_method_applies_template_variable_resolution(self):
        """Test that the execute method applies template variable resolution to query and input_variables."""
        # Setup mock context with node outputs
        self.context.node_outputs = {
            "test_node_123": {
                "query": "Analyze {document_type}",
                "input_variables": {"context": "Data from {source}"},
                "api_key": "test_key",
                "model_provider": "OpenAI",
                "model_name": "gpt-4o"
            }
        }
        
        # Mock the template variable resolution
        with patch.object(self.component, '_resolve_template_variables') as mock_resolve:
            mock_resolve.side_effect = lambda value, ctx, field: f"resolved_{field}_{value}"
            
            # Mock other dependencies
            with patch.object(self.component, '_check_autogen_installed', return_value=True), \
                 patch.object(self.component, '_extract_connected_workflow_components', return_value=[]), \
                 patch('app.components.ai.agentic_ai.importlib.import_module'):
                
                # Mock autogen modules
                mock_autogen = Mock()
                mock_assistant = Mock()
                mock_user_proxy = Mock()
                mock_autogen.AssistantAgent.return_value = mock_assistant
                mock_autogen.UserProxyAgent.return_value = mock_user_proxy
                mock_user_proxy.a_initiate_chat = Mock(return_value=Mock(summary="Test response"))
                
                with patch('app.components.ai.agentic_ai.autogen', mock_autogen):
                    # This should not raise an exception and should call template variable resolution
                    try:
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        result = loop.run_until_complete(self.component.execute(self.context))
                        loop.close()
                    except Exception:
                        pass  # We expect some errors due to mocking, but template resolution should be called
                    
                    # Verify template variable resolution was called for both fields
                    mock_resolve.assert_any_call("Analyze {document_type}", self.context, "query")
                    mock_resolve.assert_any_call({"context": "Data from {source}"}, self.context, "input_variables")

    def test_get_agent_config_applies_template_variable_resolution(self):
        """Test that get_agent_config method applies template variable resolution to query field."""
        # Setup mock context
        self.context.node_outputs = {
            "test_node_123": {
                "query": "Generate report for {client_name}",
                "model_provider": "OpenAI"
            }
        }
        
        with patch.object(self.component, '_resolve_template_variables') as mock_resolve:
            mock_resolve.return_value = "Generate report for ${client_name}"
            
            with patch.object(self.component, '_extract_connected_workflow_components', return_value=[]):
                config = self.component.get_agent_config(self.context)
                
                # Verify template variable resolution was called
                mock_resolve.assert_called_once_with("Generate report for {client_name}", self.context, "query")
                
                # Verify the resolved query is in the config
                assert config["query"] == "Generate report for ${client_name}"

    def test_template_variable_resolution_preserves_existing_syntax(self):
        """Test that existing ${variable_name} syntax is preserved."""
        query_with_mixed_syntax = "Process {new_var} and keep ${existing_var}"
        expected_result = "Process ${new_var} and keep ${existing_var}"

        with patch('app.components.ai.agentic_ai.preprocess_template_variables') as mock_preprocess:
            mock_preprocess.return_value = expected_result

            result = self.component._resolve_template_variables(
                query_with_mixed_syntax, self.context, "query"
            )

            assert result == expected_result
            mock_preprocess.assert_called_once_with(query_with_mixed_syntax, "AgenticAI.query")

    def test_integration_template_variable_processing(self):
        """Integration test demonstrating real template variable processing without mocks."""
        # Test real template variable processing using the actual preprocessing function
        test_cases = [
            {
                "input": "Analyze {document_type} for {client_name}",
                "expected": "Analyze ${document_type} for ${client_name}",
                "description": "Simple template variable conversion"
            },
            {
                "input": "Process {new_var} but keep ${existing_var}",
                "expected": "Process ${new_var} but keep ${existing_var}",
                "description": "Mixed syntax preservation"
            },
            {
                "input": {
                    "task": "Review {document}",
                    "context": "Client: ${client_name}",
                    "deadline": "2024-12-31"
                },
                "expected": {
                    "task": "Review ${document}",
                    "context": "Client: ${client_name}",
                    "deadline": "2024-12-31"
                },
                "description": "Dictionary with template variables"
            },
            {
                "input": "No template variables here",
                "expected": "No template variables here",
                "description": "No changes when no template variables"
            }
        ]

        for case in test_cases:
            result = self.component._resolve_template_variables(
                case["input"], self.context, "test_field"
            )
            assert result == case["expected"], f"Failed for case: {case['description']}"
