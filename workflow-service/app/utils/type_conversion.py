"""
Type conversion utilities for safe string-to-number conversion
Provides safe conversion functions with fallback defaults
"""

def safe_int_convert(value, default=0):
    """Convert string or numeric value to int, with fallback to default."""
    if value is None or value == "":
        return default
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def safe_float_convert(value, default=0.0):
    """Convert string or numeric value to float, with fallback to default."""
    if value is None or value == "":
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_bool_convert(value, default=False):
    """Convert string or other value to bool, with fallback to default."""
    if value is None or value == "":
        return default
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        return value.lower() in ('true', '1', 'yes', 'on')
    try:
        return bool(value)
    except (ValueError, TypeError):
        return default