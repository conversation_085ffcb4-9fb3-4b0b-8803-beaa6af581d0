"""
Module for converting between workflow_capture_schema.json and transition_schema.json formats.

This module has been updated to align with the simplified orchestration engine transition schema
that supports enhanced parameter resolution through explicit field mappings.

Key enhancements:
- Generates explicit 'mapping' arrays in input_data entries for precise field-to-field connections
- Supports nested field path resolution (e.g., 'result.result') for single result resolution
- Maintains backward compatibility with existing field mapping logic
- Removes deprecated connection metadata (priority, required, fallback_value, handle_id)
- Adds semantic type extraction from schema 1 to format field in schema 2 for orchestration engine
- Template variable preprocessing: converts {variable_name} syntax to ${variable_name} format
"""

import os
import re
import json
import logging
from collections import defaultdict
from typing import Dict, List, Any, Optional, Set, Tuple, Union
from app.services.workflow_builder.node_combiner import combine_nodes
from app.utils.type_conversion import safe_int_convert, safe_bool_convert

# Set up logger for template variable preprocessing
logger = logging.getLogger(__name__)


def extract_template_variables_from_value(value: Any) -> Dict[str, str]:
    """
    Extract template variables from a value to make them available as separate parameters.

    This function finds all template variables in the format ${variable_name} and creates
    a dictionary of variable names as keys with placeholder values.

    Args:
        value: The value to extract template variables from

    Returns:
        Dictionary of template variables with placeholder values
    """
    import re

    variables = {}

    try:
        if isinstance(value, str):
            # Find all template variables in the format ${variable_name}
            template_pattern = r"\$\{([^}]+)\}"
            matches = re.findall(template_pattern, value)

            for var_name in matches:
                # Set as placeholder for orchestration engine to resolve at runtime
                variables[var_name] = f"${{{var_name}}}"

        elif isinstance(value, dict):
            # Recursively extract from dictionary values
            for v in value.values():
                extracted = extract_template_variables_from_value(v)
                variables.update(extracted)

        elif isinstance(value, list):
            # Recursively extract from list items
            for item in value:
                extracted = extract_template_variables_from_value(item)
                variables.update(extracted)

    except Exception as e:
        logger.error(f"Error extracting template variables: {str(e)}")

    return variables


def preprocess_template_variables(field_value: Any, context: str = "") -> Any:
    """
    Preprocess template variables by converting formats to the orchestration engine format.

    This function enables template variable support by converting template formats:
    - {variable_name} → ${variable_name}
    - ${variable_name} → ${variable_name} (normalized)
    - ${{variable_name}} → ${{variable_name}} (frontend double-brace format kept as-is)

    The ${{variable_name}} format is kept as-is for frontend compatibility.

    Args:
        field_value: The field value to preprocess (can be string, dict, list, or other types)
        context: Context information for logging (e.g., field name, node ID)

    Returns:
        The preprocessed field value with template variables in proper format
    """
    if field_value is None:
        return field_value

    try:
        if isinstance(field_value, str):
            # Process string values for template variable conversion
            return _process_string_template_variables(field_value, context)
        elif isinstance(field_value, dict):
            # Recursively process dictionary values
            processed_dict = {}
            try:
                for key, value in field_value.items():
                    processed_dict[key] = preprocess_template_variables(
                        value, f"{context}.{key}" if context else key
                    )
                return processed_dict
            except Exception as e:
                logger.error(f"Error processing dictionary in {context}: {str(e)}")
                return field_value
        elif isinstance(field_value, list):
            # Recursively process list items
            processed_list = []
            try:
                for i, item in enumerate(field_value):
                    processed_list.append(
                        preprocess_template_variables(
                            item, f"{context}[{i}]" if context else f"[{i}]"
                        )
                    )
                return processed_list
            except Exception as e:
                logger.error(f"Error processing list in {context}: {str(e)}")
                return field_value
        else:
            # Return other types unchanged
            return field_value

    except Exception as e:
        logger.error(f"Error preprocessing template variables in {context}: {str(e)}")
        # Return original value on error to maintain system stability
        return field_value


def _process_string_template_variables(value: str, context: str = "") -> str:
    """
    Process a string value to convert template syntax to the orchestration engine format.

    This function converts template variable formats to the standardized format:
    - {variable_name} → ${variable_name}
    - ${variable_name} → ${variable_name} (normalized)
    - ${{variable_name}} → ${{variable_name}} (frontend double-brace format kept as-is)

    The ${{variable_name}} format is kept as-is for frontend compatibility.

    Args:
        value: The string value to process
        context: Context information for logging

    Returns:
        The processed string with template variables in proper format
    """
    if not isinstance(value, str):
        return value

    processed_value = value
    conversion_count = 0

    # Convert {variable_name} patterns (only valid variable names, not JSON, and not already ${{var}})
    legacy_pattern1 = (
        r"(?<![\$\\]){([a-zA-Z_][a-zA-Z0-9_]*)}(?!\})"  # Negative lookahead to avoid ${{var}}
    )
    matches1 = list(re.finditer(legacy_pattern1, processed_value))

    for match in reversed(matches1):  # Process in reverse to maintain positions
        variable_name = match.group(1)
        old_text = match.group(0)
        new_text = f"${{{variable_name}}}"

        # Replace the specific match
        start, end = match.span()
        processed_value = processed_value[:start] + new_text + processed_value[end:]
        conversion_count += 1
        logger.debug(f"Converted legacy template variable {old_text} to {new_text} in {context}")

    # Convert ${variable_name} patterns to ${variable_name} (already correct format, but normalize)
    legacy_pattern2 = (
        r"\$\{([a-zA-Z_][a-zA-Z0-9_]*)\}(?!\})"  # Negative lookahead to avoid ${{var}}
    )
    matches2 = list(re.finditer(legacy_pattern2, processed_value))

    for match in reversed(matches2):  # Process in reverse to maintain positions
        variable_name = match.group(1)
        old_text = match.group(0)
        new_text = f"${{{variable_name}}}"  # Keep single brace format

        # Replace the specific match (this is mainly for normalization)
        start, end = match.span()
        processed_value = processed_value[:start] + new_text + processed_value[end:]
        conversion_count += 1
        logger.debug(f"Normalized template variable {old_text} to {new_text} in {context}")

    # Keep ${{variable_name}} patterns as-is (double braces from frontend)
    double_brace_pattern = (
        r"\$\{\{([a-zA-Z_][a-zA-Z0-9_]*)\}\}"  # Match ${{variable_name}}
    )
    matches3 = list(re.finditer(double_brace_pattern, processed_value))

    for match in reversed(matches3):  # Process in reverse to maintain positions
        variable_name = match.group(1)
        old_text = match.group(0)
        new_text = f"${{{{{variable_name}}}}}"  # Keep double brace format

        # Replace the specific match (keep as double braces)
        start, end = match.span()
        processed_value = processed_value[:start] + new_text + processed_value[end:]
        conversion_count += 1
        logger.debug(f"Kept double-brace template variable {old_text} as {new_text} in {context}")

    if conversion_count > 0:
        logger.info(
            f"Preprocessed {conversion_count} template variable(s) to new ${{{{variable}}}} format in {context}"
        )

    return processed_value


def detect_template_variables(data: Any, path: str = "") -> List[Dict[str, Any]]:
    """
    Detect all template variables in a data structure.

    This function recursively scans through data structures to find all template variables
    using both {variable_name} and ${variable_name} syntax patterns.

    Args:
        data: The data structure to scan (can be string, dict, list, or other types)
        path: Current path in the data structure for context

    Returns:
        List of dictionaries containing variable information:
        [
            {
                "variable_name": "user_name",
                "syntax": "{variable_name}" or "${variable_name}",
                "location": "path.to.field",
                "value": "original string containing variable"
            }
        ]
    """
    variables = []

    try:
        if isinstance(data, str):
            variables.extend(_extract_variables_from_string(data, path))
        elif isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                variables.extend(detect_template_variables(value, current_path))
        elif isinstance(data, list):
            for i, item in enumerate(data):
                current_path = f"{path}[{i}]" if path else f"[{i}]"
                variables.extend(detect_template_variables(item, current_path))
    except Exception as e:
        logger.error(f"Error detecting template variables at {path}: {str(e)}")

    return variables


def _extract_variables_from_string(value: str, location: str) -> List[Dict[str, Any]]:
    """
    Extract template variables from a string value using the specific ${{variable}} syntax.

    This function now uses a unique and explicit syntax for template variables:
    - ${{variable_name}} - The only supported template variable format

    This eliminates confusion with JSON content like {"key": "value"} which uses single braces.

    Args:
        value: The string to scan for variables
        location: Location path for context

    Returns:
        List of variable information dictionaries
    """
    variables = []

    # Find ${{variable_name}} patterns - unique template variable syntax
    template_pattern = r"\$\{\{([^}]+)\}\}"
    template_matches = re.finditer(template_pattern, value)

    for match in template_matches:
        variable_name = match.group(1).strip()  # Extract and clean variable name
        if variable_name:  # Skip empty variable names
            # Validate variable name (letters, numbers, underscores only)
            if re.match(r"^[a-zA-Z_][a-zA-Z0-9_]*$", variable_name):
                variables.append(
                    {
                        "variable_name": variable_name,
                        "syntax": f"${{{{{variable_name}}}}}",
                        "location": location,
                        "value": value,
                        "start_pos": match.start(),
                        "end_pos": match.end(),
                    }
                )
            else:
                logger.warning(
                    f"Invalid template variable name '{variable_name}' at {location}. "
                    f"Variable names must start with letter/underscore and contain only letters, numbers, and underscores."
                )

    return variables


def validate_template_variables(variables: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Validate template variables for syntax correctness and potential issues.

    Args:
        variables: List of variable information from detect_template_variables()

    Returns:
        Dictionary containing validation results:
        {
            "valid": True/False,
            "errors": [list of error messages],
            "warnings": [list of warning messages],
            "variable_summary": {
                "total_count": int,
                "unique_variables": set,
                "syntax_breakdown": {"template": int, "dollar": int}
            }
        }
    """
    errors = []
    warnings = []
    unique_variables = set()
    syntax_counts = {"template": 0, "dollar": 0}

    # Variable name validation patterns
    valid_name_pattern = r"^[a-zA-Z_][a-zA-Z0-9_]*$"

    for var_info in variables:
        variable_name = var_info["variable_name"]
        syntax = var_info["syntax"]
        location = var_info["location"]

        # Count syntax types
        if syntax.startswith("${"):
            syntax_counts["dollar"] += 1
        else:
            syntax_counts["template"] += 1

        # Validate variable name format
        if not re.match(valid_name_pattern, variable_name):
            errors.append(
                f"Invalid variable name '{variable_name}' at {location}. "
                f"Variable names must start with letter/underscore and contain only "
                f"letters, numbers, and underscores."
            )

        # Check for reserved keywords (common programming reserved words)
        reserved_keywords = {
            "if",
            "else",
            "for",
            "while",
            "def",
            "class",
            "import",
            "from",
            "return",
            "try",
            "except",
            "finally",
            "with",
            "as",
            "pass",
            "break",
            "continue",
            "and",
            "or",
            "not",
            "in",
            "is",
            "lambda",
            "global",
            "nonlocal",
            "yield",
            "true",
            "false",
            "null",
            "none",
            "undefined",
        }

        if variable_name.lower() in reserved_keywords:
            warnings.append(
                f"Variable name '{variable_name}' at {location} is a reserved keyword. "
                f"Consider using a different name to avoid potential conflicts."
            )

        # Check for very long variable names
        if len(variable_name) > 50:
            warnings.append(
                f"Variable name '{variable_name}' at {location} is very long ({len(variable_name)} chars). "
                f"Consider using a shorter, more descriptive name."
            )

        # Check for variables with only whitespace (but don't double-report empty names)
        if not variable_name.strip() and variable_name:  # Only if not already caught by regex
            errors.append(f"Empty variable name at {location}")

        # Track unique variables
        unique_variables.add(variable_name)

    # Check for mixed syntax usage (warning)
    if syntax_counts["template"] > 0 and syntax_counts["dollar"] > 0:
        warnings.append(
            f"Mixed template variable syntax detected: {syntax_counts['template']} "
            f"{{variable}} and {syntax_counts['dollar']} ${{variable}} patterns. "
            f"Consider using consistent syntax throughout the workflow."
        )

    return {
        "valid": len(errors) == 0,
        "errors": errors,
        "warnings": warnings,
        "variable_summary": {
            "total_count": len(variables),
            "unique_variables": unique_variables,
            "syntax_breakdown": syntax_counts,
        },
    }


def validate_workflow_template_variables(workflow_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate all template variables in a complete workflow schema.

    Args:
        workflow_data: Complete workflow data structure

    Returns:
        Comprehensive validation results including variable detection and validation
    """
    logger.info("Starting template variable validation for workflow")

    # Detect all variables in the workflow
    all_variables = detect_template_variables(workflow_data, "workflow")

    # Validate the detected variables
    validation_results = validate_template_variables(all_variables)

    # Add detection results to validation output
    validation_results["detected_variables"] = all_variables

    # Log summary
    summary = validation_results["variable_summary"]
    logger.info(
        f"Template variable validation complete: "
        f"{summary['total_count']} total variables, "
        f"{len(summary['unique_variables'])} unique, "
        f"{len(validation_results['errors'])} errors, "
        f"{len(validation_results['warnings'])} warnings"
    )

    return validation_results


class HandleMappingConflictError(ValueError):
    """
    Exception raised when multiple sources try to connect to the same target handle.

    This enforces the one-to-one handle mapping constraint where each target handle
    can only receive data from exactly one source handle.
    """

    pass


def _validate_one_to_one_handle_mapping(edges: List[Dict[str, Any]]) -> None:
    """
    Validate that each target handle has exactly one source handle (one-to-one constraint).

    This function enforces the constraint that one target handle_id can only have
    one source handle_id. If multiple sources try to connect to the same target
    handle, it raises a HandleMappingConflictError.

    Special exception: The "tools" handle on AgenticAI nodes is allowed to have multiple
    connections as it's designed to accept multiple tool connections.

    Args:
        edges: List of edges from the workflow

    Raises:
        HandleMappingConflictError: If multiple sources connect to the same target handle
    """
    # Track target handles and their sources
    target_handle_sources = (
        {}
    )  # {(target_node, target_handle): [(source_node, source_handle, edge_id), ...]}

    for edge in edges:
        source_node = edge.get("source")
        target_node = edge.get("target")
        source_handle = edge.get("sourceHandle")
        target_handle = edge.get("targetHandle")
        edge_id = edge.get("id", "unknown")

        # Skip edges without handle information (e.g., flow connections)
        if not source_handle or not target_handle:
            continue

        # Create unique target handle identifier
        target_key = (target_node, target_handle)

        # Track this source for the target handle
        if target_key not in target_handle_sources:
            target_handle_sources[target_key] = []

        target_handle_sources[target_key].append((source_node, source_handle, edge_id))

    # Check for violations of one-to-one constraint
    conflicts = []
    for target_key, sources in target_handle_sources.items():
        if len(sources) > 1:
            target_node, target_handle = target_key

            # SPECIAL EXCEPTION: Allow multiple connections to "tools" handle on AgenticAI nodes
            # This is by design - the tools handle accepts multiple tool connections
            if target_handle == "tools":
                print(
                    f"   ℹ️  Allowing multiple connections to {target_node}.{target_handle} (tools handle accepts multiple connections)"
                )
                continue

            conflicts.append(
                {
                    "target_node": target_node,
                    "target_handle": target_handle,
                    "sources": sources,
                    "conflict_count": len(sources),
                }
            )

    # If conflicts found, raise detailed error
    if conflicts:
        error_details = []
        for conflict in conflicts:
            target_node = conflict["target_node"]
            target_handle = conflict["target_handle"]
            sources = conflict["sources"]

            source_descriptions = []
            for source_node, source_handle, edge_id in sources:
                source_descriptions.append(f"'{source_node}.{source_handle}' (edge: {edge_id})")

            error_details.append(
                f"Target handle '{target_node}.{target_handle}' has {len(sources)} sources: {', '.join(source_descriptions)}"
            )

        error_message = (
            f"Handle mapping constraint violation: One target handle can only have one source handle.\n"
            f"Found {len(conflicts)} conflicts:\n"
            + "\n".join(f"  - {detail}" for detail in error_details)
            + f"\n\nPlease modify your workflow to ensure each target handle receives data from only one source handle."
        )

        raise HandleMappingConflictError(error_message)


def build_graph_from_workflow(workflow_data: Dict[str, Any]) -> Tuple[Dict, Dict, Set]:
    """
    Build a directed graph from the workflow edges.

    Args:
        workflow_data: The workflow data in workflow_capture_schema format

    Returns:
        Tuple containing:
        - graph: A dictionary mapping node IDs to lists of connected node IDs
        - edge_map: A dictionary mapping (source, target) tuples to edge data
        - all_nodes: A set of all node IDs in the graph
    """
    graph = defaultdict(list)
    edge_map = {}
    all_nodes = set()

    for edge in workflow_data.get("edges", []):
        source_node = edge["source"]
        target_node = edge["target"]
        graph[source_node].append(target_node)
        edge_map[(source_node, target_node)] = edge
        all_nodes.add(source_node)
        all_nodes.add(target_node)

    return graph, edge_map, all_nodes


def compute_levels(graph: Dict, all_nodes: Set, start_nodes: Optional[List] = None) -> Dict:
    """
    Compute levels for each node using DFS.

    Args:
        graph: A dictionary mapping node IDs to lists of connected node IDs
        all_nodes: A set of all node IDs in the graph
        start_nodes: Optional list of starting node IDs. If None, uses nodes with no incoming edges.

    Returns:
        A dictionary mapping node IDs to their computed level
    """
    levels = {node: 0 for node in all_nodes}

    # If no start nodes provided, find nodes with no incoming edges
    if not start_nodes:
        incoming_edges = defaultdict(int)
        for node in all_nodes:
            for neighbor in graph.get(node, []):
                incoming_edges[neighbor] += 1

        start_nodes = [node for node in all_nodes if incoming_edges.get(node, 0) == 0]

    def dfs(node, current_level, path):
        if levels[node] < current_level:
            levels[node] = current_level
        for neighbor in graph.get(node, []):
            if neighbor in path:  # Avoid cycles
                continue
            if levels.get(neighbor, 0) < current_level + 1:
                levels[neighbor] = current_level + 1
                dfs(neighbor, current_level + 1, path + [neighbor])

    for start_node in start_nodes:
        if start_node in levels:
            dfs(start_node, 0, [start_node])

    return levels


def group_nodes_by_level(levels: Dict) -> Dict:
    """
    Group nodes by their computed level.

    Args:
        levels: A dictionary mapping node IDs to their computed level

    Returns:
        A dictionary mapping levels to lists of node IDs at that level
    """
    level_groups = defaultdict(list)
    for node, level in levels.items():
        level_groups[level].append(node)
    return level_groups


def map_input_type_to_data_type(input_type: str) -> str:
    """
    Map input_type from workflow schema to data_type in transition schema.

    Args:
        input_type: The input type from workflow schema

    Returns:
        The corresponding data type for transition schema
    """
    type_mapping = {
        "string": "string",
        "multiline": "string",
        "int": "number",
        "integer": "number",
        "float": "number",
        "bool": "boolean",
        "dropdown": "string",
        "dict": "object",
        "list": "array",
        "handle": "string",
        "button": "string",
        "code_editor": "string",
    }
    return type_mapping.get(input_type, "string")


def map_output_type_to_data_type(output_type: str) -> str:
    """
    Map output_type from workflow schema to data_type in transition schema.

    Args:
        output_type: The output type from workflow schema

    Returns:
        The corresponding data type for transition schema
    """
    type_mapping = {
        "string": "string",
        "number": "number",
        "boolean": "boolean",
        "array": "array",
        "object": "object",
    }
    return type_mapping.get(output_type, "string")


def extract_semantic_type_from_field(field_name: str, field_props: Dict[str, Any]) -> str:
    """
    Extract semantic type from field name and properties for orchestration engine format field.

    This function analyzes field names and existing format information to determine
    the appropriate semantic type that will be used by the orchestration engine
    for enhanced result formatting.

    Args:
        field_name: The name of the field
        field_props: The field properties from the original schema

    Returns:
        Semantic type string (e.g., 'email', 'url', 'datetime', 'string')
    """
    # Check if format is already specified in the original schema
    existing_format = field_props.get("format")
    if existing_format and isinstance(existing_format, str):
        return existing_format.strip().lower()

    # Extract semantic type from field name patterns
    field_name_lower = field_name.lower()

    # Email patterns
    if any(pattern in field_name_lower for pattern in ["email", "mail", "e_mail"]):
        return "email"

    # URL patterns
    if any(pattern in field_name_lower for pattern in ["url", "link", "href", "uri", "website"]):
        return "url"

    # DateTime patterns
    if any(
        pattern in field_name_lower
        for pattern in [
            "date",
            "time",
            "timestamp",
            "created_at",
            "updated_at",
            "modified_at",
            "datetime",
            "created_on",
            "updated_on",
        ]
    ):
        return "datetime"

    # Currency patterns
    if any(
        pattern in field_name_lower
        for pattern in ["price", "cost", "amount", "currency", "money", "fee"]
    ):
        return "currency"

    # Percentage patterns
    if any(pattern in field_name_lower for pattern in ["percent", "percentage", "rate"]):
        return "percentage"

    # Image patterns
    if any(
        pattern in field_name_lower for pattern in ["image", "img", "photo", "picture", "avatar"]
    ):
        return "image"

    # Audio patterns
    if any(pattern in field_name_lower for pattern in ["audio", "sound", "music", "voice"]):
        return "audio"

    # Video patterns
    if any(pattern in field_name_lower for pattern in ["video", "movie", "clip"]):
        return "video"

    # File path patterns
    if any(pattern in field_name_lower for pattern in ["path", "file", "filename", "filepath"]):
        return "file_path"

    # ID/Identifier patterns
    if any(pattern in field_name_lower for pattern in ["id", "identifier", "uuid", "guid"]):
        return "identifier"

    # Status patterns
    if any(pattern in field_name_lower for pattern in ["status", "state", "condition"]):
        return "status"

    # Color patterns
    if any(pattern in field_name_lower for pattern in ["color", "colour"]):
        return "color"

    # Default fallback
    return "string"


def extract_universal_handles(node_definition: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
    """
    Universal handle extraction from any node definition.

    This function discovers input and output handles from any node type without
    hardcoded logic. It works with MCP, Component, API, Custom, and unknown node types.

    Args:
        node_definition: Node definition from workflow schema

    Returns:
        Dictionary with 'input_handles' and 'output_handles' arrays
    """
    handles = {"input_handles": [], "output_handles": []}

    # Extract input handles from node definition
    inputs = node_definition.get("inputs", [])
    for input_def in inputs:
        if input_def.get("is_handle", False):  # Only include handles
            handle_info = {
                "handle_id": input_def.get("name", ""),
                "handle_name": input_def.get("display_name", input_def.get("name", "")),
                "data_type": map_input_type_to_data_type(input_def.get("input_type", "string")),
                "required": input_def.get("required", False),
                "description": input_def.get("info", input_def.get("description", "")),
            }
            handles["input_handles"].append(handle_info)

    # Extract output handles from node definition
    outputs = node_definition.get("outputs", [])
    for output_def in outputs:
        handle_info = {
            "handle_id": output_def.get("name", ""),
            "handle_name": output_def.get("display_name", output_def.get("name", "")),
            "data_type": map_output_type_to_data_type(output_def.get("output_type", "string")),
            "description": output_def.get("info", output_def.get("description", "")),
        }
        handles["output_handles"].append(handle_info)

    # Add workflow-specific output handles if this is a workflow node
    node_name = node_definition.get("name", "")
    if node_name.startswith("workflow-") or node_definition.get("type") == "Workflow":
        # Add single result output handle for workflow execution
        workflow_output = {
            "handle_id": "result",
            "handle_name": "Result",
            "data_type": "array",
            "description": "Workflow execution result (can be json, string, number, or any array type)"
        }

        # Only add workflow output if it doesn't already exist
        existing_handle_ids = {handle["handle_id"] for handle in handles["output_handles"]}
        if workflow_output["handle_id"] not in existing_handle_ids:
            handles["output_handles"].append(workflow_output)

    return handles


def determine_result_structure_pattern(node_data: Dict[str, Any]) -> str:
    """
    Determine the expected result structure pattern for any node type.

    This function analyzes the node to determine how results are likely structured
    without hardcoded node type logic.

    Args:
        node_data: Complete node data from workflow

    Returns:
        Result structure pattern: 'direct', 'nested_result', 'nested_output', or 'dynamic'
    """
    node_type = node_data.get("type", "")
    node_definition = node_data.get("definition", {})

    # Check if node has explicit result structure hints
    if "result_structure" in node_definition:
        return node_definition["result_structure"]

    # For nodes with single output handle, use dynamic discovery
    outputs = node_definition.get("outputs", [])
    if len(outputs) == 1:
        return "dynamic"

    # For nodes with multiple outputs, assume direct mapping
    if len(outputs) > 1:
        return "direct"

    # Default to dynamic for unknown patterns
    return "dynamic"


def create_universal_handle_mapping(
    source_handle: str, target_handle: str, edge_id: str
) -> Dict[str, str]:
    """
    Create universal handle mapping without node type analysis.

    This function creates direct handle-to-handle mappings without any
    interpretation or strategy determination.

    Args:
        source_handle: Source handle ID from edge.sourceHandle
        target_handle: Target handle ID from edge.targetHandle
        edge_id: Edge ID for traceability

    Returns:
        Direct handle mapping dictionary
    """
    return {
        "source_handle_id": source_handle,
        "target_handle_id": target_handle,
        "edge_id": edge_id,
    }


def create_handle_registry(node_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create handle registry for a node with all handle metadata.

    This function creates a complete registry of input and output handles
    for any node type without hardcoded logic.

    Args:
        node_data: Complete node data from workflow

    Returns:
        Handle registry with input and output handles
    """
    node_definition = node_data.get("definition", {})
    handles = extract_universal_handles(node_definition)

    return {"input_handles": handles["input_handles"], "output_handles": handles["output_handles"]}


def create_result_path_hints(node_data: Dict[str, Any]) -> Dict[str, str]:
    """
    Create result path hints for output handles based on node analysis.

    This function provides hints for where to find handle data in results
    without hardcoded patterns.

    Args:
        node_data: Complete node data from workflow

    Returns:
        Dictionary mapping handle IDs to result path hints
    """
    result_path_hints = {}
    node_definition = node_data.get("definition", {})
    result_pattern = determine_result_structure_pattern(node_data)

    # Get all output handles
    outputs = node_definition.get("outputs", [])

    for output_def in outputs:
        handle_id = output_def.get("name", "")

        if result_pattern == "direct":
            # Direct mapping: handle_id maps to same field in result
            result_path_hints[handle_id] = handle_id
        elif result_pattern == "nested_result":
            # Nested under result: handle_id maps to result.handle_id
            result_path_hints[handle_id] = f"result.{handle_id}"
        elif result_pattern == "nested_output":
            # Nested under output_data: handle_id maps to output_data.handle_id
            result_path_hints[handle_id] = f"output_data.{handle_id}"
        else:  # dynamic
            # Dynamic discovery: provide fallback patterns
            result_path_hints[handle_id] = handle_id  # Start with direct, discover at runtime

    return result_path_hints


def create_dynamic_discovery_config(node_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create dynamic discovery configuration for universal result path resolution.

    This function creates configuration that enables the orchestration engine
    to dynamically discover result structures for any node type.

    Args:
        node_data: Complete node data from workflow

    Returns:
        Dynamic discovery configuration
    """
    result_pattern = determine_result_structure_pattern(node_data)
    node_definition = node_data.get("definition", {})

    # Base fallback patterns that work universally
    base_patterns = [
        "{handle_id}",  # Direct handle mapping
        "result",  # Common result field
        "output_data",  # Common output field
        "response",  # API response field
        "data",  # Generic data field
        "result.{handle_id}",  # Nested under result
        "output_data.{handle_id}",  # Nested under output_data
        "result.result",  # Double nested result
        "response.data",  # API response data
        "content",  # Content field
        "value",  # Value field
    ]

    # Add node-specific patterns based on outputs
    node_specific_patterns = []
    outputs = node_definition.get("outputs", [])
    for output_def in outputs:
        handle_name = output_def.get("name", "")
        if handle_name:
            # Add variations for this specific handle
            node_specific_patterns.extend(
                [
                    f"result.{handle_name}",
                    f"output_data.{handle_name}",
                    f"response.{handle_name}",
                    f"data.{handle_name}",
                ]
            )

    # Combine patterns with node-specific ones first (higher priority)
    all_patterns = node_specific_patterns + base_patterns

    # Create validation rules for discovered paths
    validation_rules = [
        {
            "rule_type": "type_check",
            "rule_config": {
                "allowed_types": ["string", "number", "object", "array", "boolean"],
                "reject_null": False,
                "reject_undefined": True,
            },
        },
        {
            "rule_type": "structure_check",
            "rule_config": {
                "min_depth": 0,
                "max_depth": 5,
                "allow_nested_objects": True,
                "allow_arrays": True,
            },
        },
        {
            "rule_type": "content_check",
            "rule_config": {
                "min_length": 0,
                "reject_empty_strings": False,
                "reject_empty_objects": False,
                "reject_empty_arrays": False,
            },
        },
    ]

    return {
        "enabled": result_pattern == "dynamic",
        "fallback_patterns": all_patterns,
        "validation_rules": validation_rules,
    }


def create_universal_result_resolver(node_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create universal result resolver that can handle any node result structure.

    This function creates a complete resolver configuration that the orchestration
    engine can use to extract data from any node type without hardcoded logic.

    Args:
        node_data: Complete node data from workflow

    Returns:
        Universal result resolver configuration
    """
    node_definition = node_data.get("definition", {})
    node_type = node_data.get("type", "unknown")
    original_type = node_data.get("originalType", "")

    # Fix node_type for workflow nodes - use singular "workflow" instead of "workflows"
    if node_type == "workflows" or original_type.startswith("workflow-"):
        node_type = "workflow"

    # Create handle registry
    handle_registry = create_handle_registry(node_data)

    # Create result path hints
    result_path_hints = create_result_path_hints(node_data)

    # Add workflow-specific result path hints
    if node_type == "workflow":
        workflow_hints = {
            "result": "result"
        }
        result_path_hints.update(workflow_hints)

    # Create dynamic discovery config
    dynamic_discovery = create_dynamic_discovery_config(node_data)

    # Determine expected result structure
    result_pattern = determine_result_structure_pattern(node_data)

    # Create resolver configuration
    resolver_config = {
        "node_type": node_type,
        "expected_result_structure": result_pattern,
        "handle_registry": handle_registry,
        "result_path_hints": result_path_hints,
        "dynamic_discovery": dynamic_discovery,
        "extraction_metadata": {
            "supports_multiple_outputs": len(handle_registry.get("output_handles", [])) > 1,
            "supports_nested_results": result_pattern
            in ["nested_result", "nested_output", "dynamic"],
            "requires_dynamic_discovery": result_pattern == "dynamic",
            "primary_output_handle": (
                handle_registry.get("output_handles", [{}])[0].get("handle_id", "")
                if handle_registry.get("output_handles")
                else ""
            ),
        },
    }

    return resolver_config


def validate_handle_compatibility(
    source_handle_info: Dict[str, Any], target_handle_info: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Validate compatibility between source and target handles.

    This function checks if a source handle can be connected to a target handle
    based on data types and other compatibility factors.

    Args:
        source_handle_info: Source handle information
        target_handle_info: Target handle information

    Returns:
        Compatibility validation result
    """
    source_type = source_handle_info.get("data_type", "string")
    target_type = target_handle_info.get("data_type", "string")

    # Define type compatibility matrix
    type_compatibility = {
        "string": ["string", "object", "any"],
        "number": ["number", "string", "any"],
        "boolean": ["boolean", "string", "any"],
        "object": ["object", "string", "any"],
        "array": ["array", "object", "any"],
        "any": ["string", "number", "boolean", "object", "array", "any"],
    }

    is_compatible = target_type in type_compatibility.get(source_type, [])

    # Check for potential issues
    warnings = []
    if source_type == "object" and target_type == "string":
        warnings.append("Object to string conversion may require serialization")
    if source_type == "array" and target_type != "array":
        warnings.append("Array to non-array conversion may lose data")

    return {
        "compatible": is_compatible,
        "confidence": (
            "high" if is_compatible and not warnings else "medium" if is_compatible else "low"
        ),
        "warnings": warnings,
        "source_type": source_type,
        "target_type": target_type,
    }


def convert_conditional_node_to_transition_node(node: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert a conditional node from workflow_capture_schema format to transition_schema node format.

    Args:
        node: A conditional node from workflow_capture_schema

    Returns:
        A node in transition_schema format for conditional component
    """
    node_id = node["id"]
    node_data = node["data"]
    node_definition = node_data["definition"]

    print(f"   🔧 Converting conditional node to transition node: {node_id}")

    # Create server tools for conditional component
    server_tools = [
        {
            "tool_id": 1,
            "tool_name": "conditional",
            "tool_params": {
                "items": [
                    {
                        "field_name": "conditions",
                        "data_type": "array",
                        "field_value": None,  # Will be populated by conditional component
                    },
                    {
                        "field_name": "default_transition",
                        "data_type": "string",
                        "field_value": None,  # Will be populated by conditional component
                    },
                ]
            },
        }
    ]

    # Use the conditional node name or fallback to "conditional"
    node_base_id = node_definition.get("name", "conditional")

    transition_node = {
        "id": node_base_id,
        "server_script_path": "node-executor-service",
        "server_tools": server_tools,
    }

    print(f"   ✅ Conditional node converted: {node_base_id}")
    return transition_node


def convert_conditional_node_to_transition_node(node: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert a conditional node from workflow_capture_schema format to transition_schema node format.

    Args:
        node: A conditional node from workflow_capture_schema

    Returns:
        A node in transition_schema format for conditional component
    """
    node_id = node["id"]
    node_data = node["data"]
    node_definition = node_data["definition"]

    print(f"   🔧 Converting conditional node to transition node: {node_id}")

    # Create server tools for conditional component
    server_tools = [
        {
            "tool_id": 1,
            "tool_name": "conditional",
            "tool_params": {
                "items": [
                    {
                        "field_name": "conditions",
                        "data_type": "array",
                        "field_value": None,  # Will be populated by conditional component
                    },
                    {
                        "field_name": "default_transition",
                        "data_type": "string",
                        "field_value": None,  # Will be populated by conditional component
                    },
                ]
            },
        }
    ]

    # Use the conditional node name or fallback to "conditional"
    node_base_id = node_definition.get("name", "conditional")

    transition_node = {
        "id": node_base_id,
        "server_script_path": "node-executor-service",
        "server_tools": server_tools,
    }

    print(f"   ✅ Conditional node converted: {node_base_id}")
    return transition_node


def convert_conditional_node_to_transition_node(node: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert a conditional node from workflow_capture_schema format to transition_schema node format.

    Args:
        node: A conditional node from workflow_capture_schema

    Returns:
        A node in transition_schema format for conditional component
    """
    node_id = node["id"]
    node_data = node["data"]
    node_definition = node_data["definition"]

    print(f"   🔧 Converting conditional node to transition node: {node_id}")

    # Create server tools for conditional component
    server_tools = [
        {
            "tool_id": 1,
            "tool_name": "conditional",
            "input_schema": {
                "predefined_fields": [
                    {
                        "field_name": "conditions",
                        "data_type": {
                            "type": "array",
                            "description": "Array of condition objects that define routing logic",
                        },
                        "required": True,
                    },
                    {
                        "field_name": "default_transition",
                        "data_type": {
                            "type": "string",
                            "description": "Default transition to use when no conditions match",
                        },
                        "required": False,
                    },
                ]
            },
            "output_schema": {
                "predefined_fields": [
                    {
                        "field_name": "routing_decision",
                        "data_type": {
                            "type": "object",
                            "description": "Contains the target transition and evaluation metadata",
                            "format": "ROUTING_DECISION",
                        },
                    },
                    {
                        "field_name": "metadata",
                        "data_type": {
                            "type": "object",
                            "description": "Contains condition evaluation details and statistics",
                            "format": "METADATA",
                        },
                    },
                ]
            },
        }
    ]

    # Use the original node ID to match what the transition references
    # This ensures the orchestration engine can find the node definition
    node_base_id = node_id

    transition_node = {
        "id": node_base_id,
        "server_script_path": "node-executor-service",
        "server_tools": server_tools,
    }

    print(f"   ✅ Conditional node converted: {node_base_id}")
    return transition_node


def convert_node_to_transition_node(
    node: Dict[str, Any], mcp_configs: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Convert a node from workflow_capture_schema format to transition_schema node format.

    Args:
        node: A node from workflow_capture_schema
        mcp_configs: MCP configurations from workflow_capture_schema

    Returns:
        A node in transition_schema format
    """
    node_id = node["id"]
    node_data = node["data"]
    node_type = node_data.get("type", "component")  # Default to "component" if missing
    node_definition = node_data["definition"]

    # Handle conditional nodes specially
    if is_conditional_node(node):
        return convert_conditional_node_to_transition_node(node)

    # Find MCP config for this node if it exists
    mcp_config = next((config for config in mcp_configs if config["node_id"] == node_id), None)

    # Determine if this is an MCP node based on node.data.type
    is_mcp_node = node_type == "mcp" or (mcp_config is not None)

    server_tools = []

    if is_mcp_node and mcp_config:
        # Create server tools for MCP node
        tool_name = mcp_config.get("selected_tool_name", "UnknownTool")

        # Check if mcp_info.input_schema is available
        if "mcp_info" in node_definition and "input_schema" in node_definition["mcp_info"]:
            # Convert input_schema from mcp_info to the required format with predefined_fields
            mcp_input_schema = node_definition["mcp_info"]["input_schema"]
            input_schema = {"predefined_fields": []}

            # Extract properties from the mcp_input_schema
            if "properties" in mcp_input_schema:
                properties = mcp_input_schema["properties"]
                required_fields = mcp_input_schema.get("required", [])

                for field_name, field_props in properties.items():
                    # Create data_type object with proper handling of nested objects
                    field_type = field_props.get("type", "string")
                    mapped_type = map_input_type_to_data_type(field_type)
                    data_type = {
                        "type": mapped_type,
                        "description": field_props.get("description", ""),
                    }

                    # Handle object properties if present
                    if field_props.get("type") == "object" and "properties" in field_props:
                        data_type["properties"] = field_props["properties"]

                    # Handle array items if present
                    if field_props.get("type") == "array" and "items" in field_props:
                        item_type = field_props["items"].get("type", "string")
                        mapped_item_type = map_input_type_to_data_type(item_type)
                        data_type["items"] = {"type": mapped_item_type}

                        # Handle object properties in array items
                        if (
                            field_props["items"].get("type") == "object"
                            and "properties" in field_props["items"]
                        ):
                            data_type["items"]["properties"] = field_props["items"]["properties"]

                            # Handle required fields in array items
                            if "required" in field_props["items"]:
                                data_type["items"]["required"] = field_props["items"]["required"]

                    # Add the field to predefined_fields
                    input_schema["predefined_fields"].append(
                        {
                            "field_name": field_name,
                            "data_type": data_type,
                            "required": field_name in required_fields,
                        }
                    )
        else:
            # Create input schema from tool_schema
            tool_schema = mcp_config.get("tool_schema", {})
            input_schema = {"predefined_fields": []}

            # Extract fields from tool schema
            properties = tool_schema.get("properties", {})
            required_fields = tool_schema.get("required", [])

            for field_name, field_props in properties.items():
                field_type = field_props.get("type", "string")
                # Map the field type to ensure compatibility with transition schema
                mapped_type = map_input_type_to_data_type(field_type)
                input_schema["predefined_fields"].append(
                    {
                        "field_name": field_name,
                        "data_type": {
                            "type": mapped_type,
                            "description": field_props.get("description", ""),
                        },
                        "required": field_name in required_fields,
                    }
                )

        # Check if mcp_info.output_schema is available
        if "mcp_info" in node_definition and "output_schema" in node_definition["mcp_info"]:
            # Convert output_schema from mcp_info to the required format with predefined_fields
            mcp_output_schema = node_definition["mcp_info"]["output_schema"]
            output_schema = {"predefined_fields": []}

            # Extract properties from the mcp_output_schema
            if mcp_output_schema and "properties" in mcp_output_schema:
                properties = mcp_output_schema["properties"]

                for field_name, field_props in properties.items():
                    # Extract semantic type for orchestration engine format field
                    semantic_type = extract_semantic_type_from_field(field_name, field_props)

                    # Create data_type object with proper handling of nested objects
                    field_type = field_props.get("type", "string")
                    mapped_type = map_output_type_to_data_type(field_type)
                    data_type = {
                        "type": mapped_type,
                        "description": field_props.get("description", ""),
                        "format": semantic_type,  # Add semantic type to format field
                    }

                    # Handle object properties if present
                    if field_props.get("type") == "object" and "properties" in field_props:
                        data_type["properties"] = field_props["properties"]

                    # Handle array items if present
                    if field_props.get("type") == "array" and "items" in field_props:
                        # Extract semantic type for array items
                        item_semantic_type = "string"  # Default for array items
                        if "properties" in field_props["items"]:
                            # For object arrays, use object semantic type
                            item_semantic_type = extract_semantic_type_from_field(
                                f"{field_name}_item", field_props["items"]
                            )

                        item_type = field_props["items"].get("type", "string")
                        mapped_item_type = map_output_type_to_data_type(item_type)
                        data_type["items"] = {
                            "type": mapped_item_type,
                            "format": item_semantic_type,  # Add semantic type to array items
                        }

                        # Handle object properties in array items
                        if (
                            field_props["items"].get("type") == "object"
                            and "properties" in field_props["items"]
                        ):
                            data_type["items"]["properties"] = field_props["items"]["properties"]

                            # Handle required fields in array items
                            if "required" in field_props["items"]:
                                data_type["items"]["required"] = field_props["items"]["required"]

                    # Add the field to predefined_fields
                    output_schema["predefined_fields"].append(
                        {
                            "field_name": field_name,
                            "data_type": data_type,
                        }
                    )
        else:
            # Create output schema (assuming standard response format)
            output_schema = {
                "predefined_fields": [
                    {
                        "field_name": "response",
                        "data_type": {
                            "type": "string",
                            "description": "Response from the MCP tool",
                            "format": "string",  # Default semantic type for response
                        },
                    }
                ]
            }

        # Create server tool
        # For MCP nodes, ALWAYS use the tool_name from mcp_info if available
        # According to transition_schema_fields.md line 74: For MCP nodes: nodes.data.definition.mcp_info.tool_name
        if (
            node_type == "mcp"
            and "mcp_info" in node_definition
            and "tool_name" in node_definition["mcp_info"]
        ):
            tool_name = node_definition["mcp_info"]["tool_name"]
        else:
            # Fallback to the node definition name
            tool_name = node_definition["name"]

        # For MCP nodes, ALWAYS use the tool_name from mcp_info.tool_name
        # According to transition_schema_fields.md line 74: For MCP nodes: nodes.data.definition.mcp_info.tool_name
        if (
            node_type == "mcp"
            and "mcp_info" in node_definition
            and "tool_name" in node_definition["mcp_info"]
        ):
            server_tool_name = node_definition["mcp_info"]["tool_name"]
        else:
            server_tool_name = tool_name

        # Debug output
        print(f"Node type: {node_type}")
        print(f"Node definition name: {node_definition['name']}")
        if "mcp_info" in node_definition and "tool_name" in node_definition["mcp_info"]:
            print(f"MCP info tool_name: {node_definition['mcp_info']['tool_name']}")
        print(f"Server tool name: {server_tool_name}")

        server_tools.append(
            {
                "tool_id": 1,  # Assign a default ID
                "tool_name": server_tool_name,
                "input_schema": input_schema,
                "output_schema": output_schema,
            }
        )
    else:
        # Create server tools for API node
        # Check if mcp_info.input_schema is available
        if "mcp_info" in node_definition and "input_schema" in node_definition["mcp_info"]:
            # Convert input_schema from mcp_info to the required format with predefined_fields
            mcp_input_schema = node_definition["mcp_info"]["input_schema"]
            input_schema = {"predefined_fields": []}

            # Extract properties from the mcp_input_schema
            if "properties" in mcp_input_schema:
                properties = mcp_input_schema["properties"]
                required_fields = mcp_input_schema.get("required", [])

                for field_name, field_props in properties.items():
                    # Create data_type object with proper handling of nested objects
                    field_type = field_props.get("type", "string")
                    mapped_type = map_input_type_to_data_type(field_type)
                    data_type = {
                        "type": mapped_type,
                        "description": field_props.get("description", ""),
                    }

                    # Handle object properties if present
                    if field_props.get("type") == "object" and "properties" in field_props:
                        data_type["properties"] = field_props["properties"]

                    # Handle array items if present
                    if field_props.get("type") == "array" and "items" in field_props:
                        item_type = field_props["items"].get("type", "string")
                        mapped_item_type = map_input_type_to_data_type(item_type)
                        data_type["items"] = {"type": mapped_item_type}

                        # Handle object properties in array items
                        if (
                            field_props["items"].get("type") == "object"
                            and "properties" in field_props["items"]
                        ):
                            data_type["items"]["properties"] = field_props["items"]["properties"]

                            # Handle required fields in array items
                            if "required" in field_props["items"]:
                                data_type["items"]["required"] = field_props["items"]["required"]

                    # Add the field to predefined_fields
                    input_schema["predefined_fields"].append(
                        {
                            "field_name": field_name,
                            "data_type": data_type,
                            "required": field_name in required_fields,
                        }
                    )
        else:
            # Map inputs to input schema
            input_schema = {"predefined_fields": []}

            for input_def in node_definition.get("inputs", []):
                # Skip tool-related inputs for agent nodes - tools should be callable functions, not input sources
                if node_definition.get("name") == "AgenticAI" and input_def.get("name") == "tools":
                    print(
                        f"         ⏭️  SKIPPED: Tool input field (tools should be callable functions, not input sources)"
                    )
                    continue

                input_schema["predefined_fields"].append(
                    {
                        "field_name": input_def["name"],
                        "data_type": {
                            "type": map_input_type_to_data_type(
                                input_def.get("input_type", "string")
                            ),
                            "description": input_def.get("info", ""),
                        },
                        "required": input_def.get("required", False),
                    }
                )

        # Check if mcp_info.output_schema is available
        if "mcp_info" in node_definition and "output_schema" in node_definition["mcp_info"]:
            # Convert output_schema from mcp_info to the required format with predefined_fields
            mcp_output_schema = node_definition["mcp_info"]["output_schema"]
            output_schema = {"predefined_fields": []}

            # Extract properties from the mcp_output_schema
            if mcp_output_schema and "properties" in mcp_output_schema:
                properties = mcp_output_schema["properties"]

                for field_name, field_props in properties.items():
                    # Extract semantic type for orchestration engine format field
                    semantic_type = extract_semantic_type_from_field(field_name, field_props)

                    # Create data_type object with proper handling of nested objects
                    field_type = field_props.get("type", "string")
                    mapped_type = map_output_type_to_data_type(field_type)
                    data_type = {
                        "type": mapped_type,
                        "description": field_props.get("description", ""),
                        "format": semantic_type,  # Add semantic type to format field
                    }

                    # Handle object properties if present
                    if field_props.get("type") == "object" and "properties" in field_props:
                        data_type["properties"] = field_props["properties"]

                    # Handle array items if present
                    if field_props.get("type") == "array" and "items" in field_props:
                        # Extract semantic type for array items
                        item_semantic_type = "string"  # Default for array items
                        if "properties" in field_props["items"]:
                            # For object arrays, use object semantic type
                            item_semantic_type = extract_semantic_type_from_field(
                                f"{field_name}_item", field_props["items"]
                            )

                        item_type = field_props["items"].get("type", "string")
                        mapped_item_type = map_output_type_to_data_type(item_type)
                        data_type["items"] = {
                            "type": mapped_item_type,
                            "format": item_semantic_type,  # Add semantic type to array items
                        }

                        # Handle object properties in array items
                        if (
                            field_props["items"].get("type") == "object"
                            and "properties" in field_props["items"]
                        ):
                            data_type["items"]["properties"] = field_props["items"]["properties"]

                            # Handle required fields in array items
                            if "required" in field_props["items"]:
                                data_type["items"]["required"] = field_props["items"]["required"]

                    # Add the field to predefined_fields
                    output_schema["predefined_fields"].append(
                        {
                            "field_name": field_name,
                            "data_type": data_type,
                        }
                    )
        else:
            # Map outputs to output schema
            output_schema = {"predefined_fields": []}

            for output_def in node_definition.get("outputs", []):
                # Extract semantic type from output definition
                field_name = output_def["name"]
                semantic_type = extract_semantic_type_from_field(field_name, output_def)

                output_schema["predefined_fields"].append(
                    {
                        "field_name": field_name,
                        "data_type": {
                            "type": map_output_type_to_data_type(
                                output_def.get("output_type", "string")
                            ),
                            "description": output_def.get(
                                "info", output_def.get("description", "")
                            ),
                            "format": semantic_type,  # Add semantic type to format field
                        },
                    }
                )

        # Create server tool
        server_tools.append(
            {
                "tool_id": 1,  # Assign a default ID
                "tool_name": node_definition["name"],
                "input_schema": input_schema,
                "output_schema": output_schema,
            }
        )

    # Create the transition node
    # Determine server_script_path based on node type
    server_script_path = ""
    if (
        node_type == "mcp"
        and node.get("type") == "CustomNode"
        and "mcp_info" in node_definition
        and "sse_url" in node_definition["mcp_info"]
    ):
        # For MCP nodes: if nodes.data.type is mcp and nodes.type is CustomNode then nodes.data.definition.mcp_info.sse_url
        server_script_path = node_definition["mcp_info"]["sse_url"]
    else:
        # For Components nodes: null (empty string)
        server_script_path = ""

    # Determine the node ID based on node type
    if node_type == "mcp":
        # For MCP nodes, use the server_id from mcp_info if available
        if "mcp_info" in node_definition and "server_id" in node_definition["mcp_info"]:
            # Use the server_id from mcp_info
            node_base_id = node_definition["mcp_info"]["server_id"]
        else:
            # Fallback to the name from the node definition
            node_base_id = node_definition["name"]
    else:
        # For Components nodes, use the definition name
        node_base_id = node_definition["name"]

    transition_node = {
        "id": node_base_id,
        "server_script_path": server_script_path,
        "server_tools": server_tools,
    }

    return transition_node


def create_transition_from_edge(
    edge: Dict[str, Any],
    nodes: List[Dict[str, Any]],
    mcp_configs: List[Dict[str, Any]],
    edges: List[Dict[str, Any]],
    sequence: int,
    is_first: bool = False,
    has_reflection: bool = False,
    is_end: bool = False,
) -> Dict[str, Any]:
    """
    Create a transition from an edge in workflow_capture_schema.

    Args:
        edge: An edge from workflow_capture_schema
        nodes: List of nodes from workflow_capture_schema
        sequence: Sequence number for this transition
        is_first: Whether this is the first transition
        has_reflection: Whether this transition has reflection
        is_end: Whether this is an end transition

    Returns:
        A transition in transition_schema format
    """
    source_id = edge["source"]
    target_id = edge["target"]
    # Note: sourceHandle and targetHandle are used in other parts of the code when processing edges

    # Find the source and target nodes
    source_node = next((node for node in nodes if node["id"] == source_id), None)
    target_node = next((node for node in nodes if node["id"] == target_id), None)

    if not source_node or not target_node:
        raise ValueError(f"Could not find nodes for edge: {edge}")

    # Determine transition type
    transition_type = "standard"
    if is_first:
        transition_type = "initial"
    elif has_reflection:
        transition_type = "reflection"

    # Determine execution type based on node type
    source_node_type = source_node["data"]["type"]
    source_original_type = source_node["data"].get("originalType", "")

    # execution_type is based directly on node.data.type as per updated transition_schema_fields.md
    if is_loop_node(source_node):
        execution_type = "loop"
    elif source_node_type == "mcp":
        execution_type = "MCP"
    elif source_node_type == "agent" or source_node_type == "employee":
        execution_type = "agent"
    elif source_node_type == "workflows" or source_original_type.startswith("workflow-"):
        execution_type = "workflow"
    else:
        execution_type = "Components"

    # Create tools_to_use configuration
    tools_to_use = []
    source_definition = source_node["data"]["definition"]
    source_node_type = source_node["data"]["type"]

    # Find MCP config for this node if it exists
    mcp_config = next((config for config in mcp_configs if config["node_id"] == source_id), None)

    # Determine if this is an MCP node based on node.data.type
    is_mcp_node = source_node_type == "mcp" or (mcp_config is not None)
    is_agent_node = source_node_type in ["agent", "employee"]
    is_conditional_node_flag = is_conditional_node(source_node)

    if is_conditional_node_flag:
        # Handle conditional nodes - create special tool configuration for conditional routing
        tool_id = 1
        tool_name = "conditional"

        source_config = source_node["data"].get("config", {})

        # Build conditions array from the node configuration
        conditions = []
        num_additional_conditions = int(source_config.get("num_additional_conditions", 0))
        total_conditions = 1 + num_additional_conditions  # Base 1 + additional

        for i in range(1, total_conditions + 1):
            operator = source_config.get(f"condition_{i}_operator", "equals")
            expected_value = source_config.get(f"condition_{i}_expected_value", "")

            condition = {
                "operator": operator,
                "next_transition": ""  # Will be populated during transition creation
            }

            # Only add expected_value for operators that need it
            if operator not in ["exists", "is_empty"]:
                condition["expected_value"] = expected_value

            # Add variable_name for global_context source
            source = source_config.get("source", "node_output")
            if source == "global_context":
                variable_name = source_config.get("variable_name", "")
                if variable_name:
                    condition["variable_name"] = variable_name

            conditions.append(condition)

        tool = {
            "tool_id": tool_id,
            "tool_name": tool_name,
            "tool_params": {
                "items": [
                    {
                        "field_name": "conditions",
                        "data_type": "array",
                        "field_value": conditions
                    },
                    {
                        "field_name": "input_data",
                        "data_type": "string",
                        "field_value": source_config.get("input_data", "")
                    },
                    {
                        "field_name": "source",
                        "data_type": "string",
                        "field_value": source_config.get("source", "node_output")
                    }
                ]
            }
        }
    elif is_agent_node:
        # Handle agent nodes - create tool configuration for agent execution
        tool_id = 1
        tool_name = source_definition.get("name", "agent_task")

        tool = {
            "tool_id": tool_id,
            "tool_name": tool_name,
            "tool_params": {"items": []},
        }

        # For agent nodes, extract agent configuration from node config
        source_config = source_node["data"].get("config", {})

        # Extract AgenticAI inspector panel fields and bundle them into agent_config
        agent_config_data = source_config.get("agent_config", {})
        
        # Extract agent information from node definition if available
        agent_info = source_definition.get("agent_info", {})
        if agent_info:
            # Merge agent_info into agent_config_data
            agent_config_data.update({
                "id": agent_info.get("id"),
                "name": agent_info.get("name"),
                "description": agent_info.get("description"),
                "system_message": agent_info.get("system_message"),
                "model_provider": agent_info.get("model_provider"),
                "model_name": agent_info.get("model_name"),
                "temperature": agent_info.get("temperature"),
                "max_tokens": agent_info.get("max_tokens"),
                "agent_tools": agent_info.get("agent_tools", []),
                "files": agent_info.get("files", []),
                "urls": agent_info.get("urls", []),
                "department": agent_info.get("department"),
                "is_bench_employee": agent_info.get("is_bench_employee", False)
            })

        # Bundle model-related parameters into model_config
        model_config = {}
        model_fields = {
            "model_provider": source_config.get("model_provider", ""),
            "model": source_config.get("model_name", ""),  # Map model_name to model
            "temperature": source_config.get("temperature"),
            "max_tokens": source_config.get("max_tokens"),
            "base_url": source_config.get("base_url"),
            "api_key": source_config.get("api_key"),
        }

        # Only add model fields that have values
        for field, value in model_fields.items():
            if value is not None and value != "":
                model_config[field] = value

        # Add model_config to agent_config if it has any fields
        if model_config:
            agent_config_data["model_config"] = model_config

        # Add other inspector panel fields to agent_config (excluding system_message and input_variables which are now top-level)
        other_inspector_fields = [
            "description",
            "autogen_agent_type",
            "termination_condition",
        ]

        for field in other_inspector_fields:
            if field in source_config:
                agent_config_data[field] = source_config[field]
        
        # Extract system_message and input_variables for top-level parameters
        system_message = source_config.get("system_message", "")
        input_variables = source_config.get("input_variables", {})

        # Preprocess system_message for template variables (same as query and input_variables)
 
        # Determine agent_type based on node type and agent_info
        node_type = source_node["data"].get("type", "component")
        is_employee = (node_type == "employee" or 
                      agent_config_data.get("is_bench_employee", False))
        agent_type_value = "employee" if is_employee else "component"

        # Build agent tool parameters based on the agent executor interface
        agent_params = [
            {
                "field_name": "agent_type",
                "data_type": "string",
                "field_value": agent_type_value,
            },
            {
                "field_name": "execution_type",
                "data_type": "string",
                "field_value": source_config.get("execution_type", "response"),
            },
            {
                "field_name": "query",
                "data_type": "string",
                "field_value": source_config.get("query", ""),
            },
            {
                "field_name": "input",
                "data_type": "string",
                "field_value": source_config.get("input", ""),
            },
            {
                "field_name": "system_message",
                "data_type": "string",
                "field_value": system_message,
            },
            {
                "field_name": "input_variables",
                "data_type": "object",
                "field_value": input_variables,
            },
            {
                "field_name": "agent_config",
                "data_type": "object",
                "field_value": agent_config_data,
            },
        ]

        # Add optional parameters if they exist
        if "session_timeout" in source_config:
            agent_params.append(
                {
                    "field_name": "session_timeout",
                    "data_type": "number",
                    "field_value": source_config["session_timeout"],
                }
            )

        # Extract template variables from processed query, system_message, and input_variables fields
        template_variables = {}

        # Extract from query field (after preprocessing)
        query_value = source_config.get("query", "")
        if query_value:
            processed_query = preprocess_template_variables(query_value, "query")
            query_variables = extract_template_variables_from_value(processed_query)
            template_variables.update(query_variables)

        # Extract from system_message field (after preprocessing)
        if system_message:
            system_message_variables = extract_template_variables_from_value(system_message)
            template_variables.update(system_message_variables)

        # Extract from input_variables field (after preprocessing)
        input_vars = source_config.get("input_variables", {})
        if input_vars:
            processed_input_vars = preprocess_template_variables(input_vars, "input_variables")
            input_var_variables = extract_template_variables_from_value(processed_input_vars)
            template_variables.update(input_var_variables)

        # Use extracted template variables or fallback to explicit variables in config
        variables_to_use = template_variables
        if "variables" in source_config:
            # Merge explicit variables with extracted template variables
            explicit_variables = source_config["variables"]
            if isinstance(explicit_variables, dict):
                variables_to_use.update(explicit_variables)

        # Add variables parameter if we have any variables
        if variables_to_use:
            agent_params.append(
                {
                    "field_name": "variables",
                    "data_type": "object",
                    "field_value": variables_to_use,
                }
            )

        if "organization_id" in source_config:
            agent_params.append(
                {
                    "field_name": "organization_id",
                    "data_type": "string",
                    "field_value": source_config["organization_id"],
                }
            )

        if "use_knowledge" in source_config:
            agent_params.append(
                {
                    "field_name": "use_knowledge",
                    "data_type": "boolean",
                    "field_value": source_config["use_knowledge"],
                }
            )

        tool["tool_params"]["items"] = agent_params

    elif source_node_type == "workflows" or source_original_type.startswith("workflow-"):
        # Handle workflow nodes - create tool configuration for workflow execution
        tool_id = 1
        tool_name = "WorkflowExecutor"

        tool = {
            "tool_id": tool_id,
            "tool_name": tool_name,
            "tool_params": {"items": []},
        }

        # Extract workflow information from node definition
        workflow_info = source_definition.get("workflow_info", {})
        workflow_id = workflow_info.get("id", source_original_type.replace("workflow-", ""))

        # Create field mappings from input connections
        field_mappings = {}

        # Get input handles from the node definition
        inputs = source_definition.get("inputs", [])
        for input_def in inputs:
            input_name = input_def.get("name", "")
            # Map each input to the current transition (will be resolved during execution)
            field_mappings[input_name] = f"transition-{source_id}"

        # Build workflow tool parameters
        workflow_params = [
            {
                "field_name": "workflow_id",
                "data_type": "string",
                "field_value": workflow_id,
            },
            {
                "field_name": "field_mappings",
                "data_type": "object",
                "field_value": field_mappings,
            },
        ]

        tool["tool_params"]["items"] = workflow_params

    elif is_mcp_node and mcp_config:
        # Add a tool based on the MCP config
        # Get tool_id from mcp_info if available, otherwise use 1
        tool_id = 1
        if "mcp_info" in source_definition and "tool_id" in source_definition["mcp_info"]:
            tool_id = source_definition["mcp_info"]["tool_id"]

        # According to transition_schema_fields.md:
        # tool_name: nodes.data.definition.mcp_info.tool_name if execution type is MCP Server, otherwise nodes.data.definition.name
        if (
            execution_type == "MCP"
            and "mcp_info" in source_definition
            and "tool_name" in source_definition["mcp_info"]
        ):
            # For MCP nodes, use tool_name from mcp_info
            tool_name = source_definition["mcp_info"]["tool_name"]
        else:
            # For other nodes, use the node definition name
            tool_name = source_definition["name"]

        tool = {
            "tool_id": tool_id,
            "tool_name": tool_name,
            "tool_params": {"items": []},
        }

        # Check if mcp_info.tool_params is available
        if "mcp_info" in source_definition and "tool_params" in source_definition["mcp_info"]:
            # Use tool_params from mcp_info
            tool["tool_params"] = source_definition["mcp_info"]["tool_params"]
        else:
            # Add parameters from tool_args
            tool_args = mcp_config.get("tool_args", {})
            tool_schema = mcp_config.get("tool_schema", {})
            properties = tool_schema.get("properties", {})

            for param_name, param_value in tool_args.items():
                # Get the data type from the tool schema
                field_props = properties.get(param_name, {})
                field_type = field_props.get("type", "string")
                data_type = map_input_type_to_data_type(field_type)

                # Preprocess JSON strings and template variables in param_value
                json_preprocessed_param_value = preprocess_field_value(param_value)
                preprocessed_param_value = preprocess_template_variables(
                    json_preprocessed_param_value, f"MCP.{tool_name}.{param_name}"
                )

                # Handle nested objects in field_value
                if isinstance(preprocessed_param_value, dict):
                    # For nested objects like keywords, use 'object' as the data_type string
                    # The schema requires data_type to be a string, not an object
                    tool["tool_params"]["items"].append(
                        {
                            "field_name": param_name,
                            "data_type": "object",
                            "field_value": preprocessed_param_value,
                        }
                    )
                else:
                    # For simple values, use the data type from the schema
                    tool["tool_params"]["items"].append(
                        {
                            "field_name": param_name,
                            "data_type": data_type,
                            "field_value": preprocessed_param_value,
                        }
                    )
    else:
        # Add a tool based on the node type
        # For non-MCP nodes, tool_id is always 1
        tool_id = 1

        # For non-MCP nodes, use node_definition.name as per updated transition_schema_fields.md
        tool_name = source_definition["name"]

        # If this is an MCP node, check if mcp_info.tool_name is available
        if "mcp_info" in source_definition and "tool_name" in source_definition["mcp_info"]:
            tool_name = source_definition["mcp_info"]["tool_name"]

        tool = {
            "tool_id": tool_id,
            "tool_name": tool_name,
            "tool_params": {"items": []},
        }

        # For non-MCP nodes, use inputs from the node definition
        if "inputs" in source_definition:
            # Create tool_params from inputs
            inputs = source_definition.get("inputs", [])
            source_config = source_node["data"].get("config", {})

            for input_def in inputs:
                # Get field_name from input definition
                field_name = input_def.get("name", "")

                # Get field_value from source_config for matching field value, if not present use null
                field_value = source_config.get(field_name, None)

                # Get data_type from input_type
                data_type = map_input_type_to_data_type(input_def.get("input_type", "string"))

                # Preprocess JSON strings and template variables in field_value
                json_preprocessed_field_value = preprocess_field_value(field_value)
                preprocessed_field_value = preprocess_template_variables(
                    json_preprocessed_field_value, f"{tool_name}.{field_name}"
                )

                # Handle nested objects in field_value
                if isinstance(preprocessed_field_value, dict):
                    # For nested objects like keywords, use 'object' as the data_type string
                    # The schema requires data_type to be a string, not an object
                    tool["tool_params"]["items"].append(
                        {
                            "field_name": field_name,
                            "data_type": "object",
                            "field_value": preprocessed_field_value,
                        }
                    )
                else:
                    # For simple values, use the data type from the schema
                    tool["tool_params"]["items"].append(
                        {
                            "field_name": field_name,
                            "data_type": data_type,
                            "field_value": preprocessed_field_value,
                        }
                    )

    tools_to_use.append(tool)

    # Create input_data - connections coming into this node
    input_data = []
    # Find all edges that target this node
    for node in nodes:
        node_id = node["id"]
        if node_id != source_id:  # Don't include self-connections
            # Check if there's an edge from this node to the source node
            for out_edge in edges:
                if out_edge["source"] == node_id and out_edge["target"] == source_id:
                    # Found an incoming edge
                    data_type = "string"  # Default
                    # Try to determine data type from the source handle
                    source_handle_name = out_edge.get("sourceHandle")
                    if source_handle_name:
                        # Find the output definition in the source node
                        source_node_def = node["data"]["definition"]
                        output_def = next(
                            (
                                out
                                for out in source_node_def.get("outputs", [])
                                if out["name"] == source_handle_name.split("_")[0]
                            ),
                            None,
                        )
                        if output_def:
                            data_type = map_output_type_to_data_type(
                                output_def.get("output_type", "string")
                            )

                    source_node_def = node["data"]["definition"]
                    source_server_id = node_id
                    if "mcp_info" in source_node_def and "server_id" in source_node_def["mcp_info"]:
                        source_server_id = source_node_def["mcp_info"]["server_id"]
                    else:
                        source_server_id = source_node_def.get("display_name", node_id)

                    # Get target node definition (the current node)
                    target_node_def = target_node["data"]["definition"]

                    # Get target node ID (the current node's server ID)
                    target_server_id = ""
                    if "mcp_info" in target_node_def and "server_id" in target_node_def["mcp_info"]:
                        target_server_id = target_node_def["mcp_info"]["server_id"]
                    else:
                        target_server_id = target_node_def.get("display_name", target_id)

                    # Create input_data entry with handle-based mapping support
                    input_data_entry = {
                        "from_transition_id": f"transition-{node_id}",
                        "source_node_id": source_server_id,
                        "data_type": data_type,
                        "handle_mappings": [],
                    }

                    # Skip connections from start node - they should not be included
                    if (
                        "data" in node
                        and "definition" in node["data"]
                        and "originalType" in node["data"]
                        and node["data"]["originalType"] == "StartNode"
                    ):
                        continue  # Skip start node connections entirely

                    # Extract sourceHandle and targetHandle for direct handle mapping
                    source_handle = out_edge.get("sourceHandle")
                    target_handle = out_edge.get("targetHandle")

                    # Skip tool-related edges - tools should be callable functions, not input sources
                    if target_handle == "tools":
                        continue  # Skip tool connections entirely

                    if source_handle and target_handle:
                        # Create direct handle-to-handle mapping
                        handle_mapping = {
                            "source_transition_id": f"transition-{node_id}",
                            "source_handle_id": source_handle,
                            "target_handle_id": target_handle,
                            "edge_id": out_edge.get("id", ""),
                        }
                        input_data_entry["handle_mappings"].append(handle_mapping)

                        # Store for backward compatibility with existing field mapping logic
                        input_data_entry["_source_handle"] = source_handle
                        input_data_entry["_target_handle"] = target_handle

                    # Only add input_data_entry if it has handle mappings (not from start node)
                    if input_data_entry["handle_mappings"]:
                        input_data.append(input_data_entry)

    output_data = []
    for out_edge in edges:
        if out_edge["source"] == source_id:
            target_node_id = out_edge["target"]
            data_type = "string"  # Default

            # Try to determine data type from the source handle
            source_handle_name = out_edge.get("sourceHandle")
            if source_handle_name:
                # Find the output definition in the source node
                output_def = next(
                    (
                        out
                        for out in source_definition.get("outputs", [])
                        if out["name"] == source_handle_name.split("_")[0]
                    ),
                    None,
                )
                if output_def:
                    data_type = map_output_type_to_data_type(
                        output_def.get("output_type", "string")
                    )

            # Find the target node to get its server_id from mcp_info
            target_node = next((n for n in nodes if n["id"] == target_node_id), None)
            target_server_id = target_node_id  # Default to ID if node not found

            if target_node and "data" in target_node and "definition" in target_node["data"]:
                target_node_def = target_node["data"]["definition"]
                if "mcp_info" in target_node_def and "server_id" in target_node_def["mcp_info"]:
                    target_server_id = target_node_def["mcp_info"]["server_id"]
                else:
                    target_server_id = target_node_def.get("display_name", target_node_id)

            # Create output_data entry with handle registry
            output_data_entry = {
                "to_transition_id": f"transition-{target_node_id}",
                "target_node_id": target_server_id,  # Using server_id from mcp_info as per updated schema
                "data_type": data_type,
                "output_handle_registry": {"handle_mappings": []},
            }

            # Extract sourceHandle and targetHandle for handle registry
            source_handle = out_edge.get("sourceHandle")
            target_handle = out_edge.get("targetHandle")
            edge_id = out_edge.get("id", "")

            if source_handle and target_handle:
                # Create universal result path using the resolver
                source_node_data = {
                    "data": {"definition": source_definition, "type": source_node_type}
                }
                resolver_config = create_universal_result_resolver(source_node_data)
                result_path_hints = resolver_config.get("result_path_hints", {})
                result_path = result_path_hints.get(source_handle, source_handle)

                # Add handle mapping to registry with enhanced metadata
                handle_mapping = {
                    "handle_id": source_handle,
                    "result_path": result_path,
                    "edge_id": edge_id,
                }
                output_data_entry["output_handle_registry"]["handle_mappings"].append(
                    handle_mapping
                )

                # Store for backward compatibility
                output_data_entry["_source_handle"] = source_handle
                output_data_entry["_target_handle"] = target_handle

            output_data.append(output_data_entry)

    # Create the transition
    # Get the node_id based on node type
    source_definition = source_node["data"]["definition"]
    source_node_type = source_node["data"]["type"]
    server_id = source_id

    if source_node_type == "mcp":
        # For MCP nodes, use the server_id from mcp_info if available
        if "mcp_info" in source_definition and "server_id" in source_definition["mcp_info"]:
            server_id = source_definition["mcp_info"]["server_id"]
        else:
            # Fallback to the name from the node definition
            server_id = source_definition["name"]
    elif source_node_type == "agent" or source_node_type == "employee":
        # For Agent/Employee nodes, use the definition name as the agent identifier
        server_id = source_definition.get("name", f"agent-{source_id}")
    else:
        # For Components nodes, use the definition name
        server_id = source_definition["name"]

    # Check if requires_approval is present in the node definition
    # For MCP nodes, check if requires_approval is in the definition
    requires_approval = False
    if "requires_approval" in source_definition:
        requires_approval = source_definition.get("requires_approval", False)

    # Create universal result resolution metadata
    source_node_data = source_node["data"]
    result_resolution = create_universal_result_resolver(source_node_data)

    transition = {
        "id": f"transition-{source_id}",
        "sequence": sequence,
        "transition_type": transition_type,
        "execution_type": execution_type,
        "node_label": source_node["data"].get(
            "label", ""
        ),  # Add node_label from pre-converted schema
        "node_info": {
            "node_id": server_id,  # Using server_id from mcp_info as per updated transition_schema_fields.md
            "tools_to_use": tools_to_use,
            "input_data": input_data,
            "output_data": output_data,
        },
        "result_resolution": result_resolution,
        "approval_required": requires_approval,  # Add approval_required field from node definition
        "end": is_end,
    }

    # Add reflection if needed
    if has_reflection:
        transition["reflection"] = {"iteration_count": 0, "max_iterations": 3}

    if is_loop_node(source_node):
        source_config = source_node["data"].get("config", {})

        # Check if loop_config is already present (legacy format)
        if "loop_config" in source_config:
            transition["loop_config"] = source_config["loop_config"]
        else:
            # Build loop_config from individual input values (new format)
            loop_config = build_loop_config_from_inputs(source_config, source_node, edges)
            if loop_config:
                transition["loop_config"] = loop_config

        # Override with loop-specific result resolution
        transition["result_resolution"] = create_loop_result_resolution(source_node["data"])

    return transition


def is_conditional_node(node: Dict[str, Any]) -> bool:
    """
    Check if a node is a conditional node.

    Args:
        node: A node from workflow_capture_schema

    Returns:
        True if the node is a conditional node, False otherwise
    """
    try:
        if "data" in node:
            # Check both type and originalType fields
            node_type = node["data"].get("type", "")
            original_type = node["data"].get("originalType", "")

            # Ensure we have strings before using 'in' operator to avoid NoneType errors
            node_type = node_type or ""
            original_type = original_type or ""

            result = (
                "Conditional" in node_type
                or node_type == "ConditionalNode"
                or "Conditional" in original_type
                or original_type == "ConditionalNode"
            )
            print(
                f"[DEBUG] is_conditional_node({node.get('id')}): node_type='{node_type}', original_type='{original_type}', result={result}"
            )
            return result
        return False
    except Exception as e:
        print(f"[DEBUG] Error in is_conditional_node for node {node.get('id')}: {str(e)}")
        print(f"[DEBUG] Node data: {node}")
        if "NoneType" in str(e):
            print(f"[DEBUG] NoneType error in is_conditional_node")
        raise


def analyze_loop_connections(
    loop_node: Dict[str, Any], all_edges: List[Dict[str, Any]]
) -> tuple[List[str], List[str], List[str]]:
    """
    Analyze loop node connections to determine entry and exit transitions for the loop body.

    Loop body flow: loop -> entry_transition -> ... -> exit_transition -> loop (next iteration)
    Final exit flow: loop -> final_exit_transition (when loop completes)

    Args:
        loop_node: The loop node data
        all_edges: All edges in the workflow

    Returns:
        Tuple of (entry_transitions, exit_transitions) as lists of transition IDs
        - entry_transitions: Transitions that receive individual iteration data (current_item)
        - exit_transitions: Same as entry_transitions for simple single-node loop bodies
    """
    if not loop_node or not all_edges:
        return [], []

    loop_node_id = loop_node.get("id")
    if not loop_node_id:
        return [], []

    # Find all outgoing edges from the loop node
    outgoing_edges = [edge for edge in all_edges if edge.get("source") == loop_node_id]

    entry_transitions = []
    final_exit_transitions = []  # These are NOT part of the loop body

    for edge in outgoing_edges:
        source_handle = edge.get("sourceHandle", "")
        target_id = edge.get("target")

        if not target_id:
            continue

        transition_id = f"transition-{target_id}"

        # Determine if this is an entry transition or final exit transition
        if source_handle in ["current_item", "iteration_data", "loop_item"]:
            # This connects to entry transitions (loop body start)
            if transition_id not in entry_transitions:
                entry_transitions.append(transition_id)
        elif source_handle in ["final_results", "aggregated_results", "loop_results"]:
            # This connects to final exit transitions (OUTSIDE loop body, after loop completion)
            if transition_id not in final_exit_transitions:
                final_exit_transitions.append(transition_id)
        else:
            # Default: if no specific handle, treat as entry transition
            if transition_id not in entry_transitions:
                entry_transitions.append(transition_id)

    # Analyze the actual chain to find real exit transitions
    # For chains like: loop -> A -> B -> C -> loop, exit_transitions should be [C], not [A]
    exit_transitions = []

    if entry_transitions:
        print(f"🔍 Analyzing loop body chains for loop {loop_node_id}:")
        print(f"   Entry transitions: {entry_transitions}")

        # Get all nodes that are part of the loop body (excluding the loop node itself)
        loop_body_nodes = []

        # First, find direct entry points from the loop node
        entry_nodes = []
        for edge in all_edges:
            source = edge.get("source")
            target = edge.get("target")
            source_handle = edge.get("sourceHandle", "")

            # If edge comes from loop node with current_item/iteration_data handle, target is in loop body
            if source == loop_node_id and source_handle in [
                "current_item",
                "iteration_data",
                "loop_item",
            ]:
                if target not in entry_nodes:
                    entry_nodes.append(target)
                if target not in loop_body_nodes:
                    loop_body_nodes.append(target)

        # Now recursively find all nodes reachable from entry nodes that don't go back to loop
        nodes_to_process = entry_nodes.copy()
        processed_nodes = set()

        while nodes_to_process:
            current_node = nodes_to_process.pop(0)
            if current_node in processed_nodes:
                continue
            processed_nodes.add(current_node)

            # Find all outgoing edges from current node
            for edge in all_edges:
                if edge.get("source") == current_node:
                    target = edge.get("target")
                    target_handle = edge.get("targetHandle", "")

                    # Skip edges that go back to the loop node (these are exit connections)
                    if target == loop_node_id:
                        continue

                    # Skip edges that go to final result connections (outside loop body)
                    if target_handle in ["final_results", "aggregated_results", "loop_results"]:
                        continue

                    # Add target to loop body if not already included
                    if target not in loop_body_nodes:
                        loop_body_nodes.append(target)
                        nodes_to_process.append(target)

        print(f"   Loop body nodes: {loop_body_nodes}")

        # For each entry transition, follow the chain to find the actual exit(s)
        for entry_id in entry_transitions:
            print(f"   Following chain from entry: {entry_id}")
            chain_exits = find_chain_exit_transition(entry_id, all_edges, loop_body_nodes)
            print(f"   Chain exit(s) found: {chain_exits}")

            # Handle both single exit and multiple parallel exits
            def flatten_and_add_exits(exits):
                """Recursively flatten nested lists and add unique exits"""
                if isinstance(exits, list):
                    for exit_item in exits:
                        flatten_and_add_exits(exit_item)
                else:
                    if exits and exits not in exit_transitions:
                        exit_transitions.append(exits)

            flatten_and_add_exits(chain_exits)

        # If no chain exits found, fall back to entry transitions (simple single-node case)
        if not exit_transitions:
            print(f"   No chain exits found, using entry transitions as exit transitions")
            exit_transitions = entry_transitions.copy()

        print(f"   Final exit transitions: {exit_transitions}")
        print(f"   Final exit transitions (outside loop body): {final_exit_transitions}")

    return entry_transitions, exit_transitions, final_exit_transitions


def find_chain_exit_transition(
    entry_transition_id: str, all_edges: List[Dict[str, Any]], loop_body_nodes: List[str]
) -> Union[str, List[str]]:
    """
    Follow the chain from an entry transition to find the actual exit transition.

    Args:
        entry_transition_id: The ID of the entry transition to start from
        all_edges: List of all edges in the workflow
        loop_body_nodes: List of node IDs that are part of the loop body

    Returns:
        The ID of the exit transition(s), or the entry transition if no exit found.
        Can return a single string or a list of strings for parallel exits.
    """
    print(f"     🔍 Following chain from {entry_transition_id}")

    # Extract node ID from transition ID
    # Handle formats like "transition-CombineTextComponent-1750769520925"
    # The node ID should be the full part after "transition-" (e.g., "CombineTextComponent-1750769520925")
    if entry_transition_id.startswith("transition-"):
        current_node_id = entry_transition_id.replace("transition-", "")
    else:
        current_node_id = entry_transition_id

    print(f"     Starting with node: {current_node_id}")
    visited_nodes = set()

    while current_node_id and current_node_id not in visited_nodes:
        visited_nodes.add(current_node_id)
        print(f"     Visiting node: {current_node_id}")

        # Find outgoing edges from current node
        outgoing_edges = [edge for edge in all_edges if edge.get("source") == current_node_id]
        edge_descriptions = [f'{e.get("source")} -> {e.get("target")}' for e in outgoing_edges]
        print(f"     Outgoing edges: {edge_descriptions}")

        # Filter out edges that go outside the loop body (those are exit connections, not chain continuations)
        chain_edges = [edge for edge in outgoing_edges if edge.get("target") in loop_body_nodes]
        chain_edge_descriptions = [f'{e.get("source")} -> {e.get("target")}' for e in chain_edges]
        print(f"     Chain edges (excluding loop): {chain_edge_descriptions}")

        if not chain_edges:
            # No more chain edges, this is the exit transition
            exit_transition = f"transition-{current_node_id}"
            print(f"     ✅ Found exit transition: {exit_transition}")
            return exit_transition

        if len(chain_edges) == 1:
            # Continue following the chain
            current_node_id = chain_edges[0].get("target")
            print(f"     Continuing to: {current_node_id}")
        else:
            # Multiple outgoing edges (parallel branches), follow each branch
            print(f"     🔀 Multiple edges found (parallel branches): {len(chain_edges)} branches")
            parallel_exits = []

            for i, edge in enumerate(chain_edges):
                branch_target = edge.get("target")
                print(f"     📍 Following parallel branch {i+1}: {branch_target}")

                # Check if this branch target is outside the loop (immediate exit)
                if branch_target not in loop_body_nodes:
                    print(f"     ⚠️ Branch target {branch_target} is outside loop body, skipping")
                    continue

                # For parallel branches, the branch target itself is typically the exit
                # unless it has further sequential connections within the loop
                branch_edges = [
                    e
                    for e in all_edges
                    if e.get("source") == branch_target and e.get("target") in loop_body_nodes
                ]

                if not branch_edges:
                    # No further connections within loop body - this is an exit transition
                    branch_exit = f"transition-{branch_target}"
                    parallel_exits.append(branch_exit)
                    print(f"     ✅ Parallel exit found: {branch_exit}")
                else:
                    # Has further connections, continue following this branch
                    print(
                        f"     🔄 Branch has {len(branch_edges)} further connections, following..."
                    )
                    branch_exit = find_chain_exit_transition(
                        f"transition-{branch_target}", all_edges, loop_body_nodes
                    )
                    if branch_exit:
                        parallel_exits.append(branch_exit)
                        print(f"     ✅ Branch exit found: {branch_exit}")

            if parallel_exits:
                print(
                    f"     🎯 Found {len(parallel_exits)} parallel exit transitions: {parallel_exits}"
                )
                return parallel_exits  # Return list of parallel exits
            else:
                # Fallback to treating branching node as exit
                exit_transition = f"transition-{current_node_id}"
                print(
                    f"     ⚠️ No parallel exits found, falling back to branching node: {exit_transition}"
                )
                return exit_transition

    # If we get here, there might be a cycle or we couldn't find the exit
    print(f"     ⚠️ Cycle detected or couldn't find exit, falling back to: {entry_transition_id}")
    return entry_transition_id  # Fallback to entry transition


def is_node_in_loop_body(
    node_id: str, all_edges: List[Dict[str, Any]], nodes: List[Dict[str, Any]]
) -> bool:
    """
    Check if a node is part of a loop body by analyzing if it's connected to a loop node
    via current_item/iteration handles.

    Args:
        node_id: The ID of the node to check
        all_edges: List of all edges in the workflow
        nodes: List of all nodes in the workflow

    Returns:
        True if the node is part of a loop body, False otherwise
    """
    # Find all loop nodes
    loop_nodes = [node for node in nodes if is_loop_node(node)]

    for loop_node in loop_nodes:
        loop_node_id = loop_node.get("id")
        if not loop_node_id:
            continue

        # Check if this node is connected from the loop via current_item/iteration handles
        for edge in all_edges:
            if (
                edge.get("source") == loop_node_id
                and edge.get("target") == node_id
                and edge.get("sourceHandle") in ["current_item", "iteration_data", "loop_item"]
            ):
                return True

        # Also check if this node is part of a chain that starts from a loop body entry
        entry_transitions, _, _ = analyze_loop_connections(loop_node, all_edges)
        for entry_id in entry_transitions:
            entry_node_id = entry_id.replace("transition-", "")
            if is_node_in_chain_from_entry(node_id, entry_node_id, all_edges, loop_node_id):
                return True

    return False


def is_node_in_chain_from_entry(
    target_node_id: str, entry_node_id: str, all_edges: List[Dict[str, Any]], loop_node_id: str
) -> bool:
    """
    Check if a target node is reachable from an entry node in a loop body chain.
    """
    if target_node_id == entry_node_id:
        return True

    visited = set()
    queue = [entry_node_id]

    while queue:
        current_node_id = queue.pop(0)
        if current_node_id in visited:
            continue
        visited.add(current_node_id)

        if current_node_id == target_node_id:
            return True

        # Find outgoing edges that don't go back to the loop
        for edge in all_edges:
            if (
                edge.get("source") == current_node_id
                and edge.get("target") != loop_node_id
                and edge.get("target") not in visited
            ):
                queue.append(edge.get("target"))

    return False


def build_loop_config_from_inputs(
    config: Dict[str, Any], loop_node: Dict[str, Any] = None, all_edges: List[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Build loop_config from individual input values.

    Args:
        config: Node configuration containing individual input values
        loop_node: The loop node data (optional, for analyzing connections)
        all_edges: All edges in the workflow (optional, for analyzing connections)

    Returns:
        Complete loop_config dictionary or None if insufficient data
    """
    try:
        # Build iteration settings from inputs
        iteration_settings = {
            "parallel_execution": safe_bool_convert(config.get("parallel_execution", False), False),
            "max_concurrent": safe_int_convert(config.get("max_concurrent", 3), 3),
            "preserve_order": safe_bool_convert(config.get("preserve_order", True), True),
            "iteration_timeout": safe_int_convert(config.get("iteration_timeout", 30), 30),
        }

        # Build result aggregation from inputs
        result_aggregation = {
            "aggregation_type": config.get("aggregation_type", "collect_all"),
            "include_metadata": config.get("include_metadata", False),
        }

        # Build error handling from inputs
        error_handling = {
            "on_iteration_error": config.get("on_iteration_error", "continue"),
            "include_errors": config.get("include_errors", False),
        }

        # Build iteration source from inputs using the new oneOf format
        source_type = config.get("source_type", "iteration_list")

        if source_type == "iteration_list":
            iteration_list = config.get("iteration_list", [])
            if isinstance(iteration_list, str):
                # If empty string, it means the input is connected - leave it empty for runtime resolution
                if iteration_list.strip() == "":
                    iteration_list = []
                else:
                    try:
                        import json

                        iteration_list = json.loads(iteration_list)
                    except (json.JSONDecodeError, ValueError) as e:
                        raise ValueError(
                            f"Invalid JSON format for iteration_list: '{iteration_list}'. Error: {str(e)}"
                        )

            # Create iteration_source using the new oneOf format for static list
            # Convert batch_size to integer
            batch_size = safe_int_convert(config.get("batch_size", 1), 1)
            iteration_source = {"iteration_list": iteration_list, "batch_size": batch_size}
        elif source_type == "number_range":
            # Create iteration_source using the new oneOf format for number range
            # Preserve None values for handle mappings, convert others to integers
            start_value = config.get("start", 1)
            end_value = config.get("end", 10)
            step_value = config.get("step", 1)

            # Convert to integers if not None, preserve None for handle mappings
            start = safe_int_convert(start_value, 1) if start_value is not None else None
            end = safe_int_convert(end_value, 10) if end_value is not None else None
            step = safe_int_convert(step_value, 1) if step_value is not None else None

            iteration_source = {"number_range": {"start": start, "end": end}, "step": step}
        else:
            # Fallback to iteration_list format if source_type is unknown
            iteration_source = {"iteration_list": [], "batch_size": 1}

        # Simple exit condition - always process all items
        exit_condition = {"condition_type": "all_items_processed"}

        # Build loop_body_configuration section with proper entry/exit transitions
        entry_transitions, exit_transitions, final_exit_transitions = (
            analyze_loop_connections(loop_node, all_edges)
            if loop_node and all_edges
            else ([], [], [])
        )
        # Note: final_exit_transitions are intentionally NOT included in loop_body_configuration
        # They represent transitions that should execute AFTER the loop completes, not within the loop body

        loop_body_configuration = {
            "entry_transitions": entry_transitions,
            "exit_transitions": exit_transitions,
            "chain_completion_detection": (
                "explicit_exit_transitions" if exit_transitions else "auto_detect_chain_end"
            ),
            "chain_execution_timeout": 300,
            "chain_monitoring": {
                "enable_progress_tracking": True,
                "transition_completion_callbacks": True,
                "state_persistence": False,
            },
            "auto_detection_config": {
                "enable_auto_detection": True,
                "detection_strategy": "hybrid",
            },
        }

        # Build complete loop_config
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": iteration_source,
            "exit_condition": exit_condition,
            "iteration_settings": iteration_settings,
            "result_aggregation": result_aggregation,
            "loop_body_configuration": loop_body_configuration,
            "error_handling": error_handling,
        }

        return loop_config

    except Exception as e:
        print(f"[DEBUG] Error building loop_config from inputs: {str(e)}")
        return None


def create_loop_result_resolution(node_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create loop-specific result resolution configuration.

    Args:
        node_data: Complete node data from workflow

    Returns:
        Loop-specific result resolver configuration
    """
    node_definition = node_data.get("definition", {})

    # Create loop-specific input handles based on node definition
    input_handles = []
    inputs = node_definition.get("inputs", [])

    for input_def in inputs:
        if input_def.get("is_handle", False):
            handle_info = {
                "handle_id": input_def.get("name", ""),
                "handle_name": input_def.get("display_name", input_def.get("name", "")),
                "data_type": map_input_type_to_data_type(input_def.get("input_type", "string")),
                "required": input_def.get("required", False),
                "description": input_def.get("info", input_def.get("description", "")),
            }
            input_handles.append(handle_info)

    # Create loop-specific output handles
    output_handles = [
        {
            "handle_id": "current_item",
            "handle_name": "Current Item (Iteration Output)",
            "data_type": "object",
            "description": "The current item being processed in the loop iteration",
        },
        {
            "handle_id": "final_results",
            "handle_name": "All Results (Exit Output)",
            "data_type": "array",
            "description": "Aggregated results from all loop iterations",
        },
    ]

    # Create loop-specific result path hints
    result_path_hints = {"current_item": "current_item", "final_results": "final_results"}

    # Create loop-specific dynamic discovery configuration
    dynamic_discovery = {
        "enabled": False,
        "fallback_patterns": [
            "result.current_item",
            "output_data.current_item",
            "response.current_item",
            "data.current_item",
            "result.final_results",
            "output_data.final_results",
            "response.final_results",
            "data.final_results",
            "{handle_id}",
            "result",
            "output_data",
            "response",
            "data",
            "result.{handle_id}",
            "output_data.{handle_id}",
            "result.result",
            "response.data",
            "content",
            "value",
        ],
        "validation_rules": [
            {
                "rule_type": "type_check",
                "rule_config": {
                    "allowed_types": ["string", "number", "object", "array", "boolean"],
                    "reject_null": False,
                    "reject_undefined": True,
                },
            },
            {
                "rule_type": "structure_check",
                "rule_config": {
                    "min_depth": 0,
                    "max_depth": 5,
                    "allow_nested_objects": True,
                    "allow_arrays": True,
                },
            },
            {
                "rule_type": "content_check",
                "rule_config": {
                    "min_length": 0,
                    "reject_empty_strings": False,
                    "reject_empty_objects": False,
                    "reject_empty_arrays": False,
                },
            },
        ],
    }

    # Create loop-specific resolver configuration
    resolver_config = {
        "node_type": "loop",
        "expected_result_structure": "direct",
        "handle_registry": {"input_handles": input_handles, "output_handles": output_handles},
        "result_path_hints": result_path_hints,
        "dynamic_discovery": dynamic_discovery,
        "extraction_metadata": {
            "supports_multiple_outputs": True,
            "supports_nested_results": False,
            "requires_dynamic_discovery": False,
            "primary_output_handle": "current_item",
        },
    }

    return resolver_config


def is_loop_node(node: Dict[str, Any]) -> bool:
    """
    Check if a node is a loop node.

    Args:
        node: A node from workflow_capture_schema

    Returns:
        True if the node is a loop node, False otherwise
    """
    try:
        if "data" in node:
            # Check both type and originalType fields
            node_type = node["data"].get("type", "")
            original_type = node["data"].get("originalType", "")

            # Ensure we have strings before using 'in' operator to avoid NoneType errors
            node_type = node_type or ""
            original_type = original_type or ""

            result = "LoopNode" in node_type or "LoopNode" in original_type
            return result
        return False
    except Exception as e:
        print(f"[DEBUG] Error in is_loop_node for node {node.get('id')}: {str(e)}")
        return False


def is_output_node(node: Dict[str, Any]) -> bool:
    """
    Check if a node is an output node.

    Args:
        node: A node from workflow_capture_schema

    Returns:
        True if the node is an output node, False otherwise
    """
    try:
        if "data" in node and "type" in node["data"]:
            node_type = node["data"]["type"]
            # Ensure we have a string before using 'in' operator to avoid NoneType errors
            node_type = node_type or ""
            result = "Output" in node_type or node_type == "OutputNode"
            print(
                f"[DEBUG] is_output_node({node.get('id')}): node_type='{node_type}', result={result}"
            )
            return result
        return False
    except Exception as e:
        print(f"[DEBUG] Error in is_output_node for node {node.get('id')}: {str(e)}")
        print(f"[DEBUG] Node data: {node}")
        if "NoneType" in str(e):
            print(f"[DEBUG] NoneType error in is_output_node")
        raise


def fix_output_node_input_data(
    transition: Dict[str, Any],
    conditional_node_id: str,
    previous_node_id: str,
    previous_node_server_id: str,
) -> None:
    """
    Fix the input_data of an output node transition to use the previous node as the source instead of the conditional node.

    Args:
        transition: The output node transition to fix
        conditional_node_id: The ID of the conditional node
        previous_node_id: The ID of the previous node (that has the conditional routing)
        previous_node_server_id: The server_id from mcp_info of the previous node
    """
    # Update input_data to use the previous node as the source
    for input_data in transition["node_info"]["input_data"]:
        if input_data["from_transition_id"] == f"transition-{conditional_node_id}":
            input_data["from_transition_id"] = f"transition-{previous_node_id}"
            input_data["source_node_id"] = previous_node_server_id


def _process_conditional_edges(node_id: str, edges: List[Dict[str, Any]]) -> Dict[str, str]:
    """
    Process outgoing edges from a conditional node to create output mapping.

    Args:
        node_id: ID of the conditional node
        edges: List of all edges in the workflow

    Returns:
        Dictionary mapping condition keys to target node IDs
    """
    outgoing_edges = [edge for edge in edges if edge["source"] == node_id]
    output_to_target = {}

    print(f"         - Processing {len(outgoing_edges)} outgoing edges from {node_id}")

    for edge in outgoing_edges:
        source_handle = edge.get("sourceHandle", "") or ""
        target_id = edge["target"]

        print(f"           - Edge: {source_handle} -> {target_id}")

        # Handle condition output patterns: condition_1, condition_2, etc.
        if source_handle.startswith("condition_"):
            # Extract condition number from patterns like "condition_1", "condition_2"
            condition_part = source_handle.replace("condition_", "")
            try:
                condition_num = int(condition_part)
                output_to_target[f"condition_{condition_num}"] = target_id
                print(f"             ✅ Mapped condition_{condition_num} -> {target_id}")
            except ValueError:
                print(f"             ❌ Failed to parse condition number from: {source_handle}")
                continue
        # Handle default output pattern
        elif source_handle == "default":
            output_to_target["default"] = target_id
            print(f"             ✅ Mapped default -> {target_id}")
        else:
            print(f"             ⚠️  Unrecognized source handle pattern: {source_handle}")

    print(f"         - Final output mappings: {output_to_target}")
    return output_to_target


def _trace_branch_endpoints(node_id: str, edges: List[Dict[str, Any]], nodes: List[Dict[str, Any]]) -> List[str]:
    """
    Trace a branch from a starting node to find all possible endpoints.
    
    This function handles all types of branching scenarios including:
    - Conditional nodes (switch-case routers)
    - Regular nodes with multiple outputs
    - Nested branching structures
    
    Args:
        node_id: Starting node ID to trace from
        edges: List of all edges in the workflow
        nodes: List of all nodes in the workflow
    
    Returns:
        List of endpoint node IDs for this branch
    """
    def _trace_recursive(current_node: str, visited: set, depth: int = 0) -> List[str]:
        """Recursive helper to trace all possible paths from a node."""
        indent = "  " * depth
        print(f"{indent}         - Tracing from {current_node} (depth {depth})")
        
        # Prevent infinite loops
        if current_node in visited:
            print(f"{indent}           - Cycle detected, stopping at {current_node}")
            return [current_node]
        
        visited.add(current_node)
        
        # Find outgoing edges from current node
        outgoing_edges = [edge for edge in edges if edge["source"] == current_node]
        
        # If no outgoing edges, this is an endpoint
        if not outgoing_edges:
            print(f"{indent}           - Found endpoint: {current_node} (no outgoing edges)")
            return [current_node]
        
        # Check if this is an output node (explicit endpoint)
        current_node_obj = next((node for node in nodes if node["id"] == current_node), None)
        if current_node_obj and is_output_node(current_node_obj):
            print(f"{indent}           - Found endpoint: {current_node} (output node)")
            return [current_node]
        
        # If only one outgoing edge, follow it
        if len(outgoing_edges) == 1:
            next_node = outgoing_edges[0]["target"]
            print(f"{indent}           - Following {current_node} -> {next_node}")
            return _trace_recursive(next_node, visited.copy(), depth + 1)
        else:
            # Multiple outgoing edges - follow all branches
            print(f"{indent}           - Multiple branches from {current_node} ({len(outgoing_edges)} edges)")
            all_endpoints = []
            
            for i, edge in enumerate(outgoing_edges):
                next_node = edge["target"]
                source_handle = edge.get("sourceHandle", "")
                print(f"{indent}           - Branch {i+1}: {current_node} --[{source_handle}]--> {next_node}")
                
                # Recursively trace each branch
                branch_endpoints = _trace_recursive(next_node, visited.copy(), depth + 1)
                all_endpoints.extend(branch_endpoints)
            
            # Remove duplicates while preserving order
            unique_endpoints = []
            seen = set()
            for endpoint in all_endpoints:
                if endpoint not in seen:
                    unique_endpoints.append(endpoint)
                    seen.add(endpoint)
            
            print(f"{indent}           - All endpoints from {current_node}: {unique_endpoints}")
            return unique_endpoints
    
    print(f"         - Tracing all branch endpoints from {node_id}")
    endpoints = _trace_recursive(node_id, set())
    print(f"         - Final endpoints: {endpoints}")
    return endpoints


def _detect_conditional_branch_endpoints(node_id: str, edges: List[Dict[str, Any]], nodes: List[Dict[str, Any]]) -> Dict[str, List[str]]:
    """
    Detect the endpoint nodes for each branch of a conditional node.
    
    Args:
        node_id: ID of the conditional node
        edges: List of all edges in the workflow
        nodes: List of all nodes in the workflow
    
    Returns:
        Dictionary mapping condition keys to their list of endpoint node IDs
    """
    print(f"         - Detecting branch endpoints for conditional node {node_id}")
    
    # Get immediate targets from conditional node
    output_to_target = _process_conditional_edges(node_id, edges)
    branch_endpoints = {}
    
    # Trace each branch to its endpoints
    for condition_key, target_id in output_to_target.items():
        endpoint_ids = _trace_branch_endpoints(target_id, edges, nodes)
        branch_endpoints[condition_key] = endpoint_ids
        print(f"           - Branch {condition_key}: {target_id} -> endpoints: {endpoint_ids}")
    
    print(f"         - Branch endpoints: {branch_endpoints}")
    return branch_endpoints


def should_use_component_based_routing() -> bool:
    """
    Check if component-based routing should be used.

    Component-based routing is now the only supported mode.
    Embedded routing has been removed.

    Returns:
        Always True (component-based routing is the only supported mode)
    """
    # Legacy embedded mode has been removed - always use component mode
    return True


def create_conditional_component_routing(
    node: Dict[str, Any], edges: List[Dict[str, Any]], nodes: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Create a conditional component routing configuration.

    Args:
        node: A conditional node from workflow_capture_schema
        edges: List of edges from workflow_capture_schema
        nodes: List of all nodes in the workflow

    Returns:
        A conditional component configuration for component-based routing
    """
    node_id = node["id"]
    node_data = node.get("data", {})
    node_config = node_data.get("config", {})

    print(f"      🔧 CREATE_CONDITIONAL_COMPONENT_ROUTING: Starting for {node_id}")
    print(f"         - Node config keys: {list(node_config.keys())}")

    # Get number of conditions
    num_conditions = node_config.get("num_conditions", 2)
    num_conditions = max(1, min(10, int(num_conditions)))
    print(f"         - Number of conditions: {num_conditions}")

    # Find outgoing edges and create output mapping
    print(f"         - Processing conditional edges...")
    output_to_target = _process_conditional_edges(node_id, edges)
    print(f"         - Output mappings: {output_to_target}")

    # Detect branch endpoints for each condition
    print(f"         - Detecting branch endpoints...")
    branch_endpoints = _detect_conditional_branch_endpoints(node_id, edges, nodes)
    print(f"         - Branch endpoints detected: {branch_endpoints}")

    # Build conditions array
    print(f"         - Building conditions array...")
    conditions = []
    for condition_num in range(1, num_conditions + 1):
        condition_key = f"condition_{condition_num}"

        if condition_key not in output_to_target:
            print(f"           - Condition {condition_num}: No output target, skipping")
            continue

        target_id = output_to_target[condition_key]
        print(f"           - Condition {condition_num}: Target = {target_id}")

        # Extract condition configuration
        # Use global source setting for all conditions (new dropdown approach)
        global_source = node_config.get("source", "node_output")
        operator = node_config.get(f"condition_{condition_num}_operator", "equals")
        expected_value = node_config.get(f"condition_{condition_num}_expected_value", "")
        # Use global variable_name for all conditions when using global_context
        variable_name = node_config.get("variable_name", "")

        print(f"             - Source: {global_source}")
        print(f"             - Operator: {operator}")
        print(f"             - Expected Value: {expected_value}")
        if variable_name:
            print(f"             - Variable: {variable_name}")

        # Get the endpoints for this condition branch
        endpoint_ids = branch_endpoints.get(condition_key, [target_id])

        # Create condition object
        condition = {
            "operator": operator,
            "next_transition": f"transition-{target_id}",
            "ends_at": [f"transition-{endpoint_id}" for endpoint_id in endpoint_ids],
        }

        # Only add expected_value for operators that need it
        if operator not in ["exists", "is_empty"]:
            condition["expected_value"] = expected_value

        # Add variable_name for global_context source
        if global_source == "global_context" and variable_name:
            condition["variable_name"] = variable_name

        conditions.append(condition)
        print(f"           ✅ Condition {condition_num} added (ends at: {[f'transition-{eid}' for eid in endpoint_ids]})")

    # Create component configuration with required tool_id
    print(f"         - Creating component configuration...")
    default_target = output_to_target.get("default", "unknown")
    default_endpoints = branch_endpoints.get("default", [default_target])
    print(f"         - Default transition: transition-{default_target}")
    print(f"         - Default ends at: {[f'transition-{eid}' for eid in default_endpoints]}")

    # Get evaluation strategy from node configuration
    evaluation_strategy = node_config.get("evaluation_strategy", "all_matches")
    print(f"         - Evaluation strategy: {evaluation_strategy}")

    # Get input_data and source from node configuration
    input_data = node_config.get("input_data", "")
    source = node_config.get("source", "node_output")
    print(f"         - Input data: {input_data}")
    print(f"         - Source: {source}")

    component_config = {
        "tool_id": 1,  # Required field for schema validation
        "tool_name": "conditional",
        "server_id": "node-executor-service",
        "tool_params": {
            "items": [
                {"field_name": "conditions", "data_type": "array", "field_value": conditions},
                {
                    "field_name": "input_data",
                    "data_type": "string",
                    "field_value": input_data,
                },
                {
                    "field_name": "source",
                    "data_type": "string",
                    "field_value": source,
                },
                {
                    "field_name": "default_transition",
                    "data_type": "string",
                    "field_value": f"transition-{default_target}",
                },
                {
                    "field_name": "default_ends_at",
                    "data_type": "array",
                    "field_value": [f"transition-{endpoint_id}" for endpoint_id in default_endpoints],
                },
                {
                    "field_name": "evaluation_strategy",
                    "data_type": "string",
                    "field_value": evaluation_strategy,
                },
            ]
        },
    }

    print(f"      ✅ CREATE_CONDITIONAL_COMPONENT_ROUTING: Completed")
    print(f"         - Total conditions: {len(conditions)}")
    print(f"         - Tool ID: {component_config['tool_id']}")
    print(f"         - Server ID: {component_config['server_id']}")

    return component_config


def convert_embedded_to_component_format(transition: Dict[str, Any]) -> Dict[str, Any]:
    """
    DEPRECATED: Embedded conditional routing is no longer supported.

    This function is deprecated as embedded routing has been removed.
    All conditional routing should use conditional components.

    Args:
        transition: Transition configuration

    Returns:
        Transition unchanged (embedded routing no longer supported)
    """
    if "conditional_routing" in transition:
        logger.warning(
            "Embedded conditional routing detected but is no longer supported. "
            "Please use conditional components instead."
        )

    return transition


def create_conditional_node_transition(
    node: Dict[str, Any],
    edges: List[Dict[str, Any]],
    sequence: int,
    nodes_connected_to_start: List[str],
    nodes: List[Dict[str, Any]],
) -> Dict[str, Any]:
    """
    Create a dedicated transition for a conditional node in component mode.

    Args:
        node: The conditional node
        edges: List of all edges in the workflow
        sequence: Sequence number for the transition
        nodes_connected_to_start: List of nodes connected to start node
        nodes: List of all nodes in the workflow

    Returns:
        A complete transition for the conditional node
    """
    node_id = node["id"]

    print(f"🔧 CREATE_CONDITIONAL_NODE_TRANSITION: Starting for {node_id}")

    # Create the conditional component
    print(f"   📋 Creating conditional component routing...")
    try:
        conditional_component = create_conditional_component_routing(node, edges, nodes)
        print(f"   ✅ Conditional component created successfully")
        print(f"      - Tool ID: {conditional_component.get('tool_id')}")
        print(f"      - Tool Name: {conditional_component.get('tool_name')}")
        print(f"      - Server ID: {conditional_component.get('server_id')}")

        # Extract conditions from tool_params format
        conditions = []
        tool_params = conditional_component.get("tool_params", {})
        items = tool_params.get("items", [])
        for item in items:
            if item.get("field_name") == "conditions" and item.get("field_value"):
                conditions = item.get("field_value", [])
                break
        print(f"      - Conditions: {len(conditions)}")

    except Exception as e:
        print(f"   ❌ ERROR creating conditional component: {str(e)}")
        raise

    # Determine transition type
    transition_type = "standard"
    if node_id in nodes_connected_to_start:
        transition_type = "initial"

    print(f"   🏗️  Building transition structure...")
    print(f"      - Transition Type: {transition_type}")
    print(f"      - Sequence: {sequence}")
    print(f"      - Connected to start: {node_id in nodes_connected_to_start}")

    # Create result resolution metadata using the universal resolver (required by schema)
    print(f"   🔧 Creating universal result resolver...")
    try:
        # Use the same approach as create_transition_from_edge
        node_data = node["data"]
        result_resolution = create_universal_result_resolver(node_data)

        # Override node_type to "conditional" for proper identification in orchestration engine
        result_resolution["node_type"] = "conditional"

        print(f"   ✅ Universal result resolver created successfully")
        print(f"      - Node type: {result_resolution.get('node_type')}")
        print(f"      - Expected structure: {result_resolution.get('expected_result_structure')}")
        print(
            f"      - Handle registry: {len(result_resolution.get('handle_registry', {}).get('input_handles', []))} input, {len(result_resolution.get('handle_registry', {}).get('output_handles', []))} output"
        )
        print(
            f"      - Dynamic discovery: {result_resolution.get('dynamic_discovery', {}).get('enabled', False)}"
        )
    except Exception as e:
        print(f"   ❌ ERROR creating universal result resolver: {str(e)}")
        # Fallback to a minimal but schema-compliant result_resolution
        print(f"   🔄 Using fallback result resolution...")
        result_resolution = {
            "node_type": "conditional",  # Ensure conditional type for proper handling
            "expected_result_structure": "direct",
            "handle_registry": {
                "input_handles": [
                    {
                        "handle_id": "primary_input_data",
                        "handle_name": "Primary Input",
                        "data_type": "object",
                        "required": True,
                    }
                ],
                "output_handles": [
                    {
                        "handle_id": "routing_decision",
                        "handle_name": "Routing Decision",
                        "data_type": "object",
                    }
                ],
            },
            "dynamic_discovery": {"enabled": False},
        }

    # Create the transition structure with all required fields
    transition = {
        "id": f"transition-{node_id}",
        "sequence": sequence,
        "transition_type": transition_type,  # Required field for schema validation
        "execution_type": "Components",
        "node_label": node["data"].get("label", ""),  # Add node_label from pre-converted schema
        "end": False,  # Required field for schema validation
        "node_info": {
            "node_id": node_id,  # Required field for schema validation
            "tools_to_use": [conditional_component],
            "input_data": [],
            "output_data": [],
        },
        "result_resolution": result_resolution,  # Required field for schema validation
        "approval_required": False,  # Optional but commonly included
    }

    print(f"   ✅ Transition structure created with all required fields")

    # Find incoming edges to set up input_data
    print(f"   🔗 Processing input connections...")
    incoming_edges = [edge for edge in edges if edge["target"] == node_id]
    print(f"      - Found {len(incoming_edges)} incoming edges")

    for i, edge in enumerate(incoming_edges):
        source_id = edge["source"]
        target_handle = edge.get("targetHandle")

        # Skip tool-related edges - tools should be callable functions, not input sources
        if target_handle == "tools":
            print(
                f"      - Input {i+1}: from {source_id} -> SKIPPED (tool connection, not input source)"
            )
            continue

        print(f"      - Input {i+1}: from {source_id} -> PROCESSING (legitimate workflow input)")

        input_entry = {
            "from_transition_id": f"transition-{source_id}",
            "source_node_id": source_id,
            "data_type": "string",
            "handle_mappings": [],  # Required field for schema validation
            "input_handle_registry": {"handle_mappings": []},
        }
        transition["node_info"]["input_data"].append(input_entry)

    print(f"   ✅ Input data configured: {len(transition['node_info']['input_data'])} entries")

    # Note: output_data is intentionally minimal for conditional nodes
    # The conditional component handles dynamic routing through its parameters
    print(f"   ℹ️  Output data handled by conditional component parameters")

    print(f"🎯 CREATE_CONDITIONAL_NODE_TRANSITION: Completed for {node_id}")
    return transition


def create_conditional_routing(node: Dict[str, Any], edges: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    DEPRECATED: Embedded conditional routing is no longer supported.

    This function is deprecated as embedded routing has been removed.
    All conditional routing should use conditional components.

    Args:
        node: A conditional node from workflow_capture_schema
        edges: List of edges from workflow_capture_schema

    Returns:
        Empty dict (embedded routing no longer supported)
    """
    logger.warning(
        "create_conditional_routing called but embedded routing is no longer supported. "
        "Please use conditional components instead."
    )
    return {}


def _configure_agentic_tool_data_flow(
    nodes: List[Dict[str, Any]],
    virtual_tool_nodes: List[Dict[str, Any]],
    virtual_tool_edges: List[Dict[str, Any]],
) -> None:
    """
    Configure tool data flow for AgenticAI nodes to ensure tools are available in execution context.

    This function ensures that tool connection data from AgenticAI config flows properly to the
    execution context where the AgenticAI component expects to find it.

    Args:
        nodes: List of all workflow nodes (including virtual tool nodes)
        virtual_tool_nodes: List of virtual tool nodes created from AgenticAI configs
        virtual_tool_edges: List of virtual edges connecting tools to AgenticAI nodes
    """
    print(
        f"      🔧 Configuring tool data flow for {len(virtual_tool_nodes)} virtual tool nodes..."
    )

    # Group virtual tool edges by target AgenticAI node
    agentic_tool_map = {}
    for edge in virtual_tool_edges:
        target_id = edge.get("target")
        if target_id:
            if target_id not in agentic_tool_map:
                agentic_tool_map[target_id] = []
            agentic_tool_map[target_id].append(edge)

    # Update AgenticAI nodes to include tool data in a format expected by execution
    for node in nodes:
        node_id = node.get("id")
        if node_id in agentic_tool_map and (
            node.get("data", {}).get("definition", {}).get("name") == "AgenticAI"
            or node.get("data", {}).get("type") in ["agent", "employee"]
        ):
            # Prepare tool data for execution context
            tool_edges = agentic_tool_map[node_id]
            tool_data_for_execution = []

            for edge in tool_edges:
                source_id = edge.get("source")
                # Find the corresponding virtual tool node
                virtual_tool = next(
                    (vt for vt in virtual_tool_nodes if vt.get("id") == source_id), None
                )
                if virtual_tool:
                    tool_data_for_execution.append(
                        {
                            "component_id": source_id,
                            "component_type": virtual_tool.get("data", {}).get("originalType", ""),
                            "component_name": virtual_tool.get("data", {}).get("label", ""),
                            "component_definition": virtual_tool.get("data", {}).get(
                                "definition", {}
                            ),
                            "node_id": source_id,
                            "node_type": virtual_tool.get("data", {}).get("originalType", ""),
                            "node_label": virtual_tool.get("data", {}).get("label", ""),
                        }
                    )

            # Store tool data in node config for execution context access
            if "data" not in node:
                node["data"] = {}
            if "config" not in node["data"]:
                node["data"]["config"] = {}

            # Add execution-ready tool data
            node["data"]["config"]["_execution_tool_data"] = tool_data_for_execution
            print(
                f"         - Configured {len(tool_data_for_execution)} tools for AgenticAI node {node_id}"
            )


def extract_agentic_tool_connections(
    nodes: List[Dict[str, Any]]
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Extract tool connections from AgenticAI node configs and create virtual tool nodes and edges.

    Uses only the new simplified format (config.tools).
    This function addresses the issue where tools are stored in AgenticAI config but don't exist
    as separate nodes in the workflow, making them invisible during schema conversion.

    Args:
        nodes: List of workflow nodes

    Returns:
        Tuple of (virtual_tool_nodes, virtual_tool_edges)
    """
    virtual_tool_nodes = []
    virtual_tool_edges = []

    for node in nodes:
        # Check if this is an AgenticAI node with tool connections
        if (
            node.get("data", {}).get("definition", {}).get("name") == "AgenticAI"
            or node.get("data", {}).get("type") in ["agent", "employee"]
        ):
            node_id = node.get("id")
            node_config = node.get("data", {}).get("config", {})

            print(f"      🔍 Checking AgenticAI node: {node_id}")
            print(f"         - Config keys: {list(node_config.keys())}")

            # Process tool connections - simplified format only
            all_tool_connections = []

            # Check for config.tools (array)
            simplified_tools = node_config.get("tools")
            if simplified_tools and isinstance(simplified_tools, list):
                all_tool_connections.extend(simplified_tools)
                print(f"         - Found {len(simplified_tools)} tools in config.tools format")
            else:
                print(f"         - No tools found in config.tools")

            print(f"         - Total tool connections to process: {len(all_tool_connections)}")

            for i, tool_connection in enumerate(all_tool_connections):
                if not tool_connection:
                    continue

                # Extract tool information
                component_id = tool_connection.get("component_id", f"virtual_tool_{node_id}_{i}")
                component_type = tool_connection.get("component_type", "component")
                component_name = tool_connection.get("component_name", f"Tool_{i+1}")
                component_config = tool_connection.get("component_config", {})
                component_schema = tool_connection.get("component_schema", {})
                mcp_metadata = tool_connection.get("mcp_metadata", {})

                print(f"         - Tool {i+1}: {component_name} ({component_type})")
                print(f"           ID: {component_id}")

                # Create virtual tool node
                virtual_tool_node = {
                    "id": component_id,
                    "type": "CustomNode",  # Standard node type
                    "data": {
                        "type": component_type,
                        "definition": {
                            "name": component_name,
                            "display_name": component_name,
                            "description": f"Tool connected to {node_id}",
                            "inputs": [],
                            "outputs": [],
                        },
                        "config": component_config,
                    },
                    "position": {"x": 0, "y": 0},  # Virtual position
                    "_virtual_tool": True,  # Mark as virtual for identification
                    "_parent_agentic_node": node_id,  # Reference to parent AgenticAI node
                }

                # Add component schema if available
                if component_schema:
                    virtual_tool_node["data"]["definition"]["component_schema"] = component_schema

                # Add MCP metadata if available
                if mcp_metadata:
                    virtual_tool_node["data"]["definition"]["mcp_metadata"] = mcp_metadata
                    # If this is an MCP tool, set the type accordingly
                    if mcp_metadata.get("server_id"):
                        virtual_tool_node["data"]["type"] = "mcp"
                        virtual_tool_node["data"]["definition"]["mcp_info"] = {
                            "server_id": mcp_metadata.get("server_id"),
                            "tool_name": mcp_metadata.get("tool_name", component_name),
                            "tool_id": mcp_metadata.get("tool_id", 1),
                        }

                # Extract inputs and outputs from component schema if available
                if component_schema:
                    # Extract inputs from schema
                    schema_inputs = component_schema.get("inputs", [])
                    for schema_input in schema_inputs:
                        virtual_tool_node["data"]["definition"]["inputs"].append(
                            {
                                "name": schema_input.get("name", "input"),
                                "display_name": schema_input.get("display_name", "Input"),
                                "input_type": schema_input.get("input_type", "string"),
                                "required": schema_input.get("required", False),
                                "info": schema_input.get("info", ""),
                            }
                        )

                    # Extract outputs from schema
                    schema_outputs = component_schema.get("outputs", [])
                    for schema_output in schema_outputs:
                        virtual_tool_node["data"]["definition"]["outputs"].append(
                            {
                                "name": schema_output.get("name", "output"),
                                "display_name": schema_output.get("display_name", "Output"),
                                "output_type": schema_output.get("output_type", "string"),
                                "info": schema_output.get("info", ""),
                            }
                        )

                # If no schema available, create default inputs/outputs
                if not virtual_tool_node["data"]["definition"]["inputs"]:
                    virtual_tool_node["data"]["definition"]["inputs"] = [
                        {
                            "name": "input",
                            "display_name": "Input",
                            "input_type": "string",
                            "required": False,
                            "info": "Tool input",
                        }
                    ]

                if not virtual_tool_node["data"]["definition"]["outputs"]:
                    virtual_tool_node["data"]["definition"]["outputs"] = [
                        {
                            "name": "result",
                            "display_name": "Result",
                            "output_type": "string",
                            "info": "Tool result",
                        }
                    ]

                virtual_tool_nodes.append(virtual_tool_node)

                # Create virtual edge from tool to AgenticAI node
                # Always use "tools" handle for simplified approach (single handle)
                target_handle = "tools"

                virtual_edge = {
                    "id": f"virtual_edge_{component_id}_to_{node_id}",
                    "source": component_id,
                    "target": node_id,
                    "sourceHandle": "result",  # Default output handle
                    "targetHandle": target_handle,  # Use appropriate AgenticAI tools handle
                    "_virtual_edge": True,  # Mark as virtual
                    "_tool_connection": True,  # Mark as tool connection
                }

                virtual_tool_edges.append(virtual_edge)

                print(f"           ✅ Created virtual tool node and edge")

    return virtual_tool_nodes, virtual_tool_edges


def is_tool_node(node: Dict[str, Any], edges: List[Dict[str, Any]]) -> bool:
    """
    Check if a node is connected as a tool to an AgenticAI node.

    Args:
        node: The node to check
        edges: List of all edges in the workflow

    Returns:
        True if the node is connected to an AgenticAI node's tools handle
    """
    node_id = node.get("id")

    # Check if this node connects to any AgenticAI node's tools handle
    for edge in edges:
        if edge.get("source") == node_id and edge.get("targetHandle") == "tools":
            return True
    return False


def integrate_tools_into_agent_config(
    agent_node: Dict[str, Any], tool_nodes: List[Dict[str, Any]]
) -> None:
    """
    Integrate tool schemas into agent configuration.

    Args:
        agent_node: The AgenticAI node to integrate tools into
        tool_nodes: List of tool nodes connected to this agent
    """
    agent_tools = []

    for tool_node in tool_nodes:
        tool_definition = tool_node["data"]["definition"]
        tool_name = tool_definition.get("name", "unknown_tool")

        if "mcp_info" in tool_definition:
            # MCP tool - flattened metadata structure
            mcp_info = tool_definition["mcp_info"]
            agent_tools.append(
                {
                    "mcp_id": mcp_info.get("server_id", ""),
                    "tool_name": mcp_info.get("tool_name", tool_name),
                }
            )
        else:
            # Regular workflow component tool
            input_schema = {"type": "object", "properties": {}, "required": []}

            for input_def in tool_definition.get("inputs", []):
                field_name = input_def.get("name", "")
                input_schema["properties"][field_name] = {
                    "type": input_def.get("input_type", "string"),
                    "description": input_def.get("info", ""),
                }
                if input_def.get("required", False):
                    input_schema["required"].append(field_name)

            agent_tools.append(
                {
                    "tool_type": "workflow_component",
                    "component": {
                        "component_id": tool_node.get("id", "unknown"),
                        "component_type": tool_name,
                        "component_name": tool_definition.get("display_name", tool_name),
                        "component_description": tool_definition.get("description", ""),
                        "input_schema": input_schema,
                        "output_schema": {
                            "type": "object",
                            "properties": {
                                output_def["name"]: {
                                    "type": output_def.get("output_type", "string"),
                                    "description": output_def.get("info", ""),
                                }
                                for output_def in tool_definition.get("outputs", [])
                            },
                        },
                    },
                }
            )

    # Add tools to agent configuration - this will be used in the agent_config field_value
    # The conversion process looks for source_config.get("agent_config", {}) on line 1373
    # So we need to put the tools in config.agent_config.agent_tools
    node_config = agent_node["data"].get("config", {})
    if "agent_config" not in node_config:
        node_config["agent_config"] = {}
    node_config["agent_config"]["agent_tools"] = agent_tools

    print(f"      🔧 Integrated {len(agent_tools)} tools into agent {agent_node.get('id')}")
    for i, tool in enumerate(agent_tools, 1):
        component = tool.get("component", {})
        print(
            f"         {i}. {component.get('component_name', 'Unknown')} ({component.get('component_type', 'Unknown')})"
        )


def preprocess_field_value(field_value: Any) -> Any:
    """
    Preprocess field values to handle double-serialized JSON objects.

    This function detects when JSON objects have been double-serialized (converted to strings)
    and parses them back to their original object form. This commonly happens when frontend
    applications JSON.stringify the entire payload, causing nested objects to become strings.

    Args:
        field_value: The field value to preprocess (can be any type)

    Returns:
        The preprocessed field value with JSON strings parsed back to objects/arrays
    """
    if isinstance(field_value, str):
        try:
            # Try to parse as JSON - if successful and result is dict/list, use parsed value
            parsed = json.loads(field_value)
            if isinstance(parsed, (dict, list)):
                logger.debug(f"Detected and parsed double-serialized JSON: {field_value[:100]}...")
                return parsed
        except (json.JSONDecodeError, ValueError):
            # Not a JSON string, return original value
            pass
    return field_value


def convert_workflow_to_transition_schema(workflow_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert workflow data from workflow_capture_schema format to transition_schema format.

    Args:
        workflow_data: The workflow data in workflow_capture_schema format

    Returns:
        The workflow data in transition_schema format

    Raises:
        HandleMappingConflictError: If multiple sources connect to the same target handle
    """
    print("\n" + "=" * 80)
    print("🚀 STARTING WORKFLOW CONVERSION TO TRANSITION SCHEMA")
    print("=" * 80)

    # Extract components
    nodes = workflow_data.get("nodes", [])
    edges = workflow_data.get("edges", [])
    mcp_configs = workflow_data.get("mcp_configs", [])

    print(f"📊 WORKFLOW COMPONENTS EXTRACTED:")
    print(f"   - Nodes: {len(nodes)}")
    print(f"   - Edges: {len(edges)}")
    print(f"   - MCP Configs: {len(mcp_configs)}")

    # Check if tool nodes are already saved as separate visual nodes
    print(f"\n🔧 CHECKING FOR TOOL NODES IN WORKFLOW...")

    # Find all AgenticAI nodes with tool configurations
    agentic_nodes_with_tools = []
    for node in nodes:
        if (
            node.get("data", {}).get("definition", {}).get("name") == "AgenticAI"
            or node.get("data", {}).get("type") in ["agent", "employee"]
        ):
            node_config = node.get("data", {}).get("config", {})
            simplified_tools = node_config.get("tools")
            if simplified_tools and isinstance(simplified_tools, list):
                agentic_nodes_with_tools.append((node, simplified_tools))

    # Check if tool nodes already exist as separate visual nodes
    tool_nodes_exist_as_visual = False
    if agentic_nodes_with_tools:
        for agentic_node, tools in agentic_nodes_with_tools:
            for tool in tools:
                tool_node_id = tool.get("node_id")
                if tool_node_id:
                    # Check if this tool exists as a separate visual node
                    tool_node_exists = any(n.get("id") == tool_node_id for n in nodes)
                    if tool_node_exists:
                        tool_nodes_exist_as_visual = True
                        print(f"   ✅ Found tool node {tool_node_id} as separate visual node")
                        break
            if tool_nodes_exist_as_visual:
                break

    if tool_nodes_exist_as_visual:
        print(f"   ✅ Tool nodes are already saved as separate visual components")
        print(f"   ✅ Skipping virtual node creation to avoid duplicates")
        print(f"   ✅ Using existing tool nodes and edges")

        # Configure tool data flow for existing nodes
        print(f"   🔧 CONFIGURING TOOL DATA FLOW FOR EXISTING NODES...")
        for agentic_node, tools in agentic_nodes_with_tools:
            agentic_node["data"]["config"]["_execution_tool_data"] = tools
            print(
                f"         - Configured {len(tools)} tools for AgenticAI node {agentic_node['id']}"
            )
        print(f"   ✅ Tool data flow configured for existing tool nodes")
    else:
        # Legacy fallback: Extract tool connections from AgenticAI configs and create virtual tool nodes
        print(f"   ℹ️  No separate tool nodes found, creating virtual nodes from config...")
        virtual_tool_nodes, virtual_tool_edges = extract_agentic_tool_connections(nodes)

        if virtual_tool_nodes:
            print(f"   ✅ Found {len(virtual_tool_nodes)} tool connections in AgenticAI configs")
            print(f"   ✅ Created {len(virtual_tool_edges)} virtual edges for tool connections")

            # Add virtual tool nodes to the nodes list
            nodes.extend(virtual_tool_nodes)
            edges.extend(virtual_tool_edges)

            print(f"   📊 Updated totals:")
            print(
                f"      - Nodes: {len(nodes)} (added {len(virtual_tool_nodes)} virtual tool nodes)"
            )
            print(
                f"      - Edges: {len(edges)} (added {len(virtual_tool_edges)} virtual tool edges)"
            )

            # CRITICAL FIX: Ensure tool data flows to AgenticAI execution context
            print(f"   🔧 CONFIGURING TOOL DATA FLOW FOR EXECUTION...")
            _configure_agentic_tool_data_flow(nodes, virtual_tool_nodes, virtual_tool_edges)
            print(f"   ✅ Tool data flow configured for AgenticAI execution")
        else:
            print(f"   ℹ️  No tool connections found in AgenticAI configs")

    # Log node types for debugging
    node_types = {}
    conditional_nodes = []
    for node in nodes:
        node_id = node.get("id", "unknown")
        original_type = node.get("data", {}).get("originalType", "unknown")
        node_type = node.get("data", {}).get("type", "unknown")

        if original_type not in node_types:
            node_types[original_type] = 0
        node_types[original_type] += 1

        if is_conditional_node(node):
            conditional_nodes.append(node_id)

    print(f"📋 NODE TYPE BREAKDOWN:")
    for node_type, count in node_types.items():
        print(f"   - {node_type}: {count}")

    if conditional_nodes:
        print(f"🔀 CONDITIONAL NODES DETECTED: {conditional_nodes}")
        print(f"⚙️  CONDITIONAL ROUTING MODE: component (embedded mode removed)")
    else:
        print("ℹ️  NO CONDITIONAL NODES DETECTED")

    # Validate one-to-one handle mapping constraint BEFORE processing
    print("\n🔍 VALIDATING HANDLE MAPPINGS...")
    try:
        _validate_one_to_one_handle_mapping(edges)
        print("✅ Handle mapping validation successful")
    except Exception as e:
        print(f"❌ Handle mapping validation failed: {str(e)}")
        raise

    # Identify the start node
    print("\n🎯 IDENTIFYING START NODE...")
    start_node_id = None
    for i, node in enumerate(nodes):
        node_id = node.get("id", f"node-{i}")
        original_type = node.get("data", {}).get("originalType", "unknown")
        print(f"   Checking node {i}: {node_id} (type: {original_type})")
        try:
            if (
                "data" in node
                and "definition" in node["data"]
                and "originalType" in node["data"]
                and node["data"]["originalType"] == "StartNode"
            ):
                start_node_id = node["id"]
                print(f"✅ Found start node: {start_node_id}")
                break
        except Exception as e:
            print(f"❌ Error checking node {i} for start node: {str(e)}")
            raise

    if not start_node_id:
        print("⚠️  No start node found in workflow")

    # Find nodes connected to the start node - these will be marked as initial
    nodes_connected_to_start = []
    if start_node_id:
        print(f"\n🔗 FINDING NODES CONNECTED TO START NODE...")
        for edge in edges:
            if edge["source"] == start_node_id:
                target_id = edge["target"]
                nodes_connected_to_start.append(target_id)
                print(f"   - {target_id}")
        print(f"✅ Found {len(nodes_connected_to_start)} nodes connected to start")
    else:
        print("⚠️  No start node found, cannot determine initial nodes")

    # Build graph for analysis
    print(f"\n🏗️  BUILDING WORKFLOW GRAPH...")
    graph, edge_map, all_nodes = build_graph_from_workflow(workflow_data)
    print(f"   - Graph nodes: {len(graph)}")
    print(f"   - Edge mappings: {len(edge_map)}")
    print(f"   - All nodes: {len(all_nodes)}")

    # Remove the start node from the graph and all_nodes if it exists
    if start_node_id:
        if start_node_id in graph:
            del graph[start_node_id]
            print(f"   - Removed start node from graph: {start_node_id}")
        all_nodes.discard(start_node_id)

    # Compute node levels
    print(f"\n📊 COMPUTING NODE LEVELS...")
    levels = compute_levels(graph, all_nodes, nodes_connected_to_start)
    print(f"   - Node levels computed for {len(levels)} nodes")
    for level, node_ids in levels.items():
        if isinstance(node_ids, list):
            print(f"   - Level {level}: {node_ids}")

    # Group nodes by level
    level_groups = group_nodes_by_level(levels)
    print(f"   - Grouped into {len(level_groups)} levels")

    # Identify tool nodes that should NOT become separate transitions
    print(f"\n🔍 IDENTIFYING TOOL NODES...")
    tool_nodes = []
    agent_tool_mapping = {}  # Map agent_id -> [tool_nodes]
    skipped_nodes = {"start": [], "tool": [], "processed": []}

    for node in nodes:
        if is_tool_node(node, edges):
            tool_nodes.append(node)
            node_id = node.get("id")
            # Find which agent this tool connects to
            for edge in edges:
                if edge.get("source") == node_id and edge.get("targetHandle") == "tools":
                    target_agent_id = edge.get("target")
                    if target_agent_id not in agent_tool_mapping:
                        agent_tool_mapping[target_agent_id] = []
                    agent_tool_mapping[target_agent_id].append(node)
                    break

    print(f"      🔍 Found {len(tool_nodes)} tool nodes that will be integrated into agents")
    for agent_id, connected_tools in agent_tool_mapping.items():
        print(f"         - Agent {agent_id}: {len(connected_tools)} tools")

    # Integrate tools into their respective agents
    print(f"   🔧 INTEGRATING TOOLS INTO AGENT CONFIGURATIONS...")
    for agent_id, connected_tools in agent_tool_mapping.items():
        # Find the agent node
        agent_node = next((n for n in nodes if n.get("id") == agent_id), None)
        if agent_node:
            integrate_tools_into_agent_config(agent_node, connected_tools)

    # Convert nodes to transition schema format
    print(f"\n🔄 PHASE 1: CONVERTING NODES TO TRANSITION SCHEMA FORMAT")
    print("=" * 60)
    transition_nodes = []

    for i, node in enumerate(nodes):
        node_id = node.get("id", f"node-{i}")
        original_type = node.get("data", {}).get("originalType", "unknown")
        node_type = node.get("data", {}).get("type", "unknown")

        print(f"\n📦 Processing node {i+1}/{len(nodes)}: {node_id}")
        print(f"   Type: {original_type} ({node_type})")

        try:
            # Skip the start node
            if (
                "data" in node
                and "definition" in node["data"]
                and "originalType" in node["data"]
                and node["data"]["originalType"] == "StartNode"
            ):
                print(f"   ⏭️  SKIPPED: Start node (will not appear in final schema)")
                skipped_nodes["start"].append(node_id)
                continue

            # Skip tool nodes - they are integrated into agent configs, not separate transitions
            if is_tool_node(node, edges):
                print(f"   ⏭️  SKIPPED: Tool node (integrated into agent configuration)")
                skipped_nodes["tool"].append(node_id)
                continue

            print(f"   ✅ PROCESSING: Component node")
        except Exception as e:
            print(f"   ❌ ERROR processing node {i} ({node_id}): {str(e)}")
            if "NoneType" in str(e):
                print(f"   📋 Node data: {node}")
            raise

        try:
            transition_node = convert_node_to_transition_node(node, mcp_configs)

            # Fix tool_name for MCP nodes in the nodes array
            if "data" in node and "type" in node["data"] and node["data"]["type"] == "mcp":
                if (
                    "definition" in node["data"]
                    and "mcp_info" in node["data"]["definition"]
                    and "tool_name" in node["data"]["definition"]["mcp_info"]
                ):
                    # Get the tool_name from mcp_info
                    mcp_tool_name = node["data"]["definition"]["mcp_info"]["tool_name"]
                    # Set the tool_name in the server_tools array
                    if (
                        "server_tools" in transition_node
                        and len(transition_node["server_tools"]) > 0
                    ):
                        transition_node["server_tools"][0]["tool_name"] = mcp_tool_name
                        print(f"   🔧 Fixed MCP tool_name: {mcp_tool_name}")

            transition_nodes.append(transition_node)
            skipped_nodes["processed"].append(node_id)
            print(f"   ✅ CONVERTED: Added to transition_nodes array")

        except Exception as e:
            print(f"   ❌ ERROR converting node {node_id}: {str(e)}")
            raise

    # Combine nodes with the same ID
    print(f"\n🔗 COMBINING DUPLICATE NODES...")
    original_count = len(transition_nodes)
    transition_nodes = combine_nodes(transition_nodes)
    final_count = len(transition_nodes)
    if original_count != final_count:
        print(
            f"   Combined {original_count} → {final_count} nodes (removed {original_count - final_count} duplicates)"
        )
    else:
        print(f"   No duplicate nodes found ({final_count} nodes)")

    # Summary of Phase 1
    print(f"\n📊 PHASE 1 SUMMARY:")
    print(f"   - Start nodes skipped: {len(skipped_nodes['start'])} {skipped_nodes['start']}")
    print(
        f"   - Tool nodes skipped (integrated into agents): {len(skipped_nodes['tool'])} {skipped_nodes['tool']}"
    )
    print(
        f"   - Component nodes processed: {len(skipped_nodes['processed'])} {skipped_nodes['processed']}"
    )
    print(f"   - Final transition_nodes count: {final_count}")
    print(f"   - Agent tool integrations: {len(agent_tool_mapping)} agents with tools")

    # Create transitions from edges
    print(f"\n🔄 PHASE 2: CREATING TRANSITIONS FROM WORKFLOW LOGIC")
    print("=" * 60)
    transitions = []
    sequence = 1
    is_first_transition = True
    processed_nodes = set()  # Track nodes we've already processed

    # If we found a start node, add it to processed_nodes to skip it
    if start_node_id:
        processed_nodes.add(start_node_id)
        print(f"🎯 Start node marked as processed: {start_node_id}")

    # Process nodes by level
    conditional_transitions_created = []
    regular_transitions_created = []

    for level in sorted(level_groups.keys()):
        nodes_at_level = level_groups[level]
        print(f"\n🏗️  PROCESSING LEVEL {level}")
        print(f"   Nodes at this level: {nodes_at_level}")

        for node_id in nodes_at_level:
            print(f"\n   📦 Processing node: {node_id}")

            # Skip if we've already processed this node
            if node_id in processed_nodes:
                print(f"      ⏭️  Already processed, skipping")
                continue

            # Find the node
            node = next((n for n in nodes if n["id"] == node_id), None)
            if not node:
                print(f"      ❌ Node data not found in nodes list")
                continue

            # Get node details for logging
            original_type = node.get("data", {}).get("originalType", "unknown")
            node_type = node.get("data", {}).get("type", "unknown")
            print(f"      Type: {original_type} ({node_type})")

            # Skip tool nodes in transition creation - they are integrated into agent configs
            if is_tool_node(node, edges):
                print(f"      ⏭️  SKIPPED: Tool node (integrated into agent configuration)")
                processed_nodes.add(node_id)
                continue

            # Check if this is a conditional node
            is_conditional = is_conditional_node(node)
            print(f"      Is conditional: {is_conditional}")

            # Mark this node as processed
            processed_nodes.add(node_id)

            # Check for reflection (if any neighbor is at a lower level)
            has_reflection = any(
                levels.get(neighbor, 0) < levels.get(node_id, 0)
                for neighbor in graph.get(node_id, [])
            )

            # Check if this is an output node
            is_output = is_output_node(node)

            # Check if this is an end node (no outgoing edges)
            # But never mark transitions inside loop bodies as terminal
            has_no_outgoing = len(graph.get(node_id, [])) == 0
            is_in_loop_body = is_node_in_loop_body(node_id, edges, nodes)

            # Special handling for loop nodes: they are terminal if they have no exit transitions
            # (i.e., no final_results/aggregated_results/loop_results edges to other nodes)
            is_terminal_loop = False
            if is_loop_node(node):
                # Check if loop has any final result edges (exit transitions)
                final_result_edges = [
                    edge
                    for edge in edges
                    if (
                        edge.get("source") == node_id
                        and edge.get("sourceHandle")
                        in ["final_results", "aggregated_results", "loop_results"]
                    )
                ]
                is_terminal_loop = len(final_result_edges) == 0

            is_end = ((has_no_outgoing or is_output) and not is_in_loop_body) or is_terminal_loop

            # Check if this node is connected to the start node
            is_connected_to_start = node_id in nodes_connected_to_start

            # If this is an output node, create a transition for it
            if is_output:
                # Create a dummy edge for the output node
                dummy_edge = {
                    "id": f"dummy-edge-{node_id}",
                    "source": node_id,
                    "target": node_id,  # Self-reference for output nodes
                }
                transition = create_transition_from_edge(
                    dummy_edge,
                    nodes,
                    mcp_configs,
                    edges,
                    sequence,
                    is_first=is_first_transition
                    or is_connected_to_start,  # Mark as initial if connected to start
                    has_reflection=has_reflection,
                    is_end=True,
                )

                # Check if this output node is connected to a conditional node
                # If so, fix the input_data to use the previous node as the source
                for edge in edges:
                    if edge["target"] == node_id and is_conditional_node(
                        next((n for n in nodes if n["id"] == edge["source"]), {})
                    ):
                        conditional_node_id = edge["source"]

                        # Find the previous node that connects to this conditional node
                        for n_id in all_nodes:
                            if conditional_node_id in graph.get(n_id, []):
                                previous_node_id = n_id
                                previous_node = next((n for n in nodes if n["id"] == n_id), None)

                                if (
                                    previous_node
                                    and "data" in previous_node
                                    and "definition" in previous_node["data"]
                                ):
                                    previous_node_def = previous_node["data"]["definition"]
                                    previous_node_type = previous_node["data"]["type"]
                                    previous_server_id = previous_node_id

                                    if previous_node_type == "mcp":
                                        # For MCP nodes, use the server_id from mcp_info if available
                                        if (
                                            "mcp_info" in previous_node_def
                                            and "server_id" in previous_node_def["mcp_info"]
                                        ):
                                            previous_server_id = previous_node_def["mcp_info"][
                                                "server_id"
                                            ]
                                        else:
                                            # Fallback to the name from the node definition
                                            previous_server_id = previous_node_def["name"]
                                    else:
                                        # For Components nodes, use the definition name
                                        previous_server_id = previous_node_def["name"]

                                    # Fix the input_data
                                    fix_output_node_input_data(
                                        transition,
                                        conditional_node_id,
                                        previous_node_id,
                                        previous_server_id,
                                    )
                                break

                transitions.append(transition)
                sequence += 1
            # Create a transition for this node
            # If there are no outgoing edges (and it's not an output node), create a dummy edge
            elif is_end and not is_output:
                dummy_edge = {
                    "id": f"dummy-edge-{node_id}",
                    "source": node_id,
                    "target": node_id,  # Self-reference for end nodes
                }
                transition = create_transition_from_edge(
                    dummy_edge,
                    nodes,
                    mcp_configs,
                    edges,
                    sequence,
                    is_first=is_first_transition
                    or is_connected_to_start,  # Mark as initial if connected to start
                    has_reflection=has_reflection,
                    is_end=True,
                )
                transitions.append(transition)
                sequence += 1
            else:
                # Get all outgoing edges for this node
                outgoing_edges = []
                for target_id in graph.get(node_id, []):
                    edge = edge_map.get((node_id, target_id))
                    if edge:
                        outgoing_edges.append(edge)

                # Create a single transition with all outgoing connections
                if outgoing_edges:
                    # For conditional nodes, use special transition creation
                    if is_conditional:
                        print(f"      🔧 Creating conditional transition...")
                        try:
                            transition = create_conditional_node_transition(
                                node, edges, sequence, nodes_connected_to_start, nodes
                            )
                            conditional_transitions_created.append(node_id)

                            print(f"      ✅ CONDITIONAL TRANSITION CREATED:")
                            print(f"         - ID: {transition['id']}")
                            print(f"         - Sequence: {transition['sequence']}")
                            print(f"         - Execution Type: {transition['execution_type']}")
                            print(
                                f"         - Tools: {len(transition['node_info']['tools_to_use'])}"
                            )

                            # Log conditional component details
                            tools = transition["node_info"]["tools_to_use"]
                            conditional_tool = next(
                                (tool for tool in tools if tool["tool_name"] == "conditional"), None
                            )
                            if conditional_tool:
                                # Extract conditions from tool_params format
                                conditions = []
                                tool_params = conditional_tool.get("tool_params", {})
                                items = tool_params.get("items", [])
                                for item in items:
                                    if item.get("field_name") == "conditions" and item.get(
                                        "field_value"
                                    ):
                                        conditions = item.get("field_value", [])
                                        break

                                print(f"         - Conditions: {len(conditions)}")
                                for i, condition in enumerate(conditions, 1):
                                    print(
                                        f"           {i}. {condition.get('operator')} '{condition.get('expected_value')}' → {condition.get('next_transition')}"
                                    )

                        except Exception as e:
                            print(f"      ❌ ERROR creating conditional transition: {str(e)}")
                            import traceback

                            traceback.print_exc()
                            raise
                    else:
                        # Use the first edge for the transition creation
                        transition = create_transition_from_edge(
                            outgoing_edges[0],
                            nodes,
                            mcp_configs,
                            edges,  # Pass all edges for input/output data mapping
                            sequence,
                            is_first=is_first_transition
                            or is_connected_to_start,  # Mark as initial if connected to start
                            has_reflection=has_reflection,
                            is_end=False,
                        )

                    # For conditional nodes, no additional routing logic needed
                    # They handle their own routing through the conditional component
                    if not is_conditional:
                        # Check if this node connects to a conditional node
                        # If so, add conditional routing and modify output connections (only for embedded mode)
                        conditional_target = None
                        for target_id in graph.get(node_id, []):
                            target_node = next((n for n in nodes if n["id"] == target_id), None)
                            if target_node and is_conditional_node(target_node):
                                conditional_target = target_node
                                break

                        if conditional_target:
                            # Embedded routing is no longer supported
                            # Conditional nodes now execute as separate components
                            print(
                                f"   ℹ️  Conditional target detected: {conditional_target['id']} - will be handled as separate component"
                            )

                    transitions.append(transition)

                    if is_conditional:
                        # Already logged above in conditional creation
                        pass
                    else:
                        regular_transitions_created.append(node_id)
                        print(f"      ✅ REGULAR TRANSITION CREATED:")
                        print(f"         - ID: {transition['id']}")
                        print(f"         - Sequence: {transition['sequence']}")
                        print(f"         - Execution Type: {transition['execution_type']}")
                        print(f"         - Tools: {len(transition['node_info']['tools_to_use'])}")
                        print(
                            f"         - Input Data: {len(transition['node_info']['input_data'])}"
                        )
                        print(
                            f"         - Output Data: {len(transition['node_info']['output_data'])}"
                        )

                    sequence += 1
                else:
                    # 🚨 CRITICAL FIX: Create transitions for nodes without outgoing edges
                    # This handles nodes at the end of chains (like MCP nodes in loop bodies)
                    print(f"      🔧 Creating transition for node without outgoing edges...")

                    # Create a dummy edge for nodes without outgoing connections
                    dummy_edge = {
                        "id": f"dummy-edge-{node_id}",
                        "source": node_id,
                        "target": node_id,  # Self-reference for nodes without outgoing edges
                    }

                    transition = create_transition_from_edge(
                        dummy_edge,
                        nodes,
                        mcp_configs,
                        edges,  # Pass all edges for input/output data mapping
                        sequence,
                        is_first=is_first_transition or is_connected_to_start,
                        has_reflection=has_reflection,
                        is_end=is_end,  # Use the calculated is_end value
                    )

                    transitions.append(transition)
                    regular_transitions_created.append(node_id)

                    print(f"      ✅ TRANSITION CREATED FOR NODE WITHOUT OUTGOING EDGES:")
                    print(f"         - ID: {transition['id']}")
                    print(f"         - Sequence: {transition['sequence']}")
                    print(f"         - Execution Type: {transition['execution_type']}")
                    print(f"         - Tools: {len(transition['node_info']['tools_to_use'])}")
                    print(f"         - Is End: {is_end}")

                    sequence += 1

            # Only the first node processed is the first transition
            if is_first_transition:
                is_first_transition = False

    # Post-processing for embedded routing is no longer needed
    # All conditional routing is now handled by conditional components

    # Process field mappings to set field_value in tool_params
    # For each transition, look at its input_data to find source_handle and target_handle
    for transition in transitions:
        for input_data in transition["node_info"]["input_data"]:
            # Check if we have target_handle information for handle mappings
            if "_target_handle" in input_data:
                target_handle = input_data["_target_handle"]

                # Set the field_value to null for fields that have handle mappings
                # This ensures no placeholder conflicts and relies purely on handle mapping resolution
                for tool in transition["node_info"]["tools_to_use"]:
                    # Skip conditional components as they don't have tool_params
                    if tool.get("tool_name") == "conditional":
                        continue

                    if "tool_params" in tool and "items" in tool["tool_params"]:
                        for item in tool["tool_params"]["items"]:
                            if item["field_name"] == target_handle:
                                # Always set to null when handle mapping exists - no placeholders
                                item["field_value"] = None

                # Remove the temporary fields
                del input_data["_source_handle"]
                del input_data["_target_handle"]
                if "_from_start_node" in input_data:
                    del input_data["_from_start_node"]

    # Clean up any remaining temporary fields in output_data
    for transition in transitions:
        for output_data in transition["node_info"]["output_data"]:
            if "_source_handle" in output_data:
                del output_data["_source_handle"]
            if "_target_handle" in output_data:
                del output_data["_target_handle"]

    # Create the final transition schema
    transition_schema = {"nodes": transition_nodes, "transitions": transitions}

    # Final comprehensive summary
    print(f"\n" + "=" * 80)
    print("🎉 WORKFLOW CONVERSION COMPLETED SUCCESSFULLY")
    print("=" * 80)
    print(f"📊 FINAL STATISTICS:")
    print(f"   - Total nodes in final schema: {len(transition_nodes)}")
    print(f"   - Total transitions created: {len(transitions)}")
    print(f"   - Conditional transitions: {len(conditional_transitions_created)}")
    print(f"   - Regular transitions: {len(regular_transitions_created)}")

    if conditional_transitions_created:
        print(f"\n🔀 CONDITIONAL TRANSITIONS CREATED:")
        for node_id in conditional_transitions_created:
            transition_id = f"transition-{node_id}"
            transition = next((t for t in transitions if t["id"] == transition_id), None)
            if transition:
                tools = transition["node_info"]["tools_to_use"]
                conditional_tool = next(
                    (tool for tool in tools if tool["tool_name"] == "conditional"), None
                )
                if conditional_tool:
                    # Extract conditions from tool_params format
                    conditions = []
                    tool_params = conditional_tool.get("tool_params", {})
                    items = tool_params.get("items", [])
                    for item in items:
                        if item.get("field_name") == "conditions" and item.get("field_value"):
                            conditions = item.get("field_value", [])
                            break
                    print(f"   - {node_id}: {len(conditions)} conditions")

    if regular_transitions_created:
        print(f"\n⚙️  REGULAR TRANSITIONS CREATED:")
        for node_id in regular_transitions_created:
            transition_id = f"transition-{node_id}"
            transition = next((t for t in transitions if t["id"] == transition_id), None)
            if transition:
                tools = transition["node_info"]["tools_to_use"]
                print(f"   - {node_id}: {len(tools)} tools")

    # Validate final schema structure
    print(f"\n🔍 SCHEMA VALIDATION:")
    validation_errors = []

    for i, transition in enumerate(transitions):
        transition_id = transition.get("id", f"transition-{i}")
        required_fields = [
            "id",
            "sequence",
            "transition_type",
            "execution_type",
            "node_info",
            "result_resolution",
            "end",
        ]

        for field in required_fields:
            if field not in transition:
                validation_errors.append(f"Missing '{field}' in {transition_id}")

        # Check node_info structure
        node_info = transition.get("node_info", {})
        if "node_id" not in node_info:
            validation_errors.append(f"Missing 'node_id' in node_info for {transition_id}")

        # Check tools
        tools = node_info.get("tools_to_use", [])
        for j, tool in enumerate(tools):
            if "tool_id" not in tool:
                validation_errors.append(f"Missing 'tool_id' in tool {j} for {transition_id}")

    if validation_errors:
        print(f"   ❌ VALIDATION ERRORS FOUND:")
        for error in validation_errors:
            print(f"      - {error}")
    else:
        print(f"   ✅ ALL SCHEMA VALIDATION CHECKS PASSED!")

    print(f"\n🚀 CONVERSION COMPLETE - SCHEMA READY FOR EXECUTION")
    print("=" * 80)

    return transition_schema
