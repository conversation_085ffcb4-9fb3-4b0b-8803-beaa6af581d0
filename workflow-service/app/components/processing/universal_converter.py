from typing import Dict, Any, List, ClassVar, Union
import asyncio
import logging
import time
import json
import csv
import io
import re

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    StringInput,
    BoolInput,
    DropdownInput,
    MultilineInput,
)
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult

from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

logger = logging.getLogger(__name__)


class UniversalConverterComponent(BaseNode):
    """
    Universal data type converter supporting JSON, CSV, strings, numbers, booleans, and more.

    This component can convert between various data formats commonly used in workflow automation,
    including JSON parsing/serialization, CSV processing, string/number conversions, and object flattening.
    """

    name: ClassVar[str] = "UniversalConverterComponent"
    display_name: ClassVar[str] = "Universal Converter"
    description: ClassVar[str] = (
        "Convert data between different types (JSON, CSV, String, Number, Boolean, etc.)"
    )
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "ArrowRightLeft"

    inputs: ClassVar[List[InputBase]] = [
        # Input data - can be any type
        create_dual_purpose_input(
            name="input_data",
            display_name="Input Data",
            input_type="multiline",
            required=True,
            info="The data to convert. Can be any type - string, object, array, number, etc. Can be connected from another node or entered directly.",
            input_types=["Any"],
        ),
        # Source type detection
        DropdownInput(
            name="from_type",
            display_name="From Type",
            options=["Auto-detect", "String", "Number", "Boolean", "Object", "Array", "Null"],
            value="Auto-detect",
            info="The current type of your input data. Auto-detect will determine this automatically.",
        ),
        # Target type selection
        DropdownInput(
            name="to_type",
            display_name="To Type",
            options=[
                "String",
                "Number", 
                "Boolean",
                "Object",
                "Array",
                "JSON String",
                "CSV String",
                "Joined String",
                "Split Array",
                "Flattened Object"
            ],
            value="String",
            info="The target type to convert to. JSON String: pretty-formatted JSON. CSV String: comma-separated values. Joined String: array elements joined with delimiter. Split Array: string split by delimiter. Flattened Object: nested object flattened to dot notation.",
        ),
        # Delimiter for CSV/Join/Split operations
        StringInput(
            name="delimiter",
            display_name="Delimiter",
            value=",",
            info="Delimiter for CSV/Join/Split operations (e.g., ',', ';', '|', ' ', '\\t' for tab, '\\n' for newline)",
        ),
        # Pretty formatting option
        BoolInput(
            name="pretty_format",
            display_name="Pretty Format",
            value=True,
            info="For JSON String output, use pretty formatting with indentation and line breaks",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="converted_data", display_name="Converted Data", output_type="Any"),
        Output(name="original_type", display_name="Original Type", output_type="string"),
        Output(name="target_type", display_name="Target Type", output_type="string"),
        Output(name="error", display_name="Error", output_type="string"),
    ]

    def _detect_type(self, data: Any) -> str:
        """
        Auto-detect the type of input data.

        Args:
            data: The data to analyze

        Returns:
            String representing the detected type
        """
        if data is None:
            return "Null"
        elif isinstance(data, bool):
            return "Boolean"
        elif isinstance(data, int):
            return "Number"
        elif isinstance(data, float):
            return "Number"
        elif isinstance(data, str):
            return "String"
        elif isinstance(data, list):
            return "Array"
        elif isinstance(data, dict):
            return "Object"
        else:
            return "String"  # Default fallback

    def _looks_like_json(self, value: str) -> bool:
        """
        Check if a string looks like it could be JSON.

        Args:
            value: The string to check

        Returns:
            True if the string looks like JSON, False otherwise
        """
        if not isinstance(value, str):
            return False

        # Remove leading/trailing whitespace
        value = value.strip()

        # Check for standard JSON patterns
        return ((value.startswith('{') and value.endswith('}')) or 
                (value.startswith('[') and value.endswith(']')) or
                (value.startswith('"') and value.endswith('"') and len(value) > 1))

    def _safe_json_parse(self, json_str: str) -> Any:
        """
        Safely parse a JSON string with error handling and support for multi-level encoding.

        Args:
            json_str: The JSON string to parse

        Returns:
            Parsed JSON data

        Raises:
            ValueError: If the JSON cannot be parsed
        """
        try:
            # First parse attempt
            parsed = json.loads(json_str)
            
            # Check if the result is still a JSON string (double-encoded)
            if isinstance(parsed, str) and self._looks_like_json(parsed):
                logger.debug("Detected double-encoded JSON, parsing again")
                # Try to parse again for double-encoded JSON
                try:
                    parsed = json.loads(parsed)
                    logger.debug(f"Successfully parsed double-encoded JSON, result type: {type(parsed).__name__}")
                except json.JSONDecodeError:
                    logger.debug("Second parse failed, returning first parse result")
                    pass  # Keep the first parse result
            
            return parsed
            
        except json.JSONDecodeError as e:
            # Try to fix common JSON issues
            try:
                # Remove trailing commas
                fixed_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
                # Replace single quotes with double quotes
                fixed_str = re.sub(r"'([^']*)'", r'"\1"', fixed_str)
                return json.loads(fixed_str)
            except json.JSONDecodeError:
                raise ValueError(f"Invalid JSON format: {str(e)}")

    def _string_to_number(self, data: str) -> Union[int, float]:
        """
        Convert string to number (int or float).

        Args:
            data: String to convert

        Returns:
            Converted number

        Raises:
            ValueError: If conversion fails
        """
        try:
            # Try integer first
            if '.' not in str(data) and 'e' not in str(data).lower():
                return int(data)
            else:
                return float(data)
        except (ValueError, TypeError):
            raise ValueError(f"Cannot convert '{data}' to number")

    def _string_to_boolean(self, data: str) -> bool:
        """
        Convert string to boolean.

        Args:
            data: String to convert

        Returns:
            Boolean value

        Raises:
            ValueError: If conversion fails
        """
        if isinstance(data, str):
            lower_data = data.lower().strip()
            if lower_data in ['true', 'yes', '1', 'on', 'y']:
                return True
            elif lower_data in ['false', 'no', '0', 'off', 'n']:
                return False
        
        raise ValueError(f"Cannot convert '{data}' to boolean")

    def _array_to_csv_string(self, data: List, delimiter: str = ",") -> str:
        """
        Convert array to CSV string.

        Args:
            data: Array to convert
            delimiter: CSV delimiter

        Returns:
            CSV string
        """
        if not isinstance(data, list):
            raise ValueError("Input must be an array for CSV conversion")

        output = io.StringIO()
        writer = csv.writer(output, delimiter=delimiter)
        
        # Handle array of arrays (rows)
        if data and isinstance(data[0], list):
            writer.writerows(data)
        else:
            # Single row
            writer.writerow(data)
        
        return output.getvalue().strip()

    def _csv_string_to_array(self, data: str, delimiter: str = ",") -> List:
        """
        Convert CSV string to array.

        Args:
            data: CSV string to parse
            delimiter: CSV delimiter

        Returns:
            Array of values or arrays
        """
        if not isinstance(data, str):
            raise ValueError("Input must be a string for CSV parsing")

        reader = csv.reader(io.StringIO(data), delimiter=delimiter)
        rows = list(reader)
        
        # If only one row, return as simple array
        if len(rows) == 1:
            return rows[0]
        
        return rows

    def _flatten_object(self, data: Dict, separator: str = ".") -> Dict:
        """
        Flatten a nested object.

        Args:
            data: Object to flatten
            separator: Key separator

        Returns:
            Flattened object
        """
        def _flatten_recursive(obj: Dict, parent_key: str = "") -> Dict:
            items = []
            for key, value in obj.items():
                new_key = f"{parent_key}{separator}{key}" if parent_key else key
                
                if isinstance(value, dict):
                    items.extend(_flatten_recursive(value, new_key).items())
                else:
                    items.append((new_key, value))
            
            return dict(items)

        if not isinstance(data, dict):
            raise ValueError("Input must be an object for flattening")

        return _flatten_recursive(data)

    def _convert_data(self, input_data: Any, from_type: str, to_type: str, delimiter: str = ",", pretty_format: bool = True) -> Any:
        """
        Convert data from one type to another.

        Args:
            input_data: The data to convert
            from_type: Source data type
            to_type: Target data type
            delimiter: Delimiter for CSV/Join/Split operations
            pretty_format: Whether to use pretty formatting for JSON

        Returns:
            Converted data

        Raises:
            ValueError: If conversion is not possible
        """
        logger.debug(f"Converting from {from_type} to {to_type}")
        
        # Handle String conversions
        if to_type == "String":
            if isinstance(input_data, str):
                return input_data
            elif isinstance(input_data, (dict, list)):
                return json.dumps(input_data, indent=2 if pretty_format else None, ensure_ascii=False)
            else:
                return str(input_data)

        # Handle Number conversions
        elif to_type == "Number":
            if isinstance(input_data, (int, float)):
                return input_data
            elif isinstance(input_data, str):
                return self._string_to_number(input_data)
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Number")

        # Handle Boolean conversions
        elif to_type == "Boolean":
            if isinstance(input_data, bool):
                return input_data
            elif isinstance(input_data, str):
                return self._string_to_boolean(input_data)
            elif isinstance(input_data, (int, float)):
                return bool(input_data)
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Boolean")

        # Handle Object conversions
        elif to_type == "Object":
            if isinstance(input_data, dict):
                return input_data
            elif isinstance(input_data, str):
                return self._safe_json_parse(input_data)
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Object")

        # Handle Array conversions
        elif to_type == "Array":
            if isinstance(input_data, list):
                return input_data
            elif isinstance(input_data, str):
                parsed = self._safe_json_parse(input_data)
                if isinstance(parsed, list):
                    return parsed
                else:
                    raise ValueError("Parsed JSON is not an array")
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Array")

        # Handle JSON String conversions
        elif to_type == "JSON String":
            if isinstance(input_data, str):
                # Validate it's valid JSON first
                self._safe_json_parse(input_data)
                return input_data
            elif isinstance(input_data, (dict, list)):
                return json.dumps(input_data, indent=2 if pretty_format else None, ensure_ascii=False)
            else:
                return json.dumps(input_data, indent=2 if pretty_format else None, ensure_ascii=False)

        # Handle CSV String conversions
        elif to_type == "CSV String":
            if isinstance(input_data, list):
                return self._array_to_csv_string(input_data, delimiter)
            elif isinstance(input_data, str):
                # Already a string, assume it's CSV
                return input_data
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to CSV String")

        # Handle Joined String conversions
        elif to_type == "Joined String":
            if isinstance(input_data, list):
                return delimiter.join(str(item) for item in input_data)
            elif isinstance(input_data, str):
                return input_data
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Joined String")

        # Handle Split Array conversions
        elif to_type == "Split Array":
            if isinstance(input_data, str):
                return input_data.split(delimiter)
            elif isinstance(input_data, list):
                return input_data
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Split Array")

        # Handle Flattened Object conversions
        elif to_type == "Flattened Object":
            if isinstance(input_data, dict):
                return self._flatten_object(input_data)
            elif isinstance(input_data, str):
                parsed = self._safe_json_parse(input_data)
                if isinstance(parsed, dict):
                    return self._flatten_object(parsed)
                else:
                    raise ValueError("Parsed JSON is not an object")
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Flattened Object")

        else:
            raise ValueError(f"Unsupported conversion type: {to_type}")

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute the UniversalConverterComponent.

        This method converts data from one type to another locally (following CombineText pattern).

        Args:
            context: The workflow execution context containing input values.

        Returns:
            A NodeResult with the execution results.
        """
        # Start timing for performance measurement
        start_time = time.time()

        # Log execution start
        context.log(f"Executing {self.name}...")

        try:
            # Get inputs from context
            input_data = self.get_input_value("input_data", context)
            from_type = self.get_input_value("from_type", context, "Auto-detect")
            to_type = self.get_input_value("to_type", context, "String")
            delimiter = self.get_input_value("delimiter", context, ",")
            pretty_format = self.get_input_value("pretty_format", context, True)

            # Log input values for debugging
            logger.debug(f"Input data type: {type(input_data).__name__}")
            logger.debug(f"From type: {from_type}")
            logger.debug(f"To type: {to_type}")
            logger.debug(f"Delimiter: '{delimiter}'")
            logger.debug(f"Pretty format: {pretty_format}")

            # Validate inputs
            if input_data is None and input_data != 0 and input_data != False:  # Allow 0 and False as valid inputs
                error_msg = "Input data is missing. Please provide data to convert."
                context.log(error_msg)
                return NodeResult.error(error_message=error_msg)

            if not to_type:
                error_msg = "Target type is missing. Please select a type to convert to."
                context.log(error_msg)
                return NodeResult.error(error_message=error_msg)

            # Process literal escape sequences in delimiter
            processed_delimiter = delimiter.replace("\\n", "\n").replace("\\t", "\t").replace("\\r", "\r")

            # Auto-detect source type if needed
            if from_type == "Auto-detect":
                from_type = self._detect_type(input_data)
                logger.debug(f"Auto-detected source type: {from_type}")

            # Perform the conversion locally
            try:
                converted_data = self._convert_data(input_data, from_type, to_type, processed_delimiter, pretty_format)
                
                # Log success
                execution_time = time.time() - start_time
                context.log(f"Data converted successfully from {from_type} to {to_type}. Time: {execution_time:.2f}s")
                logger.info(f"Data converted successfully from {from_type} to {to_type}. Time: {execution_time:.2f}s")

                # Return success result
                return NodeResult.success(
                    outputs={
                        "converted_data": converted_data,
                        "original_type": from_type,
                        "target_type": to_type,
                        "error": None
                    },
                    execution_time=execution_time
                )

            except ValueError as e:
                error_msg = f"Conversion error: {str(e)}"
                context.log(error_msg)
                logger.error(f"{error_msg}")
                return NodeResult.success(
                    outputs={
                        "converted_data": None,
                        "original_type": from_type,
                        "target_type": to_type,
                        "error": error_msg
                    },
                    execution_time=time.time() - start_time
                )

        except Exception as e:
            # Log error
            error_msg = f"Unexpected error during data conversion: {str(e)}"
            context.log(error_msg)
            logger.error(f"{error_msg}", exc_info=True)

            # Return error result
            return NodeResult.success(
                outputs={
                    "converted_data": None,
                    "original_type": from_type if 'from_type' in locals() else "Unknown",
                    "target_type": to_type if 'to_type' in locals() else "Unknown",
                    "error": error_msg
                },
                execution_time=time.time() - start_time
            )

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get the value of an input from the context.

        This method handles both direct inputs and handle inputs, prioritizing handle inputs.

        Args:
            input_name: The name of the input.
            context: The workflow execution context.
            default: The default value to return if the input is not found.

        Returns:
            The input value, or the default value if not found.
        """
        # Get all inputs for the current node
        node_inputs = context.node_outputs.get(context.current_node_id, {})

        # Check for handle input first (prioritize connected values)
        handle_value = node_inputs.get(f"{input_name}")
        if handle_value is not None:
            return handle_value

        # If no handle input, check for direct input
        direct_value = node_inputs.get(input_name)
        if direct_value is not None:
            return direct_value

        # If neither is found, return the default value
        return default

    # Legacy method for backward compatibility
    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the UniversalConverterComponent.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Args:
            **kwargs: Contains the input values.

        Returns:
            A dictionary with the component's outputs.
        """
        logger.warning(f"Using legacy build method for {self.name}. Please update to use execute method.")

        # Get inputs
        input_data = kwargs.get("input_data")
        from_type = kwargs.get("from_type", "Auto-detect")
        to_type = kwargs.get("to_type", "String")
        delimiter = kwargs.get("delimiter", ",")
        pretty_format = kwargs.get("pretty_format", True)

        # Validate inputs
        if input_data is None and input_data != 0 and input_data != False:
            return {"error": "Input data is missing. Please provide data to convert."}

        if not to_type:
            return {"error": "Target type is missing. Please select a type to convert to."}

        try:
            # Process literal escape sequences in delimiter
            processed_delimiter = delimiter.replace("\\n", "\n").replace("\\t", "\t").replace("\\r", "\r")

            # Auto-detect source type if needed
            if from_type == "Auto-detect":
                from_type = self._detect_type(input_data)
                logger.debug(f"Auto-detected source type: {from_type}")

            # Perform the conversion locally (legacy mode)
            try:
                converted_data = self._convert_data(input_data, from_type, to_type, processed_delimiter, pretty_format)
                logger.info(f"Legacy conversion successful: {from_type} -> {to_type}")
                
                return {
                    "converted_data": converted_data,
                    "original_type": from_type,
                    "target_type": to_type,
                    "error": None
                }

            except ValueError as e:
                error_msg = f"Conversion error: {str(e)}"
                logger.error(error_msg)
                return {
                    "converted_data": None,
                    "original_type": from_type,
                    "target_type": to_type,
                    "error": error_msg
                }

        except Exception as e:
            error_msg = f"Unexpected error during legacy conversion: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}