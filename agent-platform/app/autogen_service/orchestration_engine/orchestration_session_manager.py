"""
Orchestration Team Session Manager for handling orchestration team sessions with human-in-the-loop functionality.
"""

import asyncio
import logging
import re
import uuid
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

from autogen_agentchat.messages import TextMessage

from ...helper.session_manager import SessionManager
from ...kafka_client.producer import kafka_producer
from ...schemas.chat import AgentResponse, MessageType
from ...schemas.kafka import HumanInputRequest, MessageAttachment
from ...shared.config.base import get_settings
from ...tools.mcp_tool_loader import McpToolLoader
from ...utils.enums import Mode, Resources
from ...utils.file_processor import FileProcessor
from ..message_processor import MessageProcessor
from .orchestrator_agent import build_orchestration_team

logger = logging.getLogger(__name__)


class OrchestrationSessionManager:
    """
    Manages orchestration team sessions with human-in-the-loop functionality.
    """

    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
        self.settings = get_settings()
        self.file_processor = FileProcessor()
        self.message_processor = MessageProcessor()
        self.mcp_loader = McpToolLoader()
        self.active_team_conversations = set()
        self.human_input_pending: Dict[str, asyncio.Event] = {}
        self.human_input_responses: Dict[str, str] = {}
        self._lock = asyncio.Lock()

    async def create_orchestration_session(
        self,
        user_id: str,
        organization_id: Optional[str] = None,
        variables: Optional[Dict[str, Any]] = None,
        chat_context: Optional[List[Dict[str, Any]]] = [],
    ) -> str:
        """
        Create a new orchestration team session.

        Args:
            user_id: ID of the user creating the session
            organization_id: Optional organization ID
            variables: Optional variables for the session
            chat_context: Optional chat context for the session

        Returns:
            Session ID for the orchestration team session
        """
        try:
            # Create a dummy agent config for session manager compatibility
            dummy_agent_config = {
                "name": "orchestration_team",
                "description": "Global orchestration team with human-in-the-loop",
                "agent_type": "orchestration_team",
                "system_message": "Orchestration team session",
            }

            chat_context.append(
                {
                    "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "name": "Ruh Master",
                    "rules": [
                        "DO NOT mention the mode that has been selected by the user in the conversation",
                        "For queries regarding yourself and your system, you should answer directly without delegation",
                    ],
                }
            )

            # Create session in the session manager
            session_id = await self.session_manager.create_session(
                agent_config=dummy_agent_config,
                user_id=user_id,
                communication_type="orchestration_team",
                organization_id=organization_id,
                use_knowledge=False,
                agent_group_id="orchestration_team",
                variables=variables,
                chat_context=chat_context,
            )

            logger.info(f"Created orchestration session: {session_id}")
            return session_id

        except Exception as e:
            logger.error(f"Failed to create orchestration session: {str(e)}")
            raise

    async def _process_message_response(self, response: Any) -> Optional[AgentResponse]:
        """Process a single message response"""

        msg_type: MessageType = self.message_processor.get_message_type(response)

        source, content, metadata = self.message_processor.extract_message_content(
            response
        )

        return AgentResponse(
            content=content,
            source=source,
            models_usage=self.message_processor.extract_models_usage(response),
            message_type=msg_type.value,
            metadata=metadata,
        )

    async def process_orchestration_chat(
        self,
        session_id: str,
        user_message: str,
        run_id: str,
        mode: Optional[str] = None,
        attachments: Optional[List[MessageAttachment]] = None,
        tools: Optional[List[str]] = None,
        resource: Optional[str] = None,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a chat message with the orchestration team, including human-in-the-loop functionality.
        Supports both text and multimodal messages with attachments.

        Args:
            session_id: The session ID
            user_message: The user's message
            run_id: The run ID for this chat
            attachments: Optional list of file attachments (images, documents, etc.)

        Yields:
            Chat response chunks
        """
        team_conversation_id = None

        try:

            session_data = await self.session_manager.get_session_data(session_id)

            if not session_data:
                yield {
                    "error": f"Orchestration session not found: {session_id}",
                    "success": False,
                    "final": True,
                }
                return

            (
                agent_config,
                communication_type,
                memory,
                cancellation_token,
                organization_id,
                use_knowledge,
                variables,
                user_id,
            ) = session_data

            # Create a fresh team instance for this conversation
            # This prevents "team is already running" errors
            try:

                tools = await self.mcp_loader.load_mcps_as_tools(run_id, user_id, tools)

                team = await build_orchestration_team(
                    run_id,
                    memory=memory,
                    user_id=user_id,
                    organisation_id=organization_id,
                    tools=tools,
                    resources=Resources(resource) if resource else Resources.ALL,
                    mode=Mode(mode) if mode else Mode.ASK,
                )
                logger.info(
                    f"Created fresh orchestration team for session: " f"{session_id}"
                )
            except Exception as e:
                logger.error(f"Failed to create orchestration team: {str(e)}")
                yield {
                    "error": f"Failed to create orchestration team: {str(e)}",
                    "success": False,
                    "final": True,
                }
                return

            team_conversation_id = str(uuid.uuid4())

            # Update session with current conversation ID and track active conversation
            async with self._lock:
                self.active_team_conversations.add(team_conversation_id)

            # Process attachments and create appropriate message
            attachment_summary = ""
            if attachments:
                # Validate attachments first
                validation_errors = self.file_processor.validate_attachments(
                    attachments
                )
                if validation_errors:
                    error_msg = "Attachment validation failed: " + "; ".join(
                        validation_errors
                    )
                    logger.error(error_msg)
                    yield {
                        "error": error_msg,
                        "success": False,
                        "final": True,
                    }
                    return

                attachment_summary = self.file_processor.get_attachment_summary(
                    attachments
                )
                logger.info(
                    f"Processing orchestration chat with attachments: {attachment_summary}"
                )

            # Add user message to conversation history
            user_msg_entry = {
                "role": "user",
                "content": user_message,
                "timestamp": datetime.utcnow().isoformat(),
                "attachments": (
                    [att.model_dump() for att in attachments] if attachments else []
                ),
                "attachment_summary": attachment_summary,
            }

            # Update session memory
            await self.session_manager.update_session_memory(
                session_id=session_id,
                message=user_msg_entry,
            )

            # Create appropriate message for the team (text or multimodal)
            try:
                if attachments:
                    # Create multimodal message with attachments
                    team_message = await self.file_processor.create_multimodal_message(
                        user_message, attachments, source="user"
                    )
                    logger.info(
                        f"Created multimodal message for orchestration team with {attachment_summary}"
                    )
                else:
                    # Create simple text message
                    team_message = TextMessage(content=user_message, source="user")

            except Exception as e:
                logger.error(f"Failed to create message for orchestration team: {e}")
                yield {
                    "error": f"Failed to process attachments: {e}",
                    "success": False,
                    "final": True,
                }
                return

            # Process with orchestration team using proper human-in-the-loop pattern
            async for response_chunk in self._process_team_conversation(
                session_data=session_data,
                team=team,
                initial_message=team_message,
                session_id=session_id,
                run_id=run_id,
                team_conversation_id=team_conversation_id,
                tools=tools,
                resource=resource,
                mode=mode,
            ):
                yield response_chunk

            # Clean up the team conversation from active tracking
            async with self._lock:
                if team_conversation_id:
                    self.active_team_conversations.discard(team_conversation_id)

            yield {
                "run_id": run_id,
                "session_id": session_id,
                "message": "Orchestration team conversation completed",
                "success": True,
                "final": True,
                "team_conversation_id": team_conversation_id,
            }

        except Exception as e:
            logger.error(f"Error in orchestration chat processing: {str(e)}")

            # Clean up the team conversation from active tracking on error
            try:
                async with self._lock:
                    if team_conversation_id:
                        self.active_team_conversations.discard(team_conversation_id)
            except Exception:
                pass  # Ignore cleanup errors

            yield {
                "run_id": run_id,
                "session_id": session_id,
                "error": f"Error: {str(e)}",
                "success": False,
                "final": True,
            }

    async def _process_team_conversation(
        self,
        session_data,
        team,
        initial_message,
        session_id: str,
        run_id: str,
        team_conversation_id: str,
        tools: Optional[List[str]] = None,
        resource: Optional[str] = None,
        mode: Optional[str] = None,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process team conversation with proper human-in-the-loop handling.

        This method handles the team conversation flow without causing
        concurrent team.run_stream() calls that lead to "team already running" errors.
        """
        current_message = initial_message

        # Process the team conversation in a single stream
        # to avoid "team already running" errors
        try:
            async for response in team.run_stream(task=current_message):

                print("team response:", response)

                # Process response
                agent_response = await self._process_message_response(response)

                # Add to conversation history for non-empty responses
                if agent_response and (
                    agent_response.message_type == "text"
                    or agent_response.message_type == "structured_message"
                ):
                    # Stream as thinking content
                    completion_markers = [
                        "ORCHESTRATOR_TERMINATE",
                        "SEARCH_COMPLETE",
                        "SUMMARY_COMPLETE",
                        "TERMINATE",
                    ]
                    agent_response.content = re.sub(
                        r"(" + "|".join(completion_markers) + ")",
                        "",
                        agent_response.content,
                    ).strip()

                    team_msg_entry = {
                        "role": agent_response.source,
                        "content": agent_response.content,
                        "timestamp": datetime.utcnow().isoformat(),
                    }

                    # Update session memory
                    await self.session_manager.update_session_memory(
                        session_id=session_id,
                        message=team_msg_entry,
                    )

                # Check if this is a user input request
                if agent_response.message_type == "user_input_request":

                    res = agent_response.to_dict()
                    res["metadata"]["team_conversation_id"] = team_conversation_id

                    # Yield the user input request to the client
                    yield {
                        "run_id": run_id,
                        "session_id": session_id,
                        "agent_response": res,
                        "success": True,
                        "final": False,
                        "team_conversation_id": team_conversation_id,
                        "human_input_requested": True,
                        "message": "Human input requested by the team",
                    }

                    # Request human input via Kafka
                    human_input = await self._request_human_input(
                        session_id=session_id,
                        run_id=run_id,
                        team_conversation_id=team_conversation_id,
                        prompt=(agent_response.content or "The team needs your input."),
                        requesting_agent=agent_response.source,
                    )

                    if human_input:
                        # Add to conversation history
                        human_msg_entry = {
                            "role": "user",
                            "content": human_input,
                            "timestamp": datetime.utcnow().isoformat(),
                            "type": "human_input",
                        }

                        # Update session memory
                        await self.session_manager.update_session_memory(
                            session_id=session_id,
                            message=human_msg_entry,
                        )

                        # Yield human input acknowledgment
                        yield {
                            "run_id": run_id,
                            "session_id": session_id,
                            "message": f"Human input received: {human_input}",
                            "success": True,
                            "final": False,
                            "team_conversation_id": team_conversation_id,
                            "human_input": human_input,
                        }

                        # Continue the conversation with human input
                        # by creating a new team instance and continuing
                        async for follow_up_chunk in self._continue_with_human_input(
                            session_data=session_data,
                            human_input=human_input,
                            session_id=session_id,
                            run_id=run_id,
                            team_conversation_id=team_conversation_id,
                            tools=tools,
                            resource=resource,
                            mode=mode,
                        ):
                            yield follow_up_chunk
                        return  # Exit after handling human input continuation
                    else:
                        # No human input received, continue without it
                        yield {
                            "run_id": run_id,
                            "session_id": session_id,
                            "message": "No human input received, continuing...",
                            "success": True,
                            "final": False,
                            "team_conversation_id": team_conversation_id,
                        }
                else:
                    # Regular response, yield it
                    yield {
                        "run_id": run_id,
                        "session_id": session_id,
                        "agent_response": agent_response.to_dict(),
                        "success": True,
                        "final": agent_response.message_type == "task_result",
                        "team_conversation_id": team_conversation_id,
                    }

                if agent_response.message_type == "task_result":
                    return  # Exit the conversation loop

        except Exception as e:
            logger.error(f"Error in team conversation processing: {str(e)}")
            yield {
                "run_id": run_id,
                "session_id": session_id,
                "error": f"Team conversation error: {str(e)}",
                "success": False,
                "final": True,
                "team_conversation_id": team_conversation_id,
            }

    async def _continue_with_human_input(
        self,
        session_data,
        human_input: str,
        session_id: str,
        run_id: str,
        team_conversation_id: str,
        tools: Optional[List[str]] = None,
        resource: Optional[str] = None,
        mode: Optional[str] = None,
        use_tool: bool = False,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Continue the team conversation with human input using a fresh team instance.
        """
        try:

            (
                agent_config,
                communication_type,
                memory,
                cancellation_token,
                organization_id,
                use_knowledge,
                variables,
                user_id,
            ) = session_data

            fresh_team = await build_orchestration_team(
                run_id,
                memory=memory,
                user_id=user_id,
                organisation_id=organization_id,
                tools=tools,
                resources=Resources(resource) if resource else Resources.ALL,
                mode=Mode(mode) if mode else Mode.ASK,
            )

            # Create message with human input
            human_message = TextMessage(content=human_input, source="user")

            # Continue with the fresh team
            async for response in fresh_team.run_stream(task=human_message):
                print("follow-up team response:", response)

                # Process response
                agent_response = await self._process_message_response(response)

                # Add to conversation history for non-empty responses
                if agent_response and (
                    agent_response.message_type == "text"
                    or agent_response.message_type == "structured_message"
                ):
                    # Stream as thinking content
                    completion_markers = [
                        "ORCHESTRATOR_TERMINATE",
                        "SEARCH_COMPLETE",
                        "SUMMARY_COMPLETE",
                        "TERMINATE",
                    ]
                    agent_response.content = re.sub(
                        r"(" + "|".join(completion_markers) + ")",
                        "",
                        agent_response.content,
                    ).strip()
                    team_msg_entry = {
                        "role": agent_response.source,
                        "content": agent_response.content,
                        "agent": agent_response.source,
                        "timestamp": datetime.utcnow().isoformat(),
                        "message_type": agent_response.message_type,
                    }

                    # Update session memory
                    await self.session_manager.update_session_memory(
                        session_id=session_id,
                        message=team_msg_entry,
                    )

                # Yield response
                yield {
                    "run_id": run_id,
                    "session_id": session_id,
                    "agent_response": agent_response.to_dict(),
                    "success": True,
                    "final": agent_response.message_type == "task_result",
                    "team_conversation_id": team_conversation_id,
                }

                if agent_response.message_type == "task_result":
                    return  # Exit the conversation loop

        except Exception as e:
            logger.error(f"Error in human input continuation: {str(e)}")
            yield {
                "run_id": run_id,
                "session_id": session_id,
                "error": f"Human input continuation error: {str(e)}",
                "success": False,
                "final": True,
                "team_conversation_id": team_conversation_id,
            }

    def _should_end_conversation(self, response_content: str) -> bool:
        """
        Determine if the conversation should end based on response content.

        Args:
            response_content: The response content to analyze

        Returns:
            True if conversation should end
        """
        end_triggers = [
            "TERMINATE",
            "task completed",
            "conversation complete",
            "final answer",
            "conclusion",
        ]

        content_lower = response_content.lower()
        return any(trigger.lower() in content_lower for trigger in end_triggers)

    async def _request_human_input(
        self,
        session_id: str,
        run_id: str,
        team_conversation_id: str,
        prompt: str,
        requesting_agent: Optional[str] = None,
        timeout_seconds: int = 300,
    ) -> Optional[str]:
        """
        Request human input via Kafka and wait for response.

        Args:
            session_id: The session ID
            run_id: The run ID
            team_conversation_id: The team conversation ID
            prompt: The prompt for human input
            context: Recent conversation context
            requesting_agent: The agent requesting input
            timeout_seconds: Timeout for human response

        Returns:
            Human input string or None if timeout
        """
        try:
            # Create human input request
            input_request = HumanInputRequest(
                session_id=session_id,
                run_id=run_id,
                team_conversation_id=team_conversation_id,
                prompt=prompt,
                timeout_seconds=timeout_seconds,
                requesting_agent=requesting_agent,
            )

            # Create event for waiting
            input_event = asyncio.Event()
            self.human_input_pending[team_conversation_id] = input_event

            # Send request via Kafka
            await kafka_producer.send_message(
                topic=self.settings.kafka.kafka_human_input_request_topic,
                message=input_request.model_dump(),
                headers=[
                    ("session_id", session_id.encode("utf-8")),
                    ("run_id", run_id.encode("utf-8")),
                    (
                        "team_conversation_id",
                        team_conversation_id.encode("utf-8"),
                    ),
                ],
            )

            # Wait for human input with timeout
            try:
                await asyncio.wait_for(input_event.wait(), timeout=timeout_seconds)

                # Get the response
                human_input = self.human_input_responses.get(team_conversation_id)

                # Clean up
                if team_conversation_id in self.human_input_pending:
                    del self.human_input_pending[team_conversation_id]
                if team_conversation_id in self.human_input_responses:
                    del self.human_input_responses[team_conversation_id]

                return human_input

            except asyncio.TimeoutError:
                logger.warning(
                    f"Human input timeout for conversation: " f"{team_conversation_id}"
                )

                # Clean up
                if team_conversation_id in self.human_input_pending:
                    del self.human_input_pending[team_conversation_id]

                return None

        except Exception as e:
            logger.error(f"Error requesting human input: {str(e)}")
            return None

    async def handle_human_input_response(
        self,
        team_conversation_id: str,
        user_input: str,
    ) -> bool:
        """
        Handle human input response.

        Args:
            team_conversation_id: The team conversation ID
            user_input: The human input

        Returns:
            True if handled successfully
        """
        try:
            if team_conversation_id in self.human_input_pending:
                # Store the response
                self.human_input_responses[team_conversation_id] = user_input

                # Signal the waiting coroutine
                self.human_input_pending[team_conversation_id].set()

                logger.info(
                    f"Human input received for conversation: " f"{team_conversation_id}"
                )
                return True
            else:
                logger.warning(
                    f"No pending input request for conversation: "
                    f"{team_conversation_id}"
                )
                return False

        except Exception as e:
            logger.error(f"Error handling human input response: {str(e)}")
            return False
