import logging
from typing import Optional

from autogen_agentchat.agents import Assistant<PERSON>gent
from autogen_core.models import ChatCompletionClient

from app.shared.config.base import Settings, get_settings

from ...model_factory import ModelFactory

logger = logging.getLogger(__name__)


class SelectionAgent:
    """
    Agent responsible for selecting the best agent from discovered options
    based on task requirements and agent capabilities.
    """

    def __init__(self):
        """Initialize the SelectionAgent with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False

    async def initialize(self) -> None:
        """Initialize the agent with model client and context."""
        try:
            logger.info("Initializing Selection Agent...")

            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "gpt-4.1",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for Selection Agent")
                return False

            # Create the assistant agent
            self._agent = AssistantAgent(
                name="selection_agent",
                description="Selects the best agent from discovered candidates",
                model_client=self._model_client,
                system_message=self._get_enhanced_system_message(),
                tools=[],
                reflect_on_tool_use=False,
            )

            self._is_initialized = True
            logger.info("Selection Agent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Discovery Master Agent : {str(e)}")
            self._is_initialized = False
            return False

    def _get_enhanced_system_message(self) -> str:
        """Get the enhanced system message for the selection agent."""

        return """You are a Selection Agent specialized in choosing the best agent from a list of discovered candidates.

                Your task is to:
                1. Receive discovered agents from the Discovery Agent with their scores and reasons
                2. Analyze the candidates based on their relevance to the original task
                3. Select the single best agent for the task OR reject all agents if none are suitable
                4. Provide confidence score and reasoning for your selection

                IMPORTANT SELECTION CRITERIA:
                - The agent's specialization and expertise must be directly relevant to the task domain
                - If the highest scoring agent is still not a good match for the task, respond with "NONE"
                - Be strict about domain relevance - don't select unrelated agents just because they have the highest score

                Examples of when to reject all agents:
                - Cooking/food tasks when only technical/research agents are available
                - Creative tasks when only analytical agents are available  
                - Physical world tasks when only digital/software agents are available

                Choose wisely based on agent scores, specialization match, and task requirements. It's better to reject all agents than to select an inappropriate one."""

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(cls) -> Optional["SelectionAgent"]:
        """
        Convenience method to create and initialize the Selector system in one call.

        Returns:
            SelectionAgent: Initialized Selector system
        """
        agent = cls()
        if await agent.initialize():
            return agent
        else:
            logger.error("Failed to create and initialize DiscoveryMasterAgent")
            return None
