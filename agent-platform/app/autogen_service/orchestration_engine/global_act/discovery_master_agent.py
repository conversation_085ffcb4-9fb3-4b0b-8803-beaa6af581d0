from __future__ import annotations

import json
import logging
import os
from typing import Dict, List, Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_core.models import ChatCompletionClient
from autogen_core.tools import FunctionTool
from pydantic import BaseModel

from app.shared.config.base import Settings, get_settings

from ...model_factory import ModelFactory


class EmployeeProfile(BaseModel):
    id: str
    name: str
    description: str
    avatar: str
    agent_topic_type: str
    category: str


class EmployeeAssignment(BaseModel):
    assigned_task: Optional[str]
    employee_profile: EmployeeProfile


class AgentSelectionResponse(BaseModel):
    agent_found: bool
    matched_employee: Optional[EmployeeAssignment] = None
    message: Optional[str]
    task_assigned: bool


logger = logging.getLogger(__name__)

# Disable autogen logging for cleaner output
for logger_name in [
    "autogen_agentchat",
    "autogen_core",
    "_single_threaded_agent_runtime",
    "autogen_runtime_core",
    "autogen_agentchat.teams",
    "autogen_agentchat.agents",
]:
    logger_to_silence = logging.getLogger(logger_name)
    logger_to_silence.setLevel(logging.CRITICAL)
    logger_to_silence.propagate = False


class DiscoveryMasterEmployee:
    """
    Discovery Master Agent
    This agent is responsible for discovering and scoring specialized agents based on query analysis
    from the registry.
    It uses a curated registry of agents to find the best matches for user tasks."""

    def __init__(self):
        """Initialize the DiscoveryMasterEmployee with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False

    async def initialize(self) -> bool:
        """
        Initialize the Discovery Master Agent.
        """
        try:
            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "gpt-4.1",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for Discovery Master Agent")
                return False

            self._load_agents_tool = FunctionTool(
                fetch_employees,
                name="fetch_employees",
                description="Fetches the specialized agents registry",
                strict=True,
            )

            self._agent = AssistantAgent(
                name="DiscoveryMasterEmployee",
                description="A Discovery Employee that analyzes user queries, matches them with specialized employees from a registry by calling the `fetch_employees` tool, and returns structured recommendations based on domain, complexity, and technical requirements. To be only used in `ACT` mode.",
                model_client=self._model_client,
                tools=[self._load_agents_tool],
                reflect_on_tool_use=True,
                system_message=self._get_enhanced_system_message(),
                output_content_type=AgentSelectionResponse,
            )

            self._is_initialized = True
            logger.info("Discovery Master Agent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Discovery Master Agent : {str(e)}")
            self._is_initialized = False
            return False

    def _get_enhanced_system_message(self) -> str:
        """Get the enhanced system message for the discovery agent."""

        return f"""You are a Discovery Employee specialized in finding the most suitable specialized employee, for a given task, from a curated registry.

                1. Upon receiving a query, your first task is to analyze the query and extract:
                    1. Domain/category of the query
                    2. Task summary in one clear sentence
                    3. Technical requirements needed
                    4. Keywords for employee matching
                    5. Urgency level (low, medium, high)
                    6. Complexity level (simple, moderate, complex)

                2. Next, you have to use the `fetch_employees` tool to fetch the employees registry.
                   The employees registry contains information about all the specialized employees, including their IDs, names, specializations, expertise, and keywords.

                3. Next, you have to decide which specialized employee is most suitable for the task based on the extracted requirements.

                4. You should respond in a structured format following the JSON format, which includes providing the task, in detailed context, for the specialized employee.
                   If the query is to check the availability of a specialized employee, the task should not be provided and should also not be assigned.

                Only suggest employees that exist in the registry fetched by the `fetch_employees` tool. If no suitable employee is found, respond with a message indicating that no specialized employee is available for the given task.
            """

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(cls) -> Optional["DiscoveryMasterEmployee"]:
        """
        Convenience method to create and initialize the Selector system in one call.

        Returns:
            DiscoveryMasterEmployee: Initialized Selector system
        """
        agent = cls()
        if await agent.initialize():
            return agent
        else:
            logger.error("Failed to create and initialize DiscoveryMasterEmployee")
            return None


def fetch_employees() -> List[str]:
    """Format the agent registry for discovery prompt."""

    agents_registry = _load_agents_registry()

    return _extract_agent_fields(agents_registry)


def _extract_agent_fields(agents_list: list[Dict[str, str]]) -> List:
    """
    Extracts specific fields from a list of agent objects.

    Args:
        agents_list (list): List of agent dictionaries

    Returns:
        list: List of dictionaries containing only the specified fields:
              - id
              - name
              - description
              - avatar
              - agent_topic_type
              - category
    """
    extracted_agents = []

    for agent in agents_list:
        extracted_agent = {
            "id": agent.get("id"),
            "name": agent.get("name"),
            "description": agent.get("description"),
            "avatar": agent.get("avatar"),
            "agent_topic_type": agent.get("agent_topic_type"),
            "category": agent.get("category"),
        }
        extracted_agents.append(extracted_agent)

    return extracted_agents


def _load_agents_registry():
    """Load the specialized agents registry from JSON file."""
    try:
        registry_path = os.path.join(os.path.dirname(__file__), "agents_registry.json")

        with open(registry_path, "r") as file:
            registry_data = json.load(file)

        return registry_data

    except Exception as e:
        logger.error(f"Failed to load agents registry: {e}")
        return []
