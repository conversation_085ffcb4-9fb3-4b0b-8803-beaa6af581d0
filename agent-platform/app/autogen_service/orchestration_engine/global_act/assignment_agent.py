import logging
from typing import Optional

from autogen_agentchat.agents import Assistant<PERSON>gent
from autogen_core.models import ChatCompletionClient

from app.shared.config.base import Settings, get_settings

from ...model_factory import ModelFactory

logger = logging.getLogger(__name__)


class AssignmentAgent:
    """
    Agent responsible for formulating the final task assignment with clear
    requirements and context for the selected specialized agent.
    """

    def __init__(self):
        """Initialize the AssignmentAgent with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False

    async def initialize(self) -> None:
        """Initialize the agent with model client and context."""
        try:
            logger.info("Initializing Assignment Agent...")

            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "gpt-4.1",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for Assignment Agent")
                return False

            # Create the assistant agent
            self._agent = AssistantAgent(
                name="assignment_agent",
                description="Routes tasks to selected specialized agents and manages execution",
                model_client=self._model_client,
                system_message=self._get_enhanced_system_message(),
                tools=[],
                reflect_on_tool_use=False,
            )

            self._is_initialized = True
            logger.info("AssignmentAgent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize AssignmentAgent : {str(e)}")
            self._is_initialized = False
            return False

        except Exception as e:
            logger.error(f"Failed to initialize Assignment Agent: {e}")
            raise e

    def _get_enhanced_system_message(self) -> str:
        """Get the enhanced system message for the assignment agent."""
        return """You are an Assignment Agent specialized in routing tasks to selected specialized agents.

                Your task is to:
                1. Receive the selected agent information from the Selection Agent
                2. Create appropriate task assignments for the specialized agent
                3. Route the original query to the selected agent
                4. Handle the response and format it appropriately for the user

                You are the final step in the routing pipeline, ensuring tasks are properly assigned and executed."""

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(cls) -> Optional["AssignmentAgent"]:
        """
        Convenience method to create and initialize the Selector system in one call.

        Returns:
            AssignmentAgent: Initialized Selector system
        """
        agent = cls()
        if await agent.initialize():
            return agent
        else:
            logger.error("Failed to create and initialize DiscoveryMasterAgent")
            return None
