/**
 * Template Variable Utilities Tests
 */

import {
  detectTemplateVariables,
  validateTemplateVariables,
  hasTemplateVariables,
  extractVariableNames,
  highlightTemplateVariables,
  getVariableSuggestions,
  insertTemplateVariable,
  TEMPLATE_VARIABLE_PATTERNS,
  RESERVED_KEYWORDS,
} from '../templateVariables';

describe('Template Variable Utilities', () => {
  describe('detectTemplateVariables', () => {
    it('should detect {variable_name} syntax', () => {
      const result = detectTemplateVariables('Hello {user_name}, welcome!');
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        name: 'user_name',
        syntax: '{variable_name}',
        startPos: 6,
        endPos: 17,
        fullMatch: '{user_name}'
      });
    });

    it('should detect ${variable_name} syntax', () => {
      const result = detectTemplateVariables('Hello ${user_name}, welcome!');
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        name: 'user_name',
        syntax: '${variable_name}',
        startPos: 6,
        endPos: 18,
        fullMatch: '${user_name}'
      });
    });

    it('should detect multiple variables', () => {
      const result = detectTemplateVariables('Hello {first_name} {last_name}!');
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('first_name');
      expect(result[1].name).toBe('last_name');
    });

    it('should detect mixed syntax', () => {
      const result = detectTemplateVariables('Hello {first_name} and ${last_name}!');
      expect(result).toHaveLength(2);
      expect(result[0].syntax).toBe('{variable_name}');
      expect(result[1].syntax).toBe('${variable_name}');
    });

    it('should not detect escaped braces', () => {
      const result = detectTemplateVariables('This is \\{not_a_variable}');
      expect(result).toHaveLength(0);
    });

    it('should handle empty strings', () => {
      const result = detectTemplateVariables('');
      expect(result).toHaveLength(0);
    });

    it('should handle non-string input', () => {
      const result = detectTemplateVariables(123 as any);
      expect(result).toHaveLength(0);
    });
  });

  describe('validateTemplateVariables', () => {
    it('should validate correct template variables', () => {
      const result = validateTemplateVariables('Hello {user_name}!');
      expect(result.isValid).toBe(true);
      expect(result.variables).toHaveLength(1);
      expect(result.warnings).toHaveLength(0);
    });

    it('should detect empty variable names', () => {
      const result = validateTemplateVariables('Hello {}!');
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('Empty template variable name');
    });

    it('should detect reserved keywords', () => {
      const result = validateTemplateVariables('Hello {function}!');
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('reserved keyword');
    });

    it('should detect invalid characters', () => {
      const result = validateTemplateVariables('Hello {user-name!}!');
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('invalid characters');
    });

    it('should warn about mixed syntax', () => {
      const result = validateTemplateVariables('Hello {first_name} and ${last_name}!');
      expect(result.isValid).toBe(true);
      expect(result.warnings).toHaveLength(1);
      expect(result.warnings[0]).toContain('Mixed template variable syntax');
    });

    it('should warn about long variable names', () => {
      const longName = 'a'.repeat(51);
      const result = validateTemplateVariables(`Hello {${longName}}!`);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toHaveLength(1);
      expect(result.warnings[0]).toContain('very long');
    });
  });

  describe('hasTemplateVariables', () => {
    it('should return true for strings with template variables', () => {
      expect(hasTemplateVariables('Hello {user_name}!')).toBe(true);
      expect(hasTemplateVariables('Hello ${user_name}!')).toBe(true);
    });

    it('should return false for strings without template variables', () => {
      expect(hasTemplateVariables('Hello world!')).toBe(false);
      expect(hasTemplateVariables('This is \\{not_a_variable}')).toBe(false);
    });

    it('should handle non-string input', () => {
      expect(hasTemplateVariables(123 as any)).toBe(false);
      expect(hasTemplateVariables(null as any)).toBe(false);
    });
  });

  describe('extractVariableNames', () => {
    it('should extract unique variable names', () => {
      const result = extractVariableNames('Hello {user_name} and {user_name} again!');
      expect(result).toEqual(['user_name']);
    });

    it('should extract multiple unique names', () => {
      const result = extractVariableNames('Hello {first_name} {last_name}!');
      expect(result).toEqual(['first_name', 'last_name']);
    });
  });

  describe('highlightTemplateVariables', () => {
    it('should create text segments with highlighting info', () => {
      const result = highlightTemplateVariables('Hello {user_name}!');
      expect(result).toHaveLength(3);
      expect(result[0]).toEqual({ text: 'Hello ', isVariable: false });
      expect(result[1].text).toBe('{user_name}');
      expect(result[1].isVariable).toBe(true);
      expect(result[2]).toEqual({ text: '!', isVariable: false });
    });

    it('should handle strings without variables', () => {
      const result = highlightTemplateVariables('Hello world!');
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({ text: 'Hello world!', isVariable: false });
    });
  });

  describe('getVariableSuggestions', () => {
    const availableVars = ['user_name', 'user_email', 'first_name', 'last_name'];

    it('should return suggestions for partial matches', () => {
      const result = getVariableSuggestions('Hello {user', availableVars);
      expect(result).toEqual(['user_email', 'user_name']);
    });

    it('should prioritize prefix matches', () => {
      const result = getVariableSuggestions('Hello {first', availableVars);
      expect(result[0]).toBe('first_name');
    });

    it('should return empty array when not typing a variable', () => {
      const result = getVariableSuggestions('Hello world', availableVars);
      expect(result).toEqual([]);
    });

    it('should limit suggestions to 10 items', () => {
      const manyVars = Array.from({ length: 20 }, (_, i) => `var_${i}`);
      const result = getVariableSuggestions('Hello {var', manyVars);
      expect(result.length).toBeLessThanOrEqual(10);
    });
  });

  describe('insertTemplateVariable', () => {
    it('should insert variable at cursor position', () => {
      const result = insertTemplateVariable('Hello !', 'user_name', 6, false);
      expect(result.newValue).toBe('Hello {user_name}!');
      expect(result.newCursorPosition).toBe(17);
    });

    it('should insert with dollar syntax when requested', () => {
      const result = insertTemplateVariable('Hello !', 'user_name', 6, true);
      expect(result.newValue).toBe('Hello ${user_name}!');
      expect(result.newCursorPosition).toBe(18);
    });

    it('should handle insertion at beginning', () => {
      const result = insertTemplateVariable('world!', 'hello', 0, false);
      expect(result.newValue).toBe('{hello}world!');
    });

    it('should handle insertion at end', () => {
      const result = insertTemplateVariable('Hello ', 'world', 6, false);
      expect(result.newValue).toBe('Hello {world}');
    });
  });

  describe('TEMPLATE_VARIABLE_PATTERNS', () => {
    it('should have correct regex patterns', () => {
      expect(TEMPLATE_VARIABLE_PATTERNS.NEW_SYNTAX.test('{variable}')).toBe(true);
      expect(TEMPLATE_VARIABLE_PATTERNS.NEW_SYNTAX.test('${variable}')).toBe(false);
      expect(TEMPLATE_VARIABLE_PATTERNS.EXISTING_SYNTAX.test('${variable}')).toBe(true);
      expect(TEMPLATE_VARIABLE_PATTERNS.EXISTING_SYNTAX.test('{variable}')).toBe(false);
    });
  });

  describe('RESERVED_KEYWORDS', () => {
    it('should contain common reserved words', () => {
      expect(RESERVED_KEYWORDS).toContain('function');
      expect(RESERVED_KEYWORDS).toContain('class');
      expect(RESERVED_KEYWORDS).toContain('const');
      expect(RESERVED_KEYWORDS).toContain('undefined');
    });
  });
});
