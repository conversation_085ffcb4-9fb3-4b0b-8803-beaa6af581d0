"use client";

import React, { useState, useEffect } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface JsonEditorWithPrettifyProps {
  initialJsonString?: string;
  onJsonChange: (jsonString: string) => void;
  height?: string;
}

export const JsonEditorWithPrettify: React.FC<JsonEditorWithPrettifyProps> = ({
  initialJsonString = "",
  onJsonChange,
  height = "200px",
}) => {
  const [jsonStr, setJsonStr] = useState(initialJsonString);
  const [error, setError] = useState<string | null>(null);

  const validateJson = (str: string) => {
    if (!str.trim()) {
      // Consider empty or whitespace-only string as valid (or at least not an error for parsing)
      setError(null);
      return true;
    }
    try {
      JSON.parse(str);
      setError(null);
      return true;
    } catch (e) {
      setError("Invalid JSON format");
      return false;
    }
  };

  useEffect(() => {
    setJsonStr(initialJsonString);
    validateJson(initialJsonString); // Validate initial string
  }, [initialJsonString]);

  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newStr = event.target.value;
    setJsonStr(newStr);
    onJsonChange(newStr);
    validateJson(newStr);
  };

  const handlePrettify = () => {
    try {
      const parsedJson = JSON.parse(jsonStr);
      const prettyJson = JSON.stringify(parsedJson, null, 2);
      setJsonStr(prettyJson);
      onJsonChange(prettyJson);
      setError(null);
      toast.success("JSON prettified!");
    } catch (e) {
      setError("Invalid JSON: Cannot prettify.");
      toast.error("Invalid JSON: Cannot prettify.");
    }
  };

  return (
    <div className="space-y-2">
      <Textarea
        value={jsonStr}
        onChange={handleInputChange}
        placeholder="Paste or type JSON here..."
        className={`font-mono text-xs min-h-[${height}]`}
        style={{ height: height }}
      />
      {error && <p className="text-destructive text-xs">{error}</p>}
      <Button onClick={handlePrettify} variant="outline" size="sm">
        Prettify JSON
      </Button>
    </div>
  );
};
