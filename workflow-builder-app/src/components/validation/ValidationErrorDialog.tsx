import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ValidationErrors } from "./ValidationErrors";
import { MissingFields } from "./MissingFields";
import { ValidationResult } from "@/lib/validation/types";
import { AlertCircle } from "lucide-react";

interface ValidationErrorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  validationResult: ValidationResult;
  title?: string;
  description?: string;
  onNavigateToNode?: (nodeId: string) => void;
}

/**
 * Dialog component for displaying validation errors in a user-friendly format
 * Uses the enhanced ValidationErrors component to show node labels and better formatting
 */
export function ValidationErrorDialog({
  open,
  onOpenChange,
  validationResult,
  title = "Validation Failed",
  description = "Please fix the following issues before proceeding:",
  onNavigateToNode,
}: ValidationErrorDialogProps) {
  const { errors, warnings, infos, missingFields } = validationResult;
  const hasErrors = errors.length > 0;
  const hasWarnings = warnings.length > 0;
  const hasInfos = infos.length > 0;
  const hasMissingFields = missingFields && missingFields.length > 0;

  // Count total issues
  const totalIssues = errors.length + warnings.length + (missingFields?.length || 0);

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-destructive" />
            {title}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-left">
            {description}
            {totalIssues > 0 && (
              <span className="block mt-1 text-sm text-muted-foreground">
                Found {totalIssues} issue{totalIssues > 1 ? "s" : ""} that need{totalIssues === 1 ? "s" : ""} attention.
              </span>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>

        {/* Scrollable content area */}
        <div className="flex-1 overflow-y-auto pr-2 -mr-2">
          {(hasErrors || hasWarnings || hasInfos) && (
            <ValidationErrors
              errors={errors}
              warnings={warnings}
              infos={infos}
              className="mb-4"
            />
          )}

          {hasMissingFields && (
            <MissingFields
              missingFields={missingFields}
              onNavigateToNode={onNavigateToNode}
              className="mb-4"
            />
          )}

          {!hasErrors && !hasWarnings && !hasInfos && !hasMissingFields && (
            <div className="text-center py-8 text-muted-foreground">
              <AlertCircle className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No specific validation issues found.</p>
              <p className="text-sm">Please check your workflow configuration.</p>
            </div>
          )}
        </div>

        <AlertDialogFooter>
          <AlertDialogAction onClick={() => onOpenChange(false)}>
            OK
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

/**
 * Hook to manage validation error dialog state
 */
export function useValidationErrorDialog() {
  const [isOpen, setIsOpen] = React.useState(false);
  const [validationResult, setValidationResult] = React.useState<ValidationResult | null>(null);

  const showValidationErrors = React.useCallback((result: ValidationResult) => {
    setValidationResult(result);
    setIsOpen(true);
  }, []);

  const hideValidationErrors = React.useCallback(() => {
    setIsOpen(false);
    // Don't clear validationResult immediately to allow for smooth closing animation
    setTimeout(() => setValidationResult(null), 200);
  }, []);

  return {
    isOpen,
    validationResult,
    showValidationErrors,
    hideValidationErrors,
  };
}
