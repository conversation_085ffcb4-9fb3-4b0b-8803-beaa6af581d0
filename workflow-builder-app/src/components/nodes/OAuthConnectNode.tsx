"use client";

import React, { memo, useState } from "react";
import { NodeProps } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useUserStore } from "@/store/userStore";
import { API_BASE_URL } from "@/lib/api";
import { openOAuthInNewTab } from "@/lib/oauthUtils";
import Image from "next/image";
import { useTheme } from "next-themes";

interface OAuthConnectNodeProps extends NodeProps<WorkflowNodeData> {
  onConnectionSuccess: (nodeId: string) => void;
}

const OAuthConnectNode = memo(({ data, id, onConnectionSuccess }: OAuthConnectNodeProps) => {
  const { label, definition } = data;
  const { user } = useUserStore();
  const { theme } = useTheme();
  const [isConnecting, setIsConnecting] = useState(false);

  // Theme-aware colors
  const isDark = theme === "dark";
  const colors = {
    background: isDark ? "rgba(245, 158, 11, 0.05)" : "rgba(245, 158, 11, 0.08)",
    border: "#F59E0B",
    iconBackground: isDark ? "#2E2E2E" : "#F5F5F5",
    iconFallback: isDark ? "#FFFFFF" : "#6B7280",
    titleColor: isDark ? "#FFFFFF" : "#1F2937",
    descriptionColor: isDark ? "#FFFFFF" : "#4B5563",
    buttonBackground: isDark ? "#1E1E1E" : "#FFFFFF",
    buttonBorder: isDark ? "#4B4B4D" : "#D1D5DB",
    buttonHover: isDark ? "rgba(55, 65, 81, 1)" : "rgba(243, 244, 246, 1)",
    buttonText: isDark ? "#FFFFFF" : "#374151",
    loaderColor: isDark ? "white" : "#6B7280"
  };

  const oauthDetails = definition?.oauth_details;

  if (!oauthDetails) {
    return null;
  }

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      // Get current user ID from auth context
      const userId = user?.id || user?.email || "anonymous_user";

      // Build OAuth URL with parameters
      const queryParams = new URLSearchParams({
        provider: oauthDetails.provider,
        tool_name: oauthDetails.tool_name,
        user_id: userId,
        redirect_url: window.location.origin + "/oauth/callback",
      });

      if (oauthDetails.scopes) {
        queryParams.append("scopes", oauthDetails.scopes.join(","));
      }

      const oauthUrl = `${API_BASE_URL}/oauth/authorize?${queryParams.toString()}`;

      console.log('Opening OAuth URL:', oauthUrl);
      console.log('OAuth details:', oauthDetails);

      // Open OAuth in new tab
      openOAuthInNewTab(
        oauthUrl,
        () => {
          // Success callback - let WorkflowCanvas handle verification and transition
          console.log('OAuth success callback triggered for node:', id);
          toast.success(`Successfully connected to ${oauthDetails.provider}!`);

          // Trigger the transition to normal functional node (with verification)
          onConnectionSuccess(id || "");

          setIsConnecting(false);
        },
        (error) => {
          // Error callback
          console.error('OAuth error callback triggered:', error);
          toast.error(`OAuth connection failed: ${error}`);
          setIsConnecting(false);
        }
      );
    } catch (error) {
      console.error("Error initiating OAuth flow:", error);
      toast.error("Failed to initiate OAuth connection");
      setIsConnecting(false);
    }
  };

  // Get the logo for the service
  const serviceLogo = definition?.logo;
  const providerName = oauthDetails.provider;
  const displayName = label || definition?.display_name || `${providerName} Service`;

  return (
    <div
      className="workflow-node relative overflow-visible transition-all duration-200"
      style={{
        width: "337px",
        height: "142px",
        zIndex: 10,
      }}
    >

      {/* Main Container - Exact design match */}
      <div
        className="flex flex-col items-center"
        style={{
          boxSizing: "border-box",
          padding: "12px 3px 3px",
          gap: "12px",
          width: "337px",
          height: "142px",
          background: colors.background,
          border: `2px solid ${colors.border}`,
          borderRadius: "8px",
        }}
      >
        {/* Frame 21 - Content Container */}
        <div
          className="flex flex-col items-start"
          style={{
            padding: "0px",
            gap: "12px",
            width: "303px",
            height: "108px",
            flex: "none",
            order: 0,
            flexGrow: 0,
          }}
        >
          {/* Group 16 - Header Section */}
          <div
            className="relative"
            style={{
              width: "303px",
              height: "74px",
              flex: "none",
              order: 0,
              flexGrow: 0,
            }}
          >
            {/* Frame 8 - Icon and Title Row */}
            <div
              className="flex flex-row items-center absolute"
              style={{
                padding: "0px",
                gap: "59px",
                width: "100%",
                height: "34px",
                left: "0px",
                top: "0px",
              }}
            >
              {/* Group 18 - Icon and Title */}
              <div
                className="relative"
                style={{
                  height: "34px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                {/* Service Icon Container - Overlay+Shadow */}
                <div
                  className="absolute"
                  style={{
                    width: "34px",
                    height: "34px",
                    left: "0px",
                    top: "0px",
                    background: colors.iconBackground,
                    borderRadius: "4px",
                  }}
                >
                  {serviceLogo ? (
                    <div
                      className="absolute flex items-center justify-center"
                      style={{
                        left: "calc(50% - 10px)",
                        top: "calc(50% - 10px)",
                        width: "20px",
                        height: "20px",
                      }}
                    >
                      <Image
                        src={serviceLogo}
                        alt={`${providerName} logo`}
                        width={20}
                        height={20}
                        className="object-cover w-full h-full"
                        style={{
                          maxWidth: "20px",
                          maxHeight: "20px",
                        }}
                      />
                    </div>
                  ) : (
                    <div
                      className="absolute"
                      style={{
                        width: "13.87px",
                        height: "14px",
                        left: "calc(50% - 13.87px/2 + 0px)",
                        top: "calc(50% - 14px/2)",
                        background: colors.iconFallback,
                      }}
                    />
                  )}
                </div>

                {/* Service Title */}
                <div
                  className="absolute"
                  style={{
                    width: "250px",
                    height: "22px",
                    left: "45px",
                    top: "6px",
                    fontFamily: "'Satoshi', 'Geist', system-ui, sans-serif",
                    fontStyle: "normal",
                    fontWeight: 700,
                    fontSize: "16px",
                    lineHeight: "140%",
                    letterSpacing: "-0.02em",
                    color: colors.titleColor,
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {displayName}
                </div>
              </div>
            </div>

            {/* Description Text */}
            <div
              className="absolute"
              style={{
                width: "303px",
                height: "32px",
                left: "0px",
                top: "42px",
                fontFamily: "'Satoshi', 'Geist', system-ui, sans-serif",
                fontStyle: "normal",
                fontWeight: 400,
                fontSize: "10px",
                lineHeight: "16px",
                color: colors.descriptionColor,
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
              }}
            >
              {definition?.description || "OAuth connection required for this service."}
            </div>
          </div>

          {/* Connect Button - Frame 20 */}
          <button
            onClick={handleConnect}
            disabled={isConnecting}
            className="flex flex-row items-center transition-colors duration-200"
            style={{
              boxSizing: "border-box",
              padding: "5px",
              gap: "4px",
              width: "143px",
              height: "22px",
              background: colors.buttonBackground,
              border: `1px solid ${colors.buttonBorder}`,
              borderRadius: "4px",
              flex: "none",
              order: 1,
              flexGrow: 0,
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = colors.buttonHover;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = colors.buttonBackground;
            }}
          >
            {/* Icon Container - Group 19 */}
            <div
              style={{
                width: "12px",
                height: "12px",
                flex: "none",
                order: 0,
                flexGrow: 0,
              }}
            >
              {isConnecting ? (
                <Loader2 className="h-3 w-3 animate-spin" style={{ color: colors.loaderColor }} />
              ) : serviceLogo ? (
                <div className="flex items-center justify-center w-3 h-3">
                  <Image
                    src={serviceLogo}
                    alt={`${providerName} logo`}
                    width={12}
                    height={12}
                    className="object-cover w-full h-full"
                    style={{
                      maxWidth: "12px",
                      maxHeight: "12px",
                    }}
                  />
                </div>
              ) : (
                <div
                  style={{
                    width: "12px",
                    height: "12px",
                    background: colors.iconFallback,
                    borderRadius: "2px",
                  }}
                />
              )}
            </div>

            {/* Button Text */}
            <span
              style={{
                width: "117px",
                height: "12px",
                fontFamily: "'Satoshi', 'Geist', system-ui, sans-serif",
                fontStyle: "normal",
                fontWeight: 400,
                fontSize: "9px",
                lineHeight: "12px",
                letterSpacing: "0.15px",
                color: colors.buttonText,
                flex: "none",
                order: 1,
                flexGrow: 0,
              }}
            >
              {isConnecting ? "Connecting..." : `Sign in with ${providerName.toLowerCase()}`}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
});

OAuthConnectNode.displayName = "OAuthConnectNode";

export default OAuthConnectNode;
