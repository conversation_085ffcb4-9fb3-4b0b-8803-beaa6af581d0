import React, { memo, useMemo } from "react";
import { <PERSON><PERSON>, Position, NodeProps, useReactFlow, Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { useAgenticAIToolConnections, useIsConnectedAsTool } from "@/hooks/useToolConnections";
import { generateToolStyleClasses, isToolHandle } from "@/utils/toolConnectionUtils";
import "@/styles/components/tool-connections.css";
import Image from "next/image";
import { OAuthConnectionStatus } from "./OAuthConnectionStatus";
// Import icons based on node type
import {
  LucideIcon,
  FileText,
  Database,
  Code,
  Workflow,
  Cog,
  Cpu,
  ArrowRightLeft,
  Log<PERSON>n,
  <PERSON>g<PERSON>ut,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>r,
  Layers,
  Co<PERSON>,
  Trash2,
} from "lucide-react";
import { nanoid } from "nanoid";

// Node Logo Component for MCP and Agent nodes
interface NodeLogoProps {
  data: WorkflowNodeData;
  fallbackIcon: LucideIcon;
}

const NodeLogo: React.FC<NodeLogoProps> = ({ data, fallbackIcon: FallbackIcon }) => {
  // Check if this is an agent node
  const isAgentNode = data.type === "agent" || data.originalType?.startsWith("agent-");
  const agentInfo = data.definition?.agent_info;

  // Check if this is an MCP node
  const isMCPNode = data.type === "mcp" || data.originalType?.includes("MCP_") || data.definition?.type === "MCP";
  const mcpLogo = data.definition?.logo;

  // Agent logo handling
  if (isAgentNode && agentInfo?.avatar) {
    return (
      <div className="relative h-5 w-5 flex-shrink-0">
        <Image
          src={agentInfo.avatar}
          alt={`${agentInfo.name} avatar`}
          width={20}
          height={20}
          className="rounded-full object-cover shadow-sm ring-1 ring-black/5 dark:ring-white/10"
          onError={(e) => {
            // Fallback to User icon
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = `
                <div class="h-5 w-5 rounded-full bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-800/30 dark:to-pink-800/30 flex items-center justify-center">
                  <svg class="h-3 w-3 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
              `;
            }
          }}
        />
      </div>
    );
  }

  // MCP logo handling
  if (isMCPNode && mcpLogo) {
    return (
      <div className="relative h-5 w-5 flex-shrink-0 overflow-hidden rounded-sm shadow-sm ring-1 ring-black/5 dark:ring-white/10">
        <Image
          src={mcpLogo}
          alt="MCP logo"
          width={20}
          height={20}
          className="mcp-logo-node h-full w-full object-cover"
          onError={(e) => {
            // Fallback to Layers icon
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = `
                <div class="h-5 w-5 rounded-sm bg-white dark:bg-gray-800 flex items-center justify-center">
                  <svg class="h-3 w-3 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                  </svg>
                </div>
              `;
            }
          }}
        />
      </div>
    );
  }

  // Agent fallback (no avatar)
  if (isAgentNode) {
    return (
        <User className="text-primary h-5 w-5" />
    );
  }

  // MCP fallback (no logo)
  if (isMCPNode) {
    return (
        <Layers className="text-primary h-5 w-5" />
    );
  }

  // Default fallback icon
  return <FallbackIcon className="text-primary h-5 w-5" />;
};

// Helper function to get appropriate icon based on node category or type
const getNodeIcon = (category: string, type: string, originalType?: string): LucideIcon => {
  // Special case for StartNode
  if (originalType === "StartNode") {
    return LogIn;
  }

  // Map categories to icons
  switch (category?.toLowerCase()) {
    case "io":
      return type.includes("input") ? LogIn : LogOut;
    case "data":
      return Database;
    case "processing":
      return Cpu;
    case "api":
      return ArrowRightLeft;
    case "control flow":
      return Workflow;
    case "text":
      return FileText;
    case "code":
      return Code;
    default:
      return Cog; // Default icon
  }
};

// Memoize the component for performance
const WorkflowNode = memo(({ data, isConnectable, selected, id }: NodeProps<WorkflowNodeData>) => {
  const { label, definition, type, originalType } = data;

  // Get React Flow instance for updating node data
  const { setNodes, addNodes, getNodes, setEdges } = useReactFlow();

  // Get React Flow instance to access edges and nodes with error handling
  let edges: Edge[] = [];
  let nodes: Node<WorkflowNodeData>[] = [];
  let toolConnectionState: any = {
    hasConnectedTools: false,
    connectedToolCount: 0,
    toolConnections: [],
    availableToolSlots: [],
  };
  let isConnectedAsTool = false;
  let toolStyleClasses = {
    agenticAIWithTools: "",
    connectedAsTool: "",
    toolCountBadge: "",
  };

  try {
    const { getEdges, getNodes } = useReactFlow();
    edges = getEdges();
    nodes = getNodes();

    // Tool connection hooks
    toolConnectionState = useAgenticAIToolConnections(id || "", edges, nodes);
    isConnectedAsTool = useIsConnectedAsTool(id || "", edges);

    // Generate tool style classes
    toolStyleClasses = generateToolStyleClasses(
      toolConnectionState.hasConnectedTools,
      toolConnectionState.connectedToolCount,
      isConnectedAsTool
    );
  } catch (error) {
    console.warn("[WorkflowNode] Error accessing React Flow context or tool connections:", error);
    // Continue with default values
  }

  // Basic validation
  if (!definition) {
    return (
      <div className="bg-destructive/20 text-destructive rounded border p-2 text-xs font-medium">
        Missing Definition!
      </div>
    );
  }

  // Generate single tool handle for AgenticAI components
  const dynamicToolHandles = useMemo(() => {
    if (originalType !== "AgenticAI") return [];

    // Find the tools HandleInput (new simplified approach)
    const toolsInput = (definition.inputs || []).find(
      inp => inp.is_handle && inp.name === "tools"
    );

    if (!toolsInput) return [];

    // Use the existing tools handle from the component definition
    const toolHandles = [{
      name: "tools",
      display_name: "Tools",
      input_types: ["Any"],
      is_handle: true,
      input_type: "handle",
      info: "Connect workflow components to use as agent tools. Multiple tools can connect to this single handle.",
      required: false,
      is_list: false,
      real_time_refresh: false,
      advanced: false,
      value: null,
      options: null,
      visibility_rules: null,
      visibility_logic: "OR" as const,
      requirement_rules: null,
      requirement_logic: "OR" as const
    }];


    return toolHandles;
  }, [originalType, definition.inputs, id]);

  // Filter inputs to get ONLY handles and apply visibility rules
  const handleInputs = (definition.inputs || []).filter((inp) => {
    // Only include handle inputs
    if (!inp.is_handle) return false;
    

    // PRIORITY: Handle MergeDataComponent output_key input handles BEFORE fallback logic
    if (
      (type === "MergeDataComponent" || originalType === "MergeDataComponent") &&
      inp.name.startsWith("output_key_")
    ) {

      // Only show output_key input handles when merge_strategy is "Structured Compose"
      if (data.config?.merge_strategy !== "Structured Compose") {
        return false;
      }

      // Extract the index from the output key name (e.g., "output_key_3" -> 3)
      const match = inp.name.match(/output_key_(\d+)/);
      if (match && match[1]) {
        const keyIndex = parseInt(match[1], 10);
        const numAdditionalInputs = parseInt(data.config?.num_additional_inputs || "0", 10);

        // Show output_key_1 (for main input) and output_key_2 through output_key_(1+numAdditionalInputs)
        const totalInputs = 1 + numAdditionalInputs; // main_input + additional inputs
        const shouldShow = keyIndex <= totalInputs;

        return shouldShow;
      }

      // If we can't parse the key index, hide it
      return false;
    }

    // If no visibility rules, always show
    if (!inp.visibility_rules || inp.visibility_rules.length === 0) return true;

    // Special handling for dynamic inputs
    if (
      inp.input_type === "dynamic_handle" ||
      (type === "DynamicCombineTextComponent" &&
        inp.name.startsWith("input_") &&
        inp.name.endsWith("_handle")) ||
      (originalType === "ConditionalNode" &&
        (inp.name === "primary" ||
          (inp.name.startsWith("condition_") && inp.name.match(/^condition_\d+$/))))
    ) {
      // For DynamicHandleInput (other than workflow_components)
      if (inp.input_type === "dynamic_handle") {
        const numHandles = data.config?.num_handles || inp.default_handles || 2;
        const baseNameMatch = inp.name.match(new RegExp(`${inp.base_name}_(\d+)`));
        if (baseNameMatch && baseNameMatch[1]) {
          const handleIndex = parseInt(baseNameMatch[1], 10);
          return handleIndex <= numHandles;
        }
        return true; // Show the base handle
      }

      // For DynamicCombineTextComponent (legacy support)
      if (
        type === "DynamicCombineTextComponent" &&
        inp.name.startsWith("input_") &&
        inp.name.endsWith("_handle")
      ) {
        // Extract the index from the handle name (e.g., "input_3_handle" -> 3)
        const match = inp.name.match(/input_(\d+)_handle/);
        if (match && match[1]) {
          const inputIndex = parseInt(match[1], 10);
          const numAdditionalInputs = parseInt(data.config?.num_additional_inputs || "0", 10);

          // FIXED: Show handles for main input (index 1) plus additional inputs
          // Total inputs = 1 (main) + numAdditionalInputs
          // So for numAdditionalInputs=1, show input_1_handle and input_2_handle
          // But we need to ensure proper indexing: when numAdditionalInputs=1, show indices 1 and 2
          const totalInputs = 1 + numAdditionalInputs;
          const shouldShow = inputIndex <= totalInputs;
          
          return shouldShow;
        }
      }


      // UPDATED: For ConditionalNode input handles (new dual-purpose naming)
      if (originalType === "ConditionalNode") {
        // Handle "primary" input (condition 1)
        if (inp.name === "primary") {
          return true; // Always show primary input
        }

        // Handle "condition_X" inputs (conditions 2+)
        if (inp.name.startsWith("condition_") && inp.name.match(/^condition_\d+$/)) {
          const match = inp.name.match(/condition_(\d+)/);
          if (match && match[1]) {
            const conditionIndex = parseInt(match[1], 10);
            const numAdditionalConditions = parseInt(
              data.config?.num_additional_conditions || "0",
              10,
            );
            const totalConditions = 1 + numAdditionalConditions; // Base 1 + additional

            // Show the handle if the condition index is within the total conditions
            return conditionIndex <= totalConditions;
          }
        }
      }
    }

    // For all components (including ConditionalNode non-handle inputs), check visibility rules
    return inp.visibility_rules.some((rule) => {
      // Get the target field value from config
      const targetValue = data.config?.[rule.field_name];

      // Simple equality check - show if the field_name has the specified field_value
      return targetValue === rule.field_value;
    });
  }); // No need to concat dynamic tool handles as they're now in the component definition
  
  // Sort handles to ensure proper order (input_1_handle, input_2_handle, etc.)
  if (type === "DynamicCombineTextComponent" || originalType === "DynamicCombineTextComponent" ||
      type === "MergeDataComponent" || originalType === "MergeDataComponent") {
    handleInputs.sort((a, b) => {
      // Handle input_X_handle pattern
      const aInputMatch = a.name.match(/input_(\d+)_handle/);
      const bInputMatch = b.name.match(/input_(\d+)_handle/);
      
      if (aInputMatch && bInputMatch) {
        const aIndex = parseInt(aInputMatch[1], 10);
        const bIndex = parseInt(bInputMatch[1], 10);
        return aIndex - bIndex;
      }
      
      // Handle output_key_X pattern for MergeDataComponent
      const aOutputMatch = a.name.match(/output_key_(\d+)/);
      const bOutputMatch = b.name.match(/output_key_(\d+)/);
      
      if (aOutputMatch && bOutputMatch) {
        const aIndex = parseInt(aOutputMatch[1], 10);
        const bIndex = parseInt(bOutputMatch[1], 10);
        return aIndex - bIndex;
      }
      
      return a.name.localeCompare(b.name);
    });
  }
  

  // Outputs are always handles in this model
  // Special handling for ConditionalNode dynamic outputs
  let handleOutputs = definition.outputs || [];

  if (originalType === "ConditionalNode") {
    const numAdditionalConditions = parseInt(data.config?.num_additional_conditions || "0", 10);
    const totalConditions = 1 + numAdditionalConditions; // ✅ FIXED: Base 1 + additional (matches backend BASE_CONDITIONS = 1)

    // Generate dynamic outputs for conditions
    const dynamicOutputs = [];
    for (let i = 1; i <= totalConditions; i++) {
      dynamicOutputs.push({
        name: `condition_${i}`, // ✅ FIXED: Remove "_output" suffix to match backend naming
        display_name: `Condition ${i}`,
        output_type: "Any",
        info: `Outputs data when condition ${i} matches`,
      });
    }

    // Add default output
    dynamicOutputs.push({
      name: "default", // ✅ FIXED: Remove "_output" suffix to match backend naming
      display_name: "Default",
      output_type: "Any",
      info: "Outputs data when no conditions match",
    });

    handleOutputs = dynamicOutputs;
  }


  // Get the appropriate icon
  const NodeIcon = getNodeIcon(definition.category, type, originalType);

  // Determine if this is a StartNode
  const isStartNode = originalType === "StartNode";

  // Check if this node requires approval
  const requiresApproval = data.definition?.requires_approval === true;

  // Calculate dynamic height based on number of handles
  const maxHandles = Math.max(handleInputs.length, handleOutputs.length);
  const baseHeight = 90;
  
  // Dynamic handle spacing: use larger spacing for fewer handles to ensure proper separation
  const getHandleSpacing = (numHandles: number) => {
    if (numHandles <= 2) return 25; // Ensure proper separation for 2 handles (main + 1 additional)
    if (numHandles <= 3) return 20; // Medium spacing for 3 handles
    return 15; // Tighter spacing for 4+ handles
  };
  
  const handleSpacing = getHandleSpacing(maxHandles);
  const dynamicHeight = maxHandles > 1 ? baseHeight + ((maxHandles - 1) * handleSpacing) : baseHeight;

  const [hovered, setHovered] = React.useState(false);

  // Duplicate node handler
  const handleDuplicateNode = (e: React.MouseEvent) => {
    e.stopPropagation();
    const nodes = getNodes();
    const nodeToCopy = nodes.find((n) => n.id === id);
    if (!nodeToCopy) return;
    const newId = nanoid();
    const offset = 40;
    const newNode = {
      ...nodeToCopy,
      id: newId,
      position: {
        x: nodeToCopy.position.x + offset,
        y: nodeToCopy.position.y + offset,
      },
      data: { ...nodeToCopy.data, label: `${nodeToCopy.data.label || "Node"} (Copy)` },
      selected: false,
    };
    setNodes((nds) => [...nds, newNode]);
  };

  // Delete node handler
  const handleDeleteNode = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Remove both the node and all connected edges (same as inspector delete)
    setNodes((nds) => nds.filter((n) => n.id !== id));
    setEdges((eds) => eds.filter((e) => e.source !== id && e.target !== id));
  };

  return (
    <TooltipProvider delayDuration={150}>
      <div
        className="relative"
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
      >
        {/* Hover overlay for duplicate/delete */}
        {hovered && !isStartNode && (
          <div
            className="absolute left-1/2 top-0 z-50 flex -translate-x-1/2 -translate-y-1/2 gap-2 rounded-lg bg-white/60 dark:bg-zinc-900/60 backdrop-blur-md shadow-xl border border-white/30 dark:border-zinc-700/40 px-2 py-0.5 animate-fade-in"
            style={{ pointerEvents: 'auto', transition: 'box-shadow 0.2s, background 0.2s' }}
          >
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={handleDuplicateNode}
                    className="group flex items-center justify-center rounded-full p-1 text-primary hover:bg-primary/10 hover:text-primary-700 focus:outline-none transition-all duration-150"
                    title="Duplicate Node"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>Duplicate</TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={handleDeleteNode}
                    className="group flex items-center justify-center rounded-full p-1 text-red-500 hover:bg-red-100 hover:text-red-700 focus:outline-none transition-all duration-150"
                    title="Delete Node"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>Delete</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}
        <Card
          data-testid={originalType === "AgenticAI" ? "agentic-ai-node" : "workflow-node"}
          className={`workflow-node relative w-52 overflow-visible transition-all duration-200 ${selected ? "ring-primary scale-105 shadow-lg ring-2" : "shadow-md hover:shadow-lg"} ${isStartNode ? "border-primary bg-primary/5 border-2" : ""} ${requiresApproval ? "border-2 border-[#3F72AF]/50" : ""} ${toolStyleClasses.agenticAIWithTools} ${toolStyleClasses.connectedAsTool}`}
          style={{
            minHeight: `${dynamicHeight}px`,
            height: "auto", // Allow the card to grow based on content
            zIndex: 10, // Ensure node is above connections but below tooltips
            gap: 0,
          }}
        >
          {/* Tool count badge for AgenticAI nodes with connected tools */}
          {originalType === "AgenticAI" && toolConnectionState.hasConnectedTools && (
            <div
              data-testid="tool-count-badge"
              className={toolStyleClasses.toolCountBadge}
              aria-label={`${toolConnectionState.connectedToolCount} tools connected`}
            >
              {toolConnectionState.connectedToolCount}
            </div>
          )}
          <CardHeader className="flex flex-row items-center gap-1.5 space-y-0 px-2 py-2">
            <div className="bg-primary/10 rounded-md p-1">
              <NodeLogo data={data} fallbackIcon={NodeIcon} />
            </div>
            <div className="flex-1 overflow-hidden">
              <CardTitle className="truncate text-xs leading-tight font-medium">
                {label}
                {requiresApproval && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="ml-1 inline-flex">
                        <ShieldCheck className="h-3 w-3 text-[#3F72AF]" />
                      </span>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="p-2 text-xs">
                      <div className="font-medium">Requires Approval</div>
                      <div className="text-muted-foreground text-[10px]">
                        This node requires approval before execution
                      </div>
                    </TooltipContent>
                  </Tooltip>
                )}
              </CardTitle>
              <CardDescription className="truncate text-[9px] leading-tight">
                {definition.display_name}
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent className="flex flex-col gap-2 py-2 text-xs">
            {/* <div className="flex items-center justify-between">
            <Badge variant="outline" className="bg-muted/50 h-5 px-1.5 py-0 text-[9px]">
              {definition.category}
            </Badge>

          </div> */}

            {/* Approval badge */}
            {requiresApproval && (
              <Badge
                variant="warning"
                className="flex h-5 w-fit items-center gap-1 px-1.5 py-0 text-[9px]"
              >
                <ShieldCheck className="h-3 w-3" />
                Requires Approval
              </Badge>
            )}

            {/* OAuth Connection Status */}
            {definition.oauth_details && (
              <OAuthConnectionStatus
                oauthDetails={definition.oauth_details}
                toolName={definition.display_name}
                nodeId={id || ""}
                initialConnectionState={data.oauthConnectionState}
                onConnectionChange={(isConnected) => {
                  // Update the node's OAuth connection status in both definition and node data
                  if (definition.oauth_details) {
                    definition.oauth_details.is_connected = isConnected;
                  }

                  // Update the node data with OAuth connection state
                  setNodes((nodes) =>
                    nodes.map((node) =>
                      node.id === id
                        ? {
                            ...node,
                            data: {
                              ...node.data,
                              oauthConnectionState: {
                                isConnected,
                                provider: definition.oauth_details?.provider,
                                connectedAt: isConnected ? new Date().toISOString() : undefined,
                              },
                            },
                          }
                        : node
                    )
                  );
                }}
                className="mb-2 w-full"
              />
            )}

            {/* Input and Output Handles side by side */}
            <div className="mt-2 flex gap-[10px]">
              {/* Input Handles (Left) with Tooltips */}
              <div className="flex-1">
                {handleInputs.map((input, index) => {
                  // Calculate proper vertical positioning for multiple handles
                  const currentHandleSpacing = getHandleSpacing(handleInputs.length);
                  const topOffset = index * currentHandleSpacing;
                  
                  
                  return (
                    <div
                      key={`input-${input.name}`}
                      className="relative flex items-center"
                      style={{
                        marginBottom: currentHandleSpacing,
                        minHeight: `${currentHandleSpacing}px`, // Ensure minimum height for proper spacing
                        position: 'relative'
                      }}
                    >
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="group flex items-center relative w-full">
                            <Handle
                              type="target"
                              position={Position.Left}
                              id={input.name}
                              data-testid={`handle-${input.name}`}
                              data-tool-handle={isToolHandle(input.name)}
                              data-handle-index={index}
                              className={`${
                                isToolHandle(input.name)
                                  ? "tool-handle"
                                  : "regular-handle !bg-primary hover:!bg-primary/80"
                              }`}
                              style={{
                                width: "10px",
                                height: "10px",
                                borderRadius: "5px",
                                border: "2px solid var(--background)",
                                left: "-5px",
                                position: "absolute",
                                top: "50%", // Center vertically within the container
                                transform: "translateY(-50%)", // Center the handle
                                zIndex: 50, // Ensure handle is above other elements
                              }}
                              isConnectable={isConnectable}
                              tabIndex={isToolHandle(input.name) ? 0 : -1}
                            />
                            {/* Input label */}
                            <div
                              className="overflow-hidden rounded-[5px] border border-[#3F3F46] px-[10px] py-[3px] text-[9px] font-medium text-white"
                              style={{
                                backgroundColor: "#1B1B1B",
                              }}
                            >
                              {input.display_name}
                            </div>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent
                          side="left"
                          className="bg-popover/95 z-50 p-2 text-xs backdrop-blur-sm"
                        >
                          <div className="font-medium">{input.display_name}</div>
                          <div className="text-muted-foreground text-[10px]">
                            Type: {input.input_types?.join(", ") || "any"}
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  );
                })}
              </div>

              {/* Output Handles (Right) with Tooltips */}
              <div className="flex-1">
                {handleOutputs.map((output, index) => {
                  // Calculate proper vertical positioning for multiple handles
                  const currentHandleSpacing = getHandleSpacing(handleOutputs.length);
                  
                  return (
                    <div key={`output-${output.name}`} className="relative flex items-center" style={{ marginBottom: currentHandleSpacing }}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="group flex items-center justify-end relative w-full">
                            {/* Output label */}
                            <div
                              className="overflow-hidden rounded-[5px] border border-[#3F3F46] px-[10px] py-[3px] text-[9px] font-medium text-white"
                              style={{
                                backgroundColor: "#1B1B1B",
                              }}
                            >
                              {output.display_name}
                            </div>
                            <Handle
                              type="source"
                              position={Position.Right}
                              id={output.name}
                              data-testid={`handle-${output.name}`}
                              className="regular-handle !bg-primary hover:!bg-primary/80"
                              style={{
                                width: "10px",
                                height: "10px",
                                borderRadius: "5px",
                                border: "2px solid var(--background)",
                                right: "-5px",
                                position: "absolute",
                                zIndex: 50, // Ensure handle is above other elements
                              }}
                              isConnectable={isConnectable}
                            />
                          </div>
                        </TooltipTrigger>
                        <TooltipContent
                          side="right"
                          className="bg-popover/95 z-50 p-2 text-xs backdrop-blur-sm"
                        >
                          <div className="font-medium">{output.display_name}</div>
                          <div className="text-muted-foreground text-[10px]">
                            Type: {output.output_type}
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
});

// Add display name for React DevTools
WorkflowNode.displayName = "WorkflowNode";

export default WorkflowNode;
