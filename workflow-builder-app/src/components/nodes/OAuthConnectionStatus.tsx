"use client";

import React, { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ExternalLink, CheckCircle, AlertCircle, Loader2, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { OAuthDetails } from "@/types";
import { initiateOAuthFlow, checkOAuthCredentials, API_BASE_URL } from "@/lib/api";
import { useUserStore } from "@/store/userStore";
import { checkOAuthSuccess, clearOAuthSuccess, openOAuthInNewTab } from "@/lib/oauthUtils";

interface OAuthConnectionStatusProps {
  oauthDetails: OAuthDetails;
  toolName: string;
  nodeId: string;
  onConnectionChange: (isConnected: boolean) => void;
  className?: string;
  initialConnectionState?: {
    isConnected: boolean;
    provider?: string;
    connectedAt?: string;
    expiresAt?: string;
  };
}

export const OAuthConnectionStatus: React.FC<OAuthConnectionStatusProps> = ({
  oauthDetails,
  toolName,
  nodeId,
  onConnectionChange,
  className = "",
  initialConnectionState,
}) => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{
    is_connected: boolean;
    expires_in?: number;
  }>({
    is_connected: initialConnectionState?.isConnected ?? oauthDetails.is_connected,
    expires_in: undefined,
  });

  // Get current user from store
  const user = useUserStore((state) => state.user);

  // Add debouncing for OAuth status checks
  const lastCheckRef = useRef<number>(0);
  const CHECK_DEBOUNCE_TIME = 1000; // 1 second

  // Check connection status with debouncing
  const checkConnectionStatus = async () => {
    try {
      // Debounce OAuth status checks
      const now = Date.now();
      if (now - lastCheckRef.current < CHECK_DEBOUNCE_TIME) {
        return; // Skip if called too recently
      }
      lastCheckRef.current = now;

      if (process.env.NODE_ENV === 'development') {
        console.log(`Checking OAuth status for ${oauthDetails.tool_name} (${oauthDetails.provider})`);
      }

      const result = await checkOAuthCredentials({
        tool_name: oauthDetails.tool_name,
        provider: oauthDetails.provider,
      });

      if (process.env.NODE_ENV === 'development') {
        console.log(`OAuth status result:`, result);
      }

      if (result.success) {
        const newStatus = {
          is_connected: result.is_connected || false,
          expires_in: result.expires_in,
        };

        if (process.env.NODE_ENV === 'development') {
          console.log(`Updating connection status:`, newStatus);
        }
        setConnectionStatus(newStatus);
        onConnectionChange(newStatus.is_connected);

        // Also update the original oauth_details object
        if (oauthDetails) {
          oauthDetails.is_connected = newStatus.is_connected;
        }
      }
    } catch (error) {
      console.error("Error checking OAuth status:", error);
    }
  };

  // Listen for OAuth callback success (both sessionStorage and postMessage)
  useEffect(() => {
    const handleOAuthSuccess = () => {
      const successData = checkOAuthSuccess(oauthDetails.tool_name, oauthDetails.provider);
      if (successData) {
        // Clear the success flag
        clearOAuthSuccess();
        // Refresh connection status
        checkConnectionStatus();
        // Show success toast
        toast.success(`Successfully connected to ${successData.provider} for ${successData.toolName}`);
      }
    };

    // Listen for postMessage from OAuth popup
    const handleMessage = (event: MessageEvent) => {
      // Verify origin for security
      if (event.origin !== window.location.origin) return;

      // Filter out unrelated messages (like MetaMask)
      if (!event.data || typeof event.data !== 'object') return;

      // Only process OAuth-related messages
      if (event.data.type !== 'OAUTH_SUCCESS' && event.data.type !== 'OAUTH_ERROR') {
        // Ignore non-OAuth messages (like MetaMask messages)
        return;
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('Received OAuth postMessage:', event.data);
        console.log('Current tool details:', { toolName: oauthDetails.tool_name, provider: oauthDetails.provider });
      }

      if (event.data.type === 'OAUTH_SUCCESS') {
        // Check if this success is for our tool (match by provider since tool_name might not be passed correctly)
        if (event.data.provider === oauthDetails.provider) {
          if (process.env.NODE_ENV === 'development') {
            console.log('OAuth success matched for provider:', event.data.provider);
          }
          // Refresh connection status
          setTimeout(() => {
            checkConnectionStatus();
          }, 500);
          // Show success toast
          toast.success(`Successfully connected to ${event.data.provider}!`);
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.log('OAuth success did not match. Expected provider:', oauthDetails.provider, 'Received:', event.data.provider);
          }
        }
      }
    };

    // Check immediately when component mounts
    handleOAuthSuccess();

    // Set up interval to check for success (fallback for sessionStorage method)
    const interval = setInterval(handleOAuthSuccess, 1000);

    // Add message listener for new tab OAuth flow
    window.addEventListener('message', handleMessage);

    return () => {
      clearInterval(interval);
      window.removeEventListener('message', handleMessage);
    };
  }, [oauthDetails.tool_name, oauthDetails.provider]);

  // Also listen for focus events to refresh status when user returns to tab
  useEffect(() => {
    const handleFocus = () => {
      // Refresh connection status when window gains focus
      checkConnectionStatus();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, []);

  // Check connection status on mount
  useEffect(() => {
    checkConnectionStatus();
  }, []);

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      // Get current user ID from auth context
      const userId = user?.id || user?.email || "anonymous_user";

      // Build OAuth URL with parameters
      const queryParams = new URLSearchParams({
        provider: oauthDetails.provider,
        tool_name: oauthDetails.tool_name,
        user_id: userId,
        redirect_url: window.location.origin + "/oauth/callback",
      });

      if (oauthDetails.scopes) {
        queryParams.append("scopes", oauthDetails.scopes.join(","));
      }

      const oauthUrl = `${API_BASE_URL}/oauth/authorize?${queryParams.toString()}`;

      console.log('Opening OAuth URL:', oauthUrl);
      console.log('OAuth details:', oauthDetails);

      // Open OAuth in new tab
      openOAuthInNewTab(
        oauthUrl,
        () => {
          // Success callback
          toast.success(`Successfully connected to ${oauthDetails.provider}!`);

          // Force refresh connection status multiple times to ensure it updates
          setTimeout(() => checkConnectionStatus(), 100);
          setTimeout(() => checkConnectionStatus(), 500);
          setTimeout(() => checkConnectionStatus(), 1000);

          setIsConnecting(false);
        },
        (error) => {
          // Error callback
          toast.error(`OAuth connection failed: ${error}`);
          setIsConnecting(false);
        }
      );

      toast.info(`Opening ${oauthDetails.provider} authorization in new tab...`);
    } catch (error) {
      console.error("Error initiating OAuth flow:", error);
      toast.error("Failed to start OAuth connection");
      setIsConnecting(false);
    }
  };

  const getExpirationText = () => {
    if (!connectionStatus.expires_in) return null;
    
    const hours = Math.floor(connectionStatus.expires_in / 3600);
    const minutes = Math.floor((connectionStatus.expires_in % 3600) / 60);
    
    if (hours > 0) {
      return `Expires in ${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `Expires in ${minutes}m`;
    } else {
      return "Expires soon";
    }
  };

  if (connectionStatus.is_connected) {
    return (
      <TooltipProvider>
        <div className={`relative rounded-lg border border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 p-2 shadow-sm dark:border-green-800 dark:from-green-950/30 dark:to-emerald-950/30 ${className}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-500 text-white shadow-sm">
                <CheckCircle className="h-3.5 w-3.5" />
              </div>
              <div className="flex flex-col">
                <span className="text-xs font-medium text-green-800 dark:text-green-300">
                  Connected
                </span>
                <span className="text-[10px] text-green-600 dark:text-green-400">
                  {oauthDetails.provider}
                </span>
              </div>
            </div>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent triggering inspector panel
                    checkConnectionStatus();
                  }}
                  className="h-6 w-6 p-0 text-green-600 hover:bg-green-100 hover:text-green-700 dark:text-green-400 dark:hover:bg-green-900/20"
                >
                  <RefreshCw className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top" className="p-2 text-xs">
                <div className="font-medium">Refresh Connection</div>
                {connectionStatus.expires_in && (
                  <div className="text-muted-foreground text-[10px]">
                    {getExpirationText()}
                  </div>
                )}
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
      </TooltipProvider>
    );
  }

  return (
    <TooltipProvider>
      <div className={`relative rounded-lg border border-orange-200 bg-gradient-to-r from-orange-50 to-amber-50 p-1 shadow-sm dark:border-orange-800 dark:from-orange-950/30 dark:to-amber-950/30 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-orange-500 text-white shadow-sm">
              <AlertCircle className="h-3.5 w-3.5" />
            </div>
            <div className="flex flex-col">
              <span className="text-xs font-small text-orange-800 dark:text-orange-300">
                OAuth Required
              </span>
              <span className="text-[10px] text-orange-600 dark:text-orange-400">
                {oauthDetails.provider}
              </span>
            </div>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={handleConnect}
                disabled={isConnecting}
                className="h-7 px-1 text-xs font-medium border-orange-300 bg-white text-orange-700 hover:bg-orange-100 hover:border-orange-400 disabled:opacity-50 dark:border-orange-700 dark:bg-orange-950/50 dark:text-orange-300 dark:hover:bg-orange-900/50"
              >
                {isConnecting ? (
                  <>
                    <Loader2 className="h-3 w-3 mr-1.5 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <ExternalLink className="h-3 w-3 mr-1.5" />
                    Connect
                  </>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top" className="p-2 text-xs">
              <div className="font-medium">Connect to {oauthDetails.provider}</div>
              <div className="text-muted-foreground text-[10px]">
                Required to use this tool in your workflow
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </TooltipProvider>
  );
};
