import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { AgentDetailsPanel } from "../AgentDetailsPanel";
import { Node } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { Agent } from "@/types/agents";
import * as api from "@/lib/api";

// Mock the API functions
jest.mock("@/lib/api", () => ({
  fetchMCPsByIds: jest.fn(),
  fetchWorkflowsByIds: jest.fn(),
}));

const mockFetchMCPsByIds = api.fetchMCPsByIds as jest.MockedFunction<typeof api.fetchMCPsByIds>;
const mockFetchWorkflowsByIds = api.fetchWorkflowsByIds as jest.MockedFunction<typeof api.fetchWorkflowsByIds>;

describe("AgentDetailsPanel", () => {
  const createMockAgentNode = (agentInfo: Agent): Node<WorkflowNodeData> => ({
    id: "agent-1",
    type: "WorkflowNode",
    position: { x: 0, y: 0 },
    data: {
      label: "Test Agent",
      type: "agent",
      originalType: "agent-123",
      definition: {
        name: "agent-123",
        display_name: "Test Agent",
        description: "A test agent",
        category: "Agents",
        icon: "Users",
        beta: false,
        path: "agent.123",
        inputs: [],
        outputs: [],
        is_valid: true,
        type: "Agent",
        agent_info: agentInfo,
      },
    },
  });

  const mockAgentInfo: Agent = {
    id: "agent-123",
    name: "Test Agent",
    description: "A test agent for testing",
    avatar: "avatar-url",
    owner_id: "owner-123",
    user_ids: ["user-1", "user-2"],
    owner_type: "user",
    template_id: "template-123",
    template_owner_id: "template-owner-123",
    is_imported: false,
    is_bench_employee: false,
    is_changes_marketplace: false,
    is_updated: false,
    is_a2a: false,
    is_customizable: true,
    agent_category: "Assistant",
    system_message: "You are a helpful assistant",
    model_provider: "OpenAI",
    model_name: "gpt-4",
    temperature: 0.7,
    max_tokens: 1000,
    workflow_ids: ["workflow-1", "workflow-2"],
    mcp_server_ids: ["mcp-1", "mcp-2"],
    agent_topic_type: "general",
    visibility: "private",
    tags: ["test", "assistant"],
    status: "active",
    department: "engineering",
    organization_id: "org-123",
    tone: "professional",
    files: [],
    urls: [],
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z",
  };

  const mockWorkflowsResponse = {
    success: true,
    workflows: [
      {
        id: "workflow-1",
        name: "Test Workflow 1",
        description: "First test workflow",
      },
      {
        id: "workflow-2",
        name: "Test Workflow 2",
        description: "Second test workflow",
      },
    ],
    total: 2,
  };

  const mockMCPsResponse = {
    success: true,
    mcps: [
      {
        id: "mcp-1",
        name: "Test MCP 1",
        description: "First test MCP",
      },
      {
        id: "mcp-2",
        name: "Test MCP 2",
        description: "Second test MCP",
      },
    ],
    total: 2,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockFetchWorkflowsByIds.mockResolvedValue(mockWorkflowsResponse);
    mockFetchMCPsByIds.mockResolvedValue(mockMCPsResponse);
  });

  it("renders agent information correctly", async () => {
    const node = createMockAgentNode(mockAgentInfo);
    render(<AgentDetailsPanel node={node} />);

    expect(screen.getByText("Agent Information")).toBeInTheDocument();
    expect(screen.getByText("Test Agent")).toBeInTheDocument();
    expect(screen.getByText("agent-123")).toBeInTheDocument();
    expect(screen.getByText("Assistant")).toBeInTheDocument();
    expect(screen.getByText("gpt-4")).toBeInTheDocument();
  });

  it("fetches and displays workflows", async () => {
    const node = createMockAgentNode(mockAgentInfo);
    render(<AgentDetailsPanel node={node} />);

    expect(screen.getByText("Associated Workflows")).toBeInTheDocument();
    // Check for workflow badge specifically by looking for all badges with "2"
    const badges = screen.getAllByText("2");
    expect(badges.length).toBe(2); // Should have both workflow and MCP badges

    await waitFor(() => {
      expect(mockFetchWorkflowsByIds).toHaveBeenCalledWith(["workflow-1", "workflow-2"]);
    });

    await waitFor(() => {
      expect(screen.getByText("Test Workflow 1")).toBeInTheDocument();
      expect(screen.getByText("First test workflow")).toBeInTheDocument();
      expect(screen.getByText("Test Workflow 2")).toBeInTheDocument();
      expect(screen.getByText("Second test workflow")).toBeInTheDocument();
    });
  });

  it("fetches and displays MCPs", async () => {
    const node = createMockAgentNode(mockAgentInfo);
    render(<AgentDetailsPanel node={node} />);

    expect(screen.getByText("Associated MCPs")).toBeInTheDocument();
    // Check for MCP badge specifically by looking for all badges with "2"
    const badges = screen.getAllByText("2");
    expect(badges.length).toBe(2); // Should have both workflow and MCP badges

    await waitFor(() => {
      expect(mockFetchMCPsByIds).toHaveBeenCalledWith(["mcp-1", "mcp-2"]);
    });

    await waitFor(() => {
      expect(screen.getByText("Test MCP 1")).toBeInTheDocument();
      expect(screen.getByText("First test MCP")).toBeInTheDocument();
      expect(screen.getByText("Test MCP 2")).toBeInTheDocument();
      expect(screen.getByText("Second test MCP")).toBeInTheDocument();
    });
  });

  it("handles empty workflow and MCP lists", async () => {
    const agentInfoWithoutWorkflowsAndMCPs = {
      ...mockAgentInfo,
      workflow_ids: [],
      mcp_server_ids: [],
    };
    const node = createMockAgentNode(agentInfoWithoutWorkflowsAndMCPs);
    render(<AgentDetailsPanel node={node} />);

    expect(screen.getByText("No workflows associated with this agent")).toBeInTheDocument();
    expect(screen.getByText("No MCPs associated with this agent")).toBeInTheDocument();
  });

  it("handles API errors gracefully", async () => {
    mockFetchWorkflowsByIds.mockRejectedValue(new Error("API Error"));
    mockFetchMCPsByIds.mockRejectedValue(new Error("API Error"));

    const node = createMockAgentNode(mockAgentInfo);
    render(<AgentDetailsPanel node={node} />);

    await waitFor(() => {
      expect(screen.getByText("Error fetching workflows")).toBeInTheDocument();
      expect(screen.getByText("Error fetching MCPs")).toBeInTheDocument();
    });
  });

  it("does not render for non-agent nodes", () => {
    const nonAgentNode: Node<WorkflowNodeData> = {
      id: "component-1",
      type: "WorkflowNode",
      position: { x: 0, y: 0 },
      data: {
        label: "Test Component",
        type: "component",
        originalType: "InputNode",
        definition: {
          name: "InputNode",
          display_name: "Input",
          description: "An input component",
          category: "Input",
          icon: "Input",
          beta: false,
          path: "input",
          inputs: [],
          outputs: [],
          is_valid: true,
          type: "Component",
        },
      },
    };

    const { container } = render(<AgentDetailsPanel node={nonAgentNode} />);
    expect(container.firstChild).toBeNull();
  });
});
