import React from "react";
import { Node } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { checkInputVisibility } from "@/utils/inputVisibility";
import { FormField } from "../FormField";

interface ConditionalNodeInspectorProps {
  node: Node<WorkflowNodeData>;
  onConfigChange: (inputName: string, value: any) => void;
  isInputConnected: (inputName: string) => boolean;
  shouldDisableInput: (inputName: string) => boolean;
  getConnectionInfo: (inputName: string) => {
    isConnected: boolean;
    sourceNodeId?: string;
    sourceNodeLabel?: string;
  };
}

/**
 * Specialized inspector for ConditionalNode (Switch-Case Router)
 * Implements dual-layer conditional rendering like the API node
 */
export function ConditionalNodeInspector({
  node,
  onConfigChange,
  isInputConnected,
  shouldDisableInput,
  getConnectionInfo,
}: ConditionalNodeInspectorProps) {
  const source = node.data.config?.source;

  return (
    <div className="space-y-4">
      {/* Basic Configuration - always visible inputs */}
      {node.data.definition?.inputs
        .filter((inputDef) =>
          !inputDef.is_handle &&
          !["variable_name"].includes(inputDef.name)
        )
        .map((inputDef) => {
          // Check visibility rules
          const isVisible = checkInputVisibility(
            inputDef,
            node,
            node.data.config || {},
          );
          if (!isVisible) return null;

          return (
            <FormField
              key={inputDef.name}
              inputDef={inputDef}
              node={node}
              onConfigChange={onConfigChange}
              isInputConnected={isInputConnected}
              shouldDisableInput={shouldDisableInput}
              getConnectionInfo={getConnectionInfo}
            />
          );
        })}

      {/* Variable Name - only show when source is global_context */}
      {source === "global_context" &&
        node.data.definition?.inputs
          .filter((inputDef) => inputDef.name === "variable_name")
          .map((inputDef) => {
            // Check visibility rules as well
            const isVisible = checkInputVisibility(
              inputDef,
              node,
              node.data.config || {},
            );
            if (!isVisible) return null;

            return (
              <FormField
                key={inputDef.name}
                inputDef={inputDef}
                node={node}
                onConfigChange={onConfigChange}
                isInputConnected={isInputConnected}
                shouldDisableInput={shouldDisableInput}
                getConnectionInfo={getConnectionInfo}
              />
            );
          })}

      {/* Special handling for dynamic inputs in the config */}
      {node.data.config?.inputs?.filter((input: any) => 
        !node.data.definition?.inputs?.some(defInput => defInput.name === input.name)
      ).map((inputDef: any) => {
        // Check if this input should be visible based on rules
        const isVisible = checkInputVisibility(
          inputDef,
          node,
          node.data.config || {},
        );
        if (!isVisible) return null;

        return (
          <FormField
            key={inputDef.name}
            inputDef={inputDef}
            node={node}
            onConfigChange={onConfigChange}
            isInputConnected={isInputConnected}
            shouldDisableInput={shouldDisableInput}
            getConnectionInfo={getConnectionInfo}
          />
        );
      })}
    </div>
  );
}
