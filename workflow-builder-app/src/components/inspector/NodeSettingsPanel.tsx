import React from "react";
import { Node, <PERSON> } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input-credential";
import { Separator } from "@/components/ui/separator";

// Import the extracted components
import { ApiNodeSettings } from "./ApiNodeSettings";
import { DefaultNodeSettings } from "./DefaultNodeSettings";
import { AgenticAIToolPanel } from "./AgenticAIToolPanel";
import { ConditionalNodeInspector } from "./node-types/ConditionalNodeInspector";

interface NodeSettingsPanelProps {
  node: Node<WorkflowNodeData>;
  onLabelChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onConfigChange: (inputName: string, value: any) => void;
  isInputConnected: (inputName: string) => boolean;
  shouldDisableInput: (inputName: string) => boolean;
  getConnectionInfo: (inputName: string) => {
    isConnected: boolean;
    sourceNodeId?: string;
    sourceNodeLabel?: string;
  };
  // Additional props for tool management
  nodes?: Node<WorkflowNodeData>[];
  edges?: Edge[];
  onAddToolSlot?: (nodeId: string) => void;
  onRemoveToolSlot?: (nodeId: string, handleId: string) => void;
}

/**
 * Component for displaying node settings in the inspector
 */
export function NodeSettingsPanel({
  node,
  onLabelChange,
  onConfigChange,
  isInputConnected,
  shouldDisableInput,
  getConnectionInfo,
  nodes = [],
  edges = [],
  onAddToolSlot,
  onRemoveToolSlot,
}: NodeSettingsPanelProps) {
  // Check if this is an AgenticAI node
  const isAgenticAINode = node.data.originalType === "AgenticAI";
  return (
    <ScrollArea className="h-full flex-grow overflow-auto p-4">
      <div className="space-y-4">
        {/* Node Label Editing */}
        <div>
          <Label htmlFor="nodeLabel" className="text-sm font-medium">
            Node Label
          </Label>
          <Input
            id="nodeLabel"
            value={node.data.label}
            onChange={onLabelChange}
            className="bg-background/50 mt-1 h-9 text-sm"
          />
        </div>

        <Separator />

        {/* AgenticAI Tool Management */}
        {/* {isAgenticAINode && onAddToolSlot && onRemoveToolSlot && (
          <>
            <AgenticAIToolPanel
              node={node}
              nodes={nodes}
              edges={edges}
              onConfigChange={onConfigChange}
              onAddToolSlot={onAddToolSlot}
              onRemoveToolSlot={onRemoveToolSlot}
            />
            <Separator />
          </>
        )} */}

        {/* Node Configuration */}
        {node.data.definition?.inputs && (
          <div className="space-y-6">
            {/* Special handling for API Request node */}
            {node.data.type === "ApiRequestNode" ? (
              <ApiNodeSettings
                node={node}
                onConfigChange={onConfigChange}
                isInputConnected={isInputConnected}
                shouldDisableInput={shouldDisableInput}
                getConnectionInfo={getConnectionInfo}
              />
            ) : node.data.originalType === "ConditionalNode" ? (
              <ConditionalNodeInspector
                node={node}
                onConfigChange={onConfigChange}
                isInputConnected={isInputConnected}
                shouldDisableInput={shouldDisableInput}
                getConnectionInfo={getConnectionInfo}
              />
            ) : (
              <DefaultNodeSettings
                node={node}
                onConfigChange={onConfigChange}
                isInputConnected={isInputConnected}
                shouldDisableInput={shouldDisableInput}
                getConnectionInfo={getConnectionInfo}
              />
            )}
          </div>
        )}
      </div>
    </ScrollArea>
  );
}
