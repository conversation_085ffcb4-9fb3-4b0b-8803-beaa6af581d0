/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TemplateVariableManagementDialog, type TemplateVariableDefinition } from '../TemplateVariableManagementDialog';

// Mock the utils
jest.mock('@/utils/templateVariables', () => ({
  validateTemplateVariables: jest.fn(),
  detectTemplateVariables: jest.fn(),
  insertTemplateVariable: jest.fn(),
}));

// Mock the UI components that might not be available in test environment
jest.mock('@/components/ui/custom-scroll-area', () => ({
  CustomScrollArea: ({ children, className }: any) => (
    <div className={className} data-testid="scroll-area">
      {children}
    </div>
  ),
}));

// Sample test data
const mockVariables: TemplateVariableDefinition[] = [
  {
    id: '1',
    name: 'api_key',
    description: 'API key for authentication',
    defaultValue: 'test-key',
    type: 'string',
    required: true,
    category: 'API',
    tags: ['auth', 'api'],
    usageCount: 5,
    lastUsed: new Date('2024-01-15'),
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15'),
  },
  {
    id: '2',
    name: 'database_url',
    description: 'Database connection URL',
    type: 'string',
    required: false,
    category: 'Database',
    tags: ['db', 'connection'],
    usageCount: 2,
    lastUsed: new Date('2024-01-10'),
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-10'),
  },
];

const mockUsage = {
  api_key: [
    {
      nodeId: 'node1',
      nodeName: 'API Request',
      fieldPath: 'headers.authorization',
      fieldName: 'Authorization Header',
      context: 'Bearer token',
    },
  ],
};

const defaultProps = {
  open: true,
  onOpenChange: jest.fn(),
  variables: mockVariables,
  onVariableCreate: jest.fn(),
  onVariableUpdate: jest.fn(),
  onVariableDelete: jest.fn(),
  onVariableInsert: jest.fn(),
  workflowUsage: mockUsage,
};

describe('TemplateVariableManagementDialog', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Dialog Rendering', () => {
    it('renders the dialog when open', () => {
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      expect(screen.getByText('Template Variable Management')).toBeInTheDocument();
      expect(screen.getByText(/Create, edit, and manage template variables/)).toBeInTheDocument();
    });

    it('does not render when closed', () => {
      render(<TemplateVariableManagementDialog {...defaultProps} open={false} />);
      
      expect(screen.queryByText('Template Variable Management')).not.toBeInTheDocument();
    });

    it('renders all three tabs', () => {
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      expect(screen.getByText('Browse Variables')).toBeInTheDocument();
      expect(screen.getByText('Create Variable')).toBeInTheDocument();
      expect(screen.getByText('Help & Documentation')).toBeInTheDocument();
    });
  });

  describe('Browse Variables Tab', () => {
    it('displays variables list', () => {
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      expect(screen.getByText('{api_key}')).toBeInTheDocument();
      expect(screen.getByText('{database_url}')).toBeInTheDocument();
      expect(screen.getByText('API key for authentication')).toBeInTheDocument();
      expect(screen.getByText('Database connection URL')).toBeInTheDocument();
    });

    it('shows empty state when no variables', () => {
      render(<TemplateVariableManagementDialog {...defaultProps} variables={[]} />);
      
      expect(screen.getByText('No template variables yet')).toBeInTheDocument();
      expect(screen.getByText('Create your first template variable to get started.')).toBeInTheDocument();
    });

    it('filters variables by search query', async () => {
      const user = userEvent.setup();
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      const searchInput = screen.getByPlaceholderText(/Search variables/);
      await user.type(searchInput, 'api');
      
      expect(screen.getByText('{api_key}')).toBeInTheDocument();
      expect(screen.queryByText('{database_url}')).not.toBeInTheDocument();
    });

    it('filters variables by category', async () => {
      const user = userEvent.setup();
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      const categorySelect = screen.getByDisplayValue('All');
      await user.selectOptions(categorySelect, 'API');
      
      expect(screen.getByText('{api_key}')).toBeInTheDocument();
      expect(screen.queryByText('{database_url}')).not.toBeInTheDocument();
    });

    it('shows usage details when toggled', async () => {
      const user = userEvent.setup();
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      const showUsageButton = screen.getByText('Show Usage');
      await user.click(showUsageButton);
      
      expect(screen.getByText('Usage in Workflow:')).toBeInTheDocument();
      expect(screen.getByText('API Request')).toBeInTheDocument();
      // Check for usage details in a more flexible way
      expect(screen.getByText(/Authorization/)).toBeInTheDocument();
    });

    it('calls onVariableInsert when insert button clicked', async () => {
      const user = userEvent.setup();
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      const insertButtons = screen.getAllByTitle('Insert variable');
      await user.click(insertButtons[0]);
      
      expect(defaultProps.onVariableInsert).toHaveBeenCalledWith('api_key');
      expect(defaultProps.onOpenChange).toHaveBeenCalledWith(false);
    });

    it('calls onVariableDelete when delete button clicked', async () => {
      const user = userEvent.setup();
      // Mock window.confirm to return true
      const confirmSpy = jest.spyOn(window, 'confirm').mockReturnValue(true);
      
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      const deleteButtons = screen.getAllByTitle('Delete variable');
      await user.click(deleteButtons[0]);
      
      expect(defaultProps.onVariableDelete).toHaveBeenCalledWith('1');
      
      confirmSpy.mockRestore();
    });

    it('switches to edit mode when edit button clicked', async () => {
      const user = userEvent.setup();
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      const editButtons = screen.getAllByTitle('Edit variable');
      await user.click(editButtons[0]);
      
      expect(screen.getByText('Edit Variable: api_key')).toBeInTheDocument();
      expect(screen.getByDisplayValue('api_key')).toBeInTheDocument();
      expect(screen.getByDisplayValue('API key for authentication')).toBeInTheDocument();
    });
  });

  describe('Create Variable Tab', () => {
    beforeEach(async () => {
      const user = userEvent.setup();
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      const createTab = screen.getByText('Create Variable');
      await user.click(createTab);
    });

    it('renders create form', () => {
      expect(screen.getByText('Create New Variable')).toBeInTheDocument();
      expect(screen.getByLabelText('Variable Name *')).toBeInTheDocument();
      expect(screen.getByLabelText('Description *')).toBeInTheDocument();
      expect(screen.getByLabelText('Variable Type')).toBeInTheDocument();
    });

    it('validates required fields', async () => {
      const user = userEvent.setup();

      // The create button should be disabled when required fields are empty
      const createButton = screen.getByRole('button', { name: /create variable/i });
      expect(createButton).toBeDisabled();

      // Fill in name but not description
      const nameInput = screen.getByLabelText('Variable Name *');
      await user.type(nameInput, 'test_var');

      // Button should still be disabled
      expect(createButton).toBeDisabled();

      // Clear name field to test validation
      await user.clear(nameInput);
      await user.type(nameInput, ' '); // Add space to trigger validation
      await user.clear(nameInput); // Clear again

      // Try to click the button (it should be disabled but let's test the validation logic)
      // We need to enable the button temporarily to test validation
      const descInput = screen.getByLabelText('Description *');
      await user.type(nameInput, 'temp');
      await user.type(descInput, 'temp');

      // Now clear both to trigger validation
      await user.clear(nameInput);
      await user.clear(descInput);

      // The button should be disabled again
      expect(createButton).toBeDisabled();
    });

    it('validates variable name format', async () => {
      const user = userEvent.setup();

      const nameInput = screen.getByLabelText('Variable Name *');
      const descInput = screen.getByLabelText('Description *');

      // Fill in invalid name and valid description to enable button
      await user.type(nameInput, '123invalid');
      await user.type(descInput, 'Valid description');

      const createButton = screen.getByRole('button', { name: /create variable/i });
      expect(createButton).not.toBeDisabled();

      await user.click(createButton);

      // Wait for validation errors to appear
      await waitFor(() => {
        expect(screen.getByText(/Variable name must start with a letter/)).toBeInTheDocument();
      });
    });

    it('creates variable with valid data', async () => {
      const user = userEvent.setup();
      
      const nameInput = screen.getByLabelText('Variable Name *');
      const descInput = screen.getByLabelText('Description *');
      
      await user.type(nameInput, 'test_variable');
      await user.type(descInput, 'Test variable description');
      
      const createButton = screen.getByRole('button', { name: /create variable/i });
      await user.click(createButton);
      
      expect(defaultProps.onVariableCreate).toHaveBeenCalledWith({
        name: 'test_variable',
        description: 'Test variable description',
        defaultValue: '',
        type: 'string',
        required: false,
        category: 'General',
        tags: [],
        usageCount: 0,
        lastUsed: undefined,
      });
    });

    it('adds and removes tags', async () => {
      const user = userEvent.setup();
      
      const tagInput = screen.getByPlaceholderText('Add a tag...');
      await user.type(tagInput, 'test-tag');
      
      const addButton = screen.getByText('Add');
      await user.click(addButton);
      
      expect(screen.getByText('test-tag')).toBeInTheDocument();
      
      // Remove tag
      const removeButton = screen.getByRole('button', { name: '' }); // X button
      await user.click(removeButton);
      
      expect(screen.queryByText('test-tag')).not.toBeInTheDocument();
    });

    it('updates preview as form changes', async () => {
      const user = userEvent.setup();
      
      const nameInput = screen.getByLabelText('Variable Name *');
      await user.type(nameInput, 'preview_test');
      
      expect(screen.getByText('{preview_test}')).toBeInTheDocument();
    });
  });

  describe('Help Tab', () => {
    it('renders help content', async () => {
      const user = userEvent.setup();
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      const helpTab = screen.getByText('Help & Documentation');
      await user.click(helpTab);
      
      expect(screen.getByText('Quick Start')).toBeInTheDocument();
      expect(screen.getByText('Syntax Guide')).toBeInTheDocument();
      expect(screen.getByText('Best Practices')).toBeInTheDocument();
      expect(screen.getByText('Common Use Cases')).toBeInTheDocument();
    });

    it('shows syntax examples', async () => {
      const user = userEvent.setup();
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      const helpTab = screen.getByText('Help & Documentation');
      await user.click(helpTab);
      
      expect(screen.getAllByText('{variable_name}')[0]).toBeInTheDocument();
      expect(screen.getByText('${variable_name}')).toBeInTheDocument();
    });
  });

  describe('Dialog Controls', () => {
    it('calls onOpenChange when dialog is closed', async () => {
      const user = userEvent.setup();
      render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      // This would typically be triggered by clicking outside or pressing escape
      // We'll simulate it by calling the onOpenChange directly
      const dialog = screen.getByRole('dialog');
      expect(dialog).toBeInTheDocument();
      
      // Test that the prop is properly passed
      expect(defaultProps.onOpenChange).toBeDefined();
    });

    it('resets form when dialog closes and reopens', async () => {
      const { rerender } = render(<TemplateVariableManagementDialog {...defaultProps} />);
      
      // Close dialog
      rerender(<TemplateVariableManagementDialog {...defaultProps} open={false} />);
      
      // Reopen dialog
      rerender(<TemplateVariableManagementDialog {...defaultProps} open={true} />);
      
      // Should be back on browse tab
      expect(screen.getByText('Browse Variables')).toBeInTheDocument();
    });
  });
});
