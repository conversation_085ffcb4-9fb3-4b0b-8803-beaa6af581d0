/**
 * Template Variable Management Dialog
 * 
 * Comprehensive interface for managing template variables in workflows:
 * - Create, edit, and delete template variables
 * - <PERSON>rowse and search existing variables
 * - View usage tracking and documentation
 * - Help system for template variable syntax
 */

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { CustomScrollArea } from '@/components/ui/custom-scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Plus,
  Edit2,
  Trash2,
  Copy,
  BookOpen,
  AlertCircle,
  CheckCircle,
  Info,
  Variable,
  Code,
  FileText,
  Lightbulb,
  X,
  Eye,
  EyeOff,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  validateTemplateVariables,
  detectTemplateVariables,
  insertTemplateVariable,
  type TemplateVariable,
  type TemplateVariableValidation,
} from '@/utils/templateVariables';

// Types for template variable management
export interface TemplateVariableDefinition {
  id: string;
  name: string;
  description: string;
  defaultValue?: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  category: string;
  tags: string[];
  usageCount: number;
  lastUsed?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface TemplateVariableUsage {
  nodeId: string;
  nodeName: string;
  fieldPath: string;
  fieldName: string;
  context: string;
}

export interface TemplateVariableManagementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  variables?: TemplateVariableDefinition[];
  onVariableCreate?: (variable: Omit<TemplateVariableDefinition, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onVariableUpdate?: (id: string, variable: Partial<TemplateVariableDefinition>) => void;
  onVariableDelete?: (id: string) => void;
  onVariableInsert?: (variableName: string) => void;
  workflowUsage?: Record<string, TemplateVariableUsage[]>;
  className?: string;
}

// Default categories for template variables
const DEFAULT_CATEGORIES = [
  'General',
  'Configuration',
  'API',
  'Database',
  'File System',
  'Authentication',
  'Workflow Control',
  'Custom',
];

// Variable type options
const VARIABLE_TYPES = [
  { value: 'string', label: 'String', description: 'Text value' },
  { value: 'number', label: 'Number', description: 'Numeric value' },
  { value: 'boolean', label: 'Boolean', description: 'True/false value' },
  { value: 'object', label: 'Object', description: 'JSON object' },
  { value: 'array', label: 'Array', description: 'List of values' },
] as const;

export function TemplateVariableManagementDialog({
  open,
  onOpenChange,
  variables = [],
  onVariableCreate,
  onVariableUpdate,
  onVariableDelete,
  onVariableInsert,
  workflowUsage = {},
  className,
}: TemplateVariableManagementDialogProps) {
  // State management
  const [activeTab, setActiveTab] = useState<'browse' | 'create' | 'help'>('browse');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [selectedVariable, setSelectedVariable] = useState<TemplateVariableDefinition | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showUsageDetails, setShowUsageDetails] = useState<Record<string, boolean>>({});

  // Form state for creating/editing variables
  const [formData, setFormData] = useState<Partial<TemplateVariableDefinition>>({
    name: '',
    description: '',
    defaultValue: '',
    type: 'string',
    required: false,
    category: 'General',
    tags: [],
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [newTag, setNewTag] = useState('');

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setFormData({
        name: '',
        description: '',
        defaultValue: '',
        type: 'string',
        required: false,
        category: 'General',
        tags: [],
      });
      setFormErrors({});
      setSelectedVariable(null);
      setIsEditing(false);
      setActiveTab('browse');
    }
  }, [open]);

  // Filter and search variables
  const filteredVariables = useMemo(() => {
    let filtered = variables;

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (variable) =>
          variable.name.toLowerCase().includes(query) ||
          variable.description.toLowerCase().includes(query) ||
          variable.category.toLowerCase().includes(query) ||
          variable.tags.some((tag) => tag.toLowerCase().includes(query))
      );
    }

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter((variable) => variable.category === selectedCategory);
    }

    // Sort by usage count and name
    return filtered.sort((a, b) => {
      if (a.usageCount !== b.usageCount) {
        return b.usageCount - a.usageCount;
      }
      return a.name.localeCompare(b.name);
    });
  }, [variables, searchQuery, selectedCategory]);

  // Get unique categories from variables
  const availableCategories = useMemo(() => {
    const categories = new Set(['All', ...DEFAULT_CATEGORIES]);
    variables.forEach((variable) => categories.add(variable.category));
    return Array.from(categories).sort();
  }, [variables]);

  // Form validation
  const validateForm = useCallback(() => {
    const errors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      errors.name = 'Variable name is required';
    } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(formData.name)) {
      errors.name = 'Variable name must start with a letter and contain only letters, numbers, and underscores';
    } else if (variables.some((v) => v.name === formData.name && v.id !== selectedVariable?.id)) {
      errors.name = 'Variable name already exists';
    }

    if (!formData.description?.trim()) {
      errors.description = 'Description is required';
    }

    if (!formData.category?.trim()) {
      errors.category = 'Category is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData, variables, selectedVariable]);

  // Handle form submission
  const handleSubmit = useCallback(() => {
    if (!validateForm()) return;

    const variableData = {
      ...formData,
      name: formData.name!.trim(),
      description: formData.description!.trim(),
      category: formData.category!.trim(),
      tags: formData.tags || [],
      usageCount: selectedVariable?.usageCount || 0,
      lastUsed: selectedVariable?.lastUsed,
    } as Omit<TemplateVariableDefinition, 'id' | 'createdAt' | 'updatedAt'>;

    if (isEditing && selectedVariable) {
      onVariableUpdate?.(selectedVariable.id, variableData);
    } else {
      onVariableCreate?.(variableData);
    }

    // Reset form and switch to browse tab
    setFormData({
      name: '',
      description: '',
      defaultValue: '',
      type: 'string',
      required: false,
      category: 'General',
      tags: [],
    });
    setSelectedVariable(null);
    setIsEditing(false);
    setActiveTab('browse');
  }, [formData, validateForm, isEditing, selectedVariable, onVariableCreate, onVariableUpdate]);

  // Handle editing a variable
  const handleEdit = useCallback((variable: TemplateVariableDefinition) => {
    setSelectedVariable(variable);
    setFormData({
      name: variable.name,
      description: variable.description,
      defaultValue: variable.defaultValue,
      type: variable.type,
      required: variable.required,
      category: variable.category,
      tags: [...variable.tags],
    });
    setIsEditing(true);
    setActiveTab('create');
  }, []);

  // Handle deleting a variable
  const handleDelete = useCallback((variable: TemplateVariableDefinition) => {
    if (window.confirm(`Are you sure you want to delete the variable "${variable.name}"?`)) {
      onVariableDelete?.(variable.id);
    }
  }, [onVariableDelete]);

  // Handle inserting a variable
  const handleInsert = useCallback((variableName: string) => {
    onVariableInsert?.(variableName);
    onOpenChange(false);
  }, [onVariableInsert, onOpenChange]);

  // Handle adding a tag
  const handleAddTag = useCallback(() => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()],
      }));
      setNewTag('');
    }
  }, [newTag, formData.tags]);

  // Handle removing a tag
  const handleRemoveTag = useCallback((tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags?.filter((tag) => tag !== tagToRemove) || [],
    }));
  }, []);

  // Toggle usage details visibility
  const toggleUsageDetails = useCallback((variableId: string) => {
    setShowUsageDetails((prev) => ({
      ...prev,
      [variableId]: !prev[variableId],
    }));
  }, []);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn('max-w-4xl max-h-[90vh] flex flex-col', className)}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Variable className="h-5 w-5" />
            Template Variable Management
          </DialogTitle>
          <DialogDescription>
            Create, edit, and manage template variables for your workflow. Use {'{variable_name}'} syntax in input fields.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="browse" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Browse Variables
            </TabsTrigger>
            <TabsTrigger value="create" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              {isEditing ? 'Edit Variable' : 'Create Variable'}
            </TabsTrigger>
            <TabsTrigger value="help" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Help & Documentation
            </TabsTrigger>
          </TabsList>

          {/* Browse Variables Tab */}
          <TabsContent value="browse" className="flex-1 flex flex-col space-y-4">
            {/* Search and Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search variables by name, description, or tags..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-input bg-background rounded-md text-sm"
                >
                  {availableCategories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setActiveTab('create')}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  New Variable
                </Button>
              </div>
            </div>

            {/* Variables List */}
            <CustomScrollArea className="flex-1 border rounded-lg">
              <div className="p-4 space-y-3">
                {filteredVariables.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {variables.length === 0 ? (
                      <div className="space-y-2">
                        <Variable className="h-12 w-12 mx-auto opacity-50" />
                        <p className="text-lg font-medium">No template variables yet</p>
                        <p className="text-sm">Create your first template variable to get started.</p>
                        <Button
                          variant="outline"
                          onClick={() => setActiveTab('create')}
                          className="mt-4"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Create Variable
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Search className="h-8 w-8 mx-auto opacity-50" />
                        <p>No variables match your search criteria</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSearchQuery('');
                            setSelectedCategory('All');
                          }}
                        >
                          Clear filters
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  filteredVariables.map((variable) => (
                    <Card key={variable.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-lg flex items-center gap-2">
                              <code className="bg-muted px-2 py-1 rounded text-sm font-mono">
                                {'{' + variable.name + '}'}
                              </code>
                              {variable.required && (
                                <Badge variant="destructive" className="text-xs">
                                  Required
                                </Badge>
                              )}
                              <Badge variant="secondary" className="text-xs">
                                {variable.type}
                              </Badge>
                            </CardTitle>
                            <CardDescription className="mt-1">
                              {variable.description}
                            </CardDescription>
                          </div>
                          <div className="flex items-center gap-1 ml-4">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleInsert(variable.name)}
                              title="Insert variable"
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(variable)}
                              title="Edit variable"
                            >
                              <Edit2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(variable)}
                              title="Delete variable"
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          {/* Variable Details */}
                          <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
                            <Badge variant="outline" className="text-xs">
                              {variable.category}
                            </Badge>
                            {variable.tags.map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>

                          {/* Default Value */}
                          {variable.defaultValue && (
                            <div className="text-sm">
                              <span className="text-muted-foreground">Default: </span>
                              <code className="bg-muted px-1 py-0.5 rounded text-xs">
                                {variable.defaultValue}
                              </code>
                            </div>
                          )}

                          {/* Usage Information */}
                          <div className="flex items-center justify-between text-sm text-muted-foreground">
                            <div className="flex items-center gap-4">
                              <span>Used {variable.usageCount} times</span>
                              {variable.lastUsed && (
                                <span>Last used {variable.lastUsed.toLocaleDateString()}</span>
                              )}
                            </div>
                            {workflowUsage[variable.name] && workflowUsage[variable.name].length > 0 && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleUsageDetails(variable.id)}
                                className="text-xs h-6 px-2"
                              >
                                {showUsageDetails[variable.id] ? (
                                  <>
                                    <EyeOff className="h-3 w-3 mr-1" />
                                    Hide Usage
                                  </>
                                ) : (
                                  <>
                                    <Eye className="h-3 w-3 mr-1" />
                                    Show Usage
                                  </>
                                )}
                              </Button>
                            )}
                          </div>

                          {/* Usage Details */}
                          {showUsageDetails[variable.id] && workflowUsage[variable.name] && (
                            <div className="mt-3 p-3 bg-muted rounded-lg">
                              <h4 className="text-sm font-medium mb-2">Usage in Workflow:</h4>
                              <div className="space-y-2">
                                {workflowUsage[variable.name].map((usage, index) => (
                                  <div key={index} className="text-xs text-muted-foreground">
                                    <div className="font-medium">{usage.nodeName}</div>
                                    <div className="ml-2">
                                      Field: {usage.fieldName} ({usage.fieldPath})
                                    </div>
                                    {usage.context && (
                                      <div className="ml-2 text-xs opacity-75">
                                        Context: {usage.context}
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </CustomScrollArea>
          </TabsContent>

          {/* Create/Edit Variable Tab */}
          <TabsContent value="create" className="flex-1 flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                {isEditing ? `Edit Variable: ${selectedVariable?.name}` : 'Create New Variable'}
              </h3>
              {isEditing && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsEditing(false);
                    setSelectedVariable(null);
                    setFormData({
                      name: '',
                      description: '',
                      defaultValue: '',
                      type: 'string',
                      required: false,
                      category: 'General',
                      tags: [],
                    });
                    setFormErrors({});
                  }}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel Edit
                </Button>
              )}
            </div>

            <CustomScrollArea className="flex-1">
              <div className="space-y-6 pr-4">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                    Basic Information
                  </h4>

                  {/* Variable Name */}
                  <div className="space-y-2">
                    <Label htmlFor="variable-name">Variable Name *</Label>
                    <Input
                      id="variable-name"
                      placeholder="e.g., api_key, user_name, database_url"
                      value={formData.name || ''}
                      onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                      className={formErrors.name ? 'border-destructive' : ''}
                    />
                    {formErrors.name && (
                      <p className="text-sm text-destructive">{formErrors.name}</p>
                    )}
                    <p className="text-xs text-muted-foreground">
                      Must start with a letter and contain only letters, numbers, and underscores.
                    </p>
                  </div>

                  {/* Description */}
                  <div className="space-y-2">
                    <Label htmlFor="variable-description">Description *</Label>
                    <Textarea
                      id="variable-description"
                      placeholder="Describe what this variable is used for..."
                      value={formData.description || ''}
                      onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                      className={formErrors.description ? 'border-destructive' : ''}
                      rows={3}
                    />
                    {formErrors.description && (
                      <p className="text-sm text-destructive">{formErrors.description}</p>
                    )}
                  </div>

                  {/* Variable Type */}
                  <div className="space-y-2">
                    <Label htmlFor="variable-type">Variable Type</Label>
                    <select
                      id="variable-type"
                      value={formData.type || 'string'}
                      onChange={(e) => setFormData((prev) => ({ ...prev, type: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                    >
                      {VARIABLE_TYPES.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label} - {type.description}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Default Value */}
                  <div className="space-y-2">
                    <Label htmlFor="variable-default">Default Value (Optional)</Label>
                    <Input
                      id="variable-default"
                      placeholder="Enter a default value..."
                      value={formData.defaultValue || ''}
                      onChange={(e) => setFormData((prev) => ({ ...prev, defaultValue: e.target.value }))}
                    />
                    <p className="text-xs text-muted-foreground">
                      This value will be used when the variable is not explicitly set.
                    </p>
                  </div>
                </div>

                <Separator />

                {/* Organization */}
                <div className="space-y-4">
                  <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                    Organization
                  </h4>

                  {/* Category */}
                  <div className="space-y-2">
                    <Label htmlFor="variable-category">Category</Label>
                    <select
                      id="variable-category"
                      value={formData.category || 'General'}
                      onChange={(e) => setFormData((prev) => ({ ...prev, category: e.target.value }))}
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                    >
                      {DEFAULT_CATEGORIES.map((category) => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Tags */}
                  <div className="space-y-2">
                    <Label>Tags</Label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add a tag..."
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleAddTag();
                          }
                        }}
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleAddTag}
                        disabled={!newTag.trim()}
                      >
                        Add
                      </Button>
                    </div>
                    {formData.tags && formData.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {formData.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                            <button
                              type="button"
                              onClick={() => handleRemoveTag(tag)}
                              className="ml-1 hover:text-destructive"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Required Flag */}
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="variable-required"
                      checked={formData.required || false}
                      onChange={(e) => setFormData((prev) => ({ ...prev, required: e.target.checked }))}
                      className="rounded border-input"
                    />
                    <Label htmlFor="variable-required" className="text-sm">
                      This variable is required
                    </Label>
                  </div>
                </div>

                {/* Preview */}
                <div className="space-y-4">
                  <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                    Preview
                  </h4>
                  <Card>
                    <CardContent className="pt-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <code className="bg-muted px-2 py-1 rounded text-sm font-mono">
                            {'{' + (formData.name || 'variable_name') + '}'}
                          </code>
                          {formData.required && (
                            <Badge variant="destructive" className="text-xs">
                              Required
                            </Badge>
                          )}
                          <Badge variant="secondary" className="text-xs">
                            {formData.type || 'string'}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {formData.description || 'Variable description will appear here...'}
                        </p>
                        {formData.defaultValue && (
                          <div className="text-sm">
                            <span className="text-muted-foreground">Default: </span>
                            <code className="bg-muted px-1 py-0.5 rounded text-xs">
                              {formData.defaultValue}
                            </code>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CustomScrollArea>

            {/* Form Actions */}
            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setActiveTab('browse')}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={!formData.name?.trim() || !formData.description?.trim()}
              >
                {isEditing ? 'Update Variable' : 'Create Variable'}
              </Button>
            </div>
          </TabsContent>

          {/* Help & Documentation Tab */}
          <TabsContent value="help" className="flex-1 flex flex-col">
            <CustomScrollArea className="flex-1">
              <div className="space-y-6 pr-4">
                {/* Quick Start */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Lightbulb className="h-5 w-5 text-yellow-500" />
                    <h3 className="text-lg font-semibold">Quick Start</h3>
                  </div>
                  <Card>
                    <CardContent className="pt-4">
                      <div className="space-y-3">
                        <p className="text-sm">
                          Template variables allow you to create reusable, configurable workflows. Use the{' '}
                          <code className="bg-muted px-1 py-0.5 rounded text-xs">{'{variable_name}'}</code>{' '}
                          syntax in any input field.
                        </p>
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Example:</p>
                          <div className="bg-muted p-3 rounded-lg font-mono text-sm">
                            <div>API URL: <span className="text-blue-600">{'{api_base_url}'}</span>/users</div>
                            <div>API Key: <span className="text-blue-600">{'{api_key}'}</span></div>
                            <div>User ID: <span className="text-blue-600">{'{user_id}'}</span></div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Syntax Guide */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Code className="h-5 w-5 text-blue-500" />
                    <h3 className="text-lg font-semibold">Syntax Guide</h3>
                  </div>
                  <div className="grid gap-4">
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">Basic Syntax</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <code className="bg-muted px-2 py-1 rounded text-sm">{'{variable_name}'}</code>
                            <span className="text-sm text-muted-foreground">New template variable syntax</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <code className="bg-muted px-2 py-1 rounded text-sm">{'${variable_name}'}</code>
                            <span className="text-sm text-muted-foreground">Existing workflow variable syntax</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">Variable Names</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <code className="text-sm">{'{api_key}'}</code>
                            <span className="text-sm text-muted-foreground">Valid</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <code className="text-sm">{'{user_name_123}'}</code>
                            <span className="text-sm text-muted-foreground">Valid</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-red-500" />
                            <code className="text-sm">{'{123_invalid}'}</code>
                            <span className="text-sm text-muted-foreground">Invalid - starts with number</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-red-500" />
                            <code className="text-sm">{'{api-key}'}</code>
                            <span className="text-sm text-muted-foreground">Invalid - contains hyphen</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                {/* Best Practices */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-green-500" />
                    <h3 className="text-lg font-semibold">Best Practices</h3>
                  </div>
                  <div className="space-y-3">
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Naming Conventions</AlertTitle>
                      <AlertDescription>
                        Use descriptive, lowercase names with underscores: <code>api_base_url</code>, <code>database_host</code>, <code>user_email</code>
                      </AlertDescription>
                    </Alert>
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Organization</AlertTitle>
                      <AlertDescription>
                        Group related variables using categories and tags. Use consistent naming patterns within categories.
                      </AlertDescription>
                    </Alert>
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Documentation</AlertTitle>
                      <AlertDescription>
                        Always provide clear descriptions. Include expected format, examples, and any constraints.
                      </AlertDescription>
                    </Alert>
                  </div>
                </div>

                {/* Common Use Cases */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Variable className="h-5 w-5 text-purple-500" />
                    <h3 className="text-lg font-semibold">Common Use Cases</h3>
                  </div>
                  <div className="grid gap-4">
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">API Configuration</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2 font-mono text-sm">
                          <div><span className="text-blue-600">{'{api_base_url}'}</span> - Base URL for API endpoints</div>
                          <div><span className="text-blue-600">{'{api_key}'}</span> - Authentication key</div>
                          <div><span className="text-blue-600">{'{api_version}'}</span> - API version (e.g., v1, v2)</div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">Database Configuration</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2 font-mono text-sm">
                          <div><span className="text-blue-600">{'{db_host}'}</span> - Database server hostname</div>
                          <div><span className="text-blue-600">{'{db_port}'}</span> - Database port number</div>
                          <div><span className="text-blue-600">{'{db_name}'}</span> - Database name</div>
                          <div><span className="text-blue-600">{'{db_user}'}</span> - Database username</div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">File Operations</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2 font-mono text-sm">
                          <div><span className="text-blue-600">{'{input_file_path}'}</span> - Path to input file</div>
                          <div><span className="text-blue-600">{'{output_directory}'}</span> - Output directory path</div>
                          <div><span className="text-blue-600">{'{file_format}'}</span> - File format (csv, json, xml)</div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </CustomScrollArea>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
