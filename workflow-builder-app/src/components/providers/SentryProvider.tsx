"use client";

import { useEffect } from "react";
import "../../../sentry.client.init"; // Import the Sentry client initialization

// This component ensures Sen<PERSON> is initialized on the client-side.
// It's a client component because it uses `useEffect` and `Sentry.init`
// which are client-side operations.
export function SentryProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    console.log("Sentry client-side initialized via SentryProvider.");
  }, []);

  return <>{children}</>;
}
