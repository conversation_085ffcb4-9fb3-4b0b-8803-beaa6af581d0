/**
 * Template Variable Input Component Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TemplateVariableInput, useTemplateVariableInput } from '../template-variable-input';

// Mock the template variable utilities
jest.mock('@/utils/templateVariables', () => ({
  detectTemplateVariables: jest.fn(),
  validateTemplateVariables: jest.fn(),
  getVariableSuggestions: jest.fn(),
  insertTemplateVariable: jest.fn(),
  highlightTemplateVariables: jest.fn(),
  hasTemplateVariables: jest.fn(),
}));

const mockTemplateVariables = require('@/utils/templateVariables');

describe('TemplateVariableInput', () => {
  const defaultProps = {
    value: '',
    onChange: jest.fn(),
    placeholder: 'Enter text...',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock implementations
    mockTemplateVariables.validateTemplateVariables.mockReturnValue({
      isValid: true,
      message: 'Valid',
      variables: [],
      warnings: []
    });
    
    mockTemplateVariables.getVariableSuggestions.mockReturnValue([]);
    mockTemplateVariables.highlightTemplateVariables.mockReturnValue([
      { text: '', isVariable: false }
    ]);
    mockTemplateVariables.hasTemplateVariables.mockReturnValue(false);
  });

  describe('Basic Rendering', () => {
    it('should render input field', () => {
      render(<TemplateVariableInput {...defaultProps} />);
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should render textarea when multiline is true', () => {
      render(<TemplateVariableInput {...defaultProps} multiline={true} />);
      expect(screen.getByRole('textbox')).toBeInTheDocument();
      // Note: textarea also has role="textbox" in testing library
    });

    it('should display placeholder text', () => {
      render(<TemplateVariableInput {...defaultProps} placeholder="Test placeholder" />);
      expect(screen.getByPlaceholderText('Test placeholder')).toBeInTheDocument();
    });

    it('should be disabled when disabled prop is true', () => {
      render(<TemplateVariableInput {...defaultProps} disabled={true} />);
      expect(screen.getByRole('textbox')).toBeDisabled();
    });
  });

  describe('Value Handling', () => {
    it('should display the provided value', () => {
      render(<TemplateVariableInput {...defaultProps} value="Hello world" />);
      expect(screen.getByDisplayValue('Hello world')).toBeInTheDocument();
    });

    it('should call onChange when value changes', async () => {
      const user = userEvent.setup();
      const onChange = jest.fn();
      
      render(<TemplateVariableInput {...defaultProps} onChange={onChange} />);
      
      const input = screen.getByRole('textbox');
      await user.type(input, 'test');
      
      expect(onChange).toHaveBeenCalled();
    });
  });

  describe('Template Variable Validation', () => {
    it('should validate template variables when showValidation is true', () => {
      mockTemplateVariables.validateTemplateVariables.mockReturnValue({
        isValid: false,
        message: 'Invalid variable',
        variables: [],
        warnings: []
      });

      render(
        <TemplateVariableInput 
          {...defaultProps} 
          value="{invalid_var}" 
          showValidation={true} 
        />
      );

      expect(mockTemplateVariables.validateTemplateVariables).toHaveBeenCalledWith('{invalid_var}');
    });

    it('should display validation errors', () => {
      mockTemplateVariables.validateTemplateVariables.mockReturnValue({
        isValid: false,
        message: 'Invalid variable name',
        variables: [],
        warnings: []
      });

      render(
        <TemplateVariableInput 
          {...defaultProps} 
          value="{invalid}" 
          showValidation={true} 
        />
      );

      expect(screen.getByText('Invalid variable name')).toBeInTheDocument();
    });

    it('should display validation warnings', () => {
      mockTemplateVariables.validateTemplateVariables.mockReturnValue({
        isValid: true,
        message: 'Valid',
        variables: [{ name: 'test', syntax: '{variable_name}', startPos: 0, endPos: 6, fullMatch: '{test}' }],
        warnings: ['Mixed syntax warning']
      });

      render(
        <TemplateVariableInput 
          {...defaultProps} 
          value="{test}" 
          showValidation={true} 
        />
      );

      expect(screen.getByText('Mixed syntax warning')).toBeInTheDocument();
    });

    it('should display variable count for valid variables', () => {
      mockTemplateVariables.validateTemplateVariables.mockReturnValue({
        isValid: true,
        message: 'Valid',
        variables: [
          { name: 'var1', syntax: '{variable_name}', startPos: 0, endPos: 6, fullMatch: '{var1}' },
          { name: 'var2', syntax: '{variable_name}', startPos: 7, endPos: 13, fullMatch: '{var2}' }
        ],
        warnings: []
      });

      render(
        <TemplateVariableInput 
          {...defaultProps} 
          value="{var1} {var2}" 
          showValidation={true} 
        />
      );

      expect(screen.getByText('2 template variables detected')).toBeInTheDocument();
    });
  });

  describe('Variable Suggestions', () => {
    it('should show suggestions when available', async () => {
      mockTemplateVariables.getVariableSuggestions.mockReturnValue(['user_name', 'user_email']);

      const user = userEvent.setup();
      render(
        <TemplateVariableInput 
          {...defaultProps} 
          showSuggestions={true}
          availableVariables={['user_name', 'user_email']}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, '{{user');

      await waitFor(() => {
        expect(screen.getByText('{user_name}')).toBeInTheDocument();
        expect(screen.getByText('{user_email}')).toBeInTheDocument();
      });
    });

    it('should handle suggestion selection', async () => {
      mockTemplateVariables.getVariableSuggestions.mockReturnValue(['user_name']);
      mockTemplateVariables.insertTemplateVariable.mockReturnValue({
        newValue: 'Hello {user_name}',
        newCursorPosition: 17
      });

      const user = userEvent.setup();
      const onChange = jest.fn();
      
      render(
        <TemplateVariableInput 
          {...defaultProps} 
          onChange={onChange}
          showSuggestions={true}
          availableVariables={['user_name']}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, '{{user');

      await waitFor(() => {
        const suggestion = screen.getByText('{user_name}');
        expect(suggestion).toBeInTheDocument();
      });

      const suggestion = screen.getByText('{user_name}');
      await user.click(suggestion);

      expect(mockTemplateVariables.insertTemplateVariable).toHaveBeenCalled();
      expect(onChange).toHaveBeenCalledWith('Hello {user_name}');
    });
  });

  describe('Variable Indicators', () => {
    it('should display variable badges when variables are detected', () => {
      mockTemplateVariables.validateTemplateVariables.mockReturnValue({
        isValid: true,
        message: 'Valid',
        variables: [
          { name: 'user_name', syntax: '{variable_name}', startPos: 0, endPos: 11, fullMatch: '{user_name}' }
        ],
        warnings: []
      });

      render(
        <TemplateVariableInput 
          {...defaultProps} 
          value="{user_name}" 
          showValidation={true} 
        />
      );

      expect(screen.getByText('{user_name}')).toBeInTheDocument();
    });
  });

  describe('Keyboard Interactions', () => {
    it('should handle Escape key to close suggestions', async () => {
      mockTemplateVariables.getVariableSuggestions.mockReturnValue(['user_name']);

      const user = userEvent.setup();
      render(
        <TemplateVariableInput 
          {...defaultProps} 
          showSuggestions={true}
          availableVariables={['user_name']}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, '{{user');

      await waitFor(() => {
        expect(screen.getByText('{user_name}')).toBeInTheDocument();
      });

      await user.keyboard('{Escape}');

      await waitFor(() => {
        expect(screen.queryByText('{user_name}')).not.toBeInTheDocument();
      });
    });

    it('should handle Tab key to select first suggestion', async () => {
      mockTemplateVariables.getVariableSuggestions.mockReturnValue(['user_name']);
      mockTemplateVariables.insertTemplateVariable.mockReturnValue({
        newValue: 'Hello {user_name}',
        newCursorPosition: 17
      });

      const user = userEvent.setup();
      const onChange = jest.fn();
      
      render(
        <TemplateVariableInput 
          {...defaultProps} 
          onChange={onChange}
          showSuggestions={true}
          availableVariables={['user_name']}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, '{{user');

      await waitFor(() => {
        expect(screen.getByText('{user_name}')).toBeInTheDocument();
      });

      await user.keyboard('{Tab}');

      expect(mockTemplateVariables.insertTemplateVariable).toHaveBeenCalled();
    });
  });
});

describe('useTemplateVariableInput', () => {
  it('should initialize with provided value', () => {
    const TestComponent = () => {
      const { value } = useTemplateVariableInput('initial value');
      return <div data-testid="value">{value}</div>;
    };

    render(<TestComponent />);
    expect(screen.getByTestId('value')).toHaveTextContent('initial value');
  });

  it('should validate template variables', () => {
    mockTemplateVariables.validateTemplateVariables.mockReturnValue({
      isValid: true,
      message: 'Valid',
      variables: [{ name: 'test', syntax: '{variable_name}', startPos: 0, endPos: 6, fullMatch: '{test}' }],
      warnings: []
    });

    const TestComponent = () => {
      const { validation, hasVariables, isValid } = useTemplateVariableInput('{test}');
      return (
        <div>
          <div data-testid="has-variables">{hasVariables.toString()}</div>
          <div data-testid="is-valid">{isValid.toString()}</div>
        </div>
      );
    };

    render(<TestComponent />);
    expect(screen.getByTestId('has-variables')).toHaveTextContent('true');
    expect(screen.getByTestId('is-valid')).toHaveTextContent('true');
  });
});
