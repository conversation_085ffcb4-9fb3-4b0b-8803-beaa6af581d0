import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TemplateVariableDisplay, useTemplateVariableDisplay } from '../template-variable-display';

// Mock the template variables utility functions
jest.mock('@/utils/templateVariables', () => ({
  highlightTemplateVariables: jest.fn((text: string) => {
    // Simple mock implementation for highlighting
    const segments = [];
    const regex = /(\{[^}]+\}|\$\{[^}]+\})/g;
    let lastIndex = 0;
    let match;
    
    while ((match = regex.exec(text)) !== null) {
      if (match.index > lastIndex) {
        segments.push({ text: text.slice(lastIndex, match.index), isVariable: false });
      }
      segments.push({ text: match[0], isVariable: true });
      lastIndex = match.index + match[0].length;
    }
    
    if (lastIndex < text.length) {
      segments.push({ text: text.slice(lastIndex), isVariable: false });
    }
    
    return segments;
  }),
  detectTemplateVariables: jest.fn((text: string) => {
    const matches = text.match(/(\{[^}]+\}|\$\{[^}]+\})/g) || [];
    return matches.map(match => ({ match, start: 0, end: match.length }));
  }),
  extractVariableNames: jest.fn((text: string) => {
    const matches = text.match(/\{([^}]+)\}|\$\{([^}]+)\}/g) || [];
    return matches.map(match => match.replace(/[{}$]/g, ''));
  }),
}));

describe('TemplateVariableDisplay', () => {
  const mockAvailableVariables = [
    { name: 'user_name', value: 'John Doe', description: 'Current user name' },
    { name: 'api_key', value: 'secret123', description: 'API key for authentication' },
    { name: 'undefined_var', description: 'Variable without value' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render text without template variables', () => {
      render(
        <TemplateVariableDisplay 
          value="Hello world" 
          showHighlighting={true}
        />
      );
      
      expect(screen.getByText('Hello world')).toBeInTheDocument();
    });

    it('should not render anything for empty value', () => {
      const { container } = render(
        <TemplateVariableDisplay value="" />
      );
      
      expect(container.firstChild).toBeNull();
    });

    it('should render with custom className', () => {
      const { container } = render(
        <TemplateVariableDisplay 
          value="test" 
          className="custom-class"
        />
      );
      
      expect(container.firstChild).toHaveClass('custom-class');
    });
  });

  describe('Template Variable Highlighting', () => {
    it('should highlight template variables when showHighlighting is true', () => {
      render(
        <TemplateVariableDisplay 
          value="Hello {user_name}, your key is ${api_key}"
          availableVariables={mockAvailableVariables}
          showHighlighting={true}
        />
      );
      
      expect(screen.getByText('{user_name}')).toBeInTheDocument();
      expect(screen.getByText('${api_key}')).toBeInTheDocument();
    });

    it('should not highlight when showHighlighting is false', () => {
      render(
        <TemplateVariableDisplay 
          value="Hello {user_name}"
          showHighlighting={false}
        />
      );
      
      expect(screen.getByText('Hello {user_name}')).toBeInTheDocument();
    });

    it('should show different styles for defined vs undefined variables', () => {
      render(
        <TemplateVariableDisplay 
          value="Defined: {user_name}, Undefined: {missing_var}"
          availableVariables={mockAvailableVariables}
          showHighlighting={true}
        />
      );
      
      const definedVar = screen.getByText('{user_name}');
      const undefinedVar = screen.getByText('{missing_var}');
      
      expect(definedVar).toHaveClass('text-green-700');
      expect(undefinedVar).toHaveClass('text-red-700');
    });
  });

  describe('Preview Functionality', () => {
    it('should show preview toggle button when showPreview is true and variables exist', () => {
      render(
        <TemplateVariableDisplay 
          value="Hello {user_name}"
          availableVariables={mockAvailableVariables}
          showPreview={true}
        />
      );
      
      const toggleButton = screen.getByRole('button');
      expect(toggleButton).toBeInTheDocument();
    });

    it('should not show preview toggle when no variables exist', () => {
      render(
        <TemplateVariableDisplay 
          value="Hello world"
          showPreview={true}
        />
      );
      
      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('should toggle between normal and preview mode', async () => {
      const user = userEvent.setup();
      
      render(
        <TemplateVariableDisplay 
          value="Hello {user_name}"
          availableVariables={mockAvailableVariables}
          showPreview={true}
        />
      );
      
      const toggleButton = screen.getByRole('button');
      
      // Initially should show template variable
      expect(screen.getByText('{user_name}')).toBeInTheDocument();
      
      // Click to show preview
      await user.click(toggleButton);
      
      // Should show resolved value
      await waitFor(() => {
        expect(screen.getByText('Hello John Doe')).toBeInTheDocument();
      });
    });
  });

  describe('Variable Indicators', () => {
    it('should show variable badges when showIndicators is true', () => {
      render(
        <TemplateVariableDisplay 
          value="Hello {user_name} and {api_key}"
          availableVariables={mockAvailableVariables}
          showIndicators={true}
        />
      );
      
      expect(screen.getByText('user_name')).toBeInTheDocument();
      expect(screen.getByText('api_key')).toBeInTheDocument();
    });

    it('should not show indicators when showIndicators is false', () => {
      render(
        <TemplateVariableDisplay 
          value="Hello {user_name}"
          availableVariables={mockAvailableVariables}
          showIndicators={false}
        />
      );
      
      // Should not find badge with just variable name
      expect(screen.queryByText('user_name')).not.toBeInTheDocument();
    });

    it('should not show indicators in compact mode', () => {
      render(
        <TemplateVariableDisplay 
          value="Hello {user_name}"
          availableVariables={mockAvailableVariables}
          showIndicators={true}
          compact={true}
        />
      );
      
      expect(screen.queryByText('user_name')).not.toBeInTheDocument();
    });
  });

  describe('Variable Click Handling', () => {
    it('should call onVariableClick when a defined variable is clicked', async () => {
      const user = userEvent.setup();
      const mockOnVariableClick = jest.fn();
      
      render(
        <TemplateVariableDisplay 
          value="Hello {user_name}"
          availableVariables={mockAvailableVariables}
          onVariableClick={mockOnVariableClick}
        />
      );
      
      const variableElement = screen.getByText('{user_name}');
      await user.click(variableElement);
      
      expect(mockOnVariableClick).toHaveBeenCalledWith('user_name');
    });

    it('should call onVariableClick when variable badge is clicked', async () => {
      const user = userEvent.setup();
      const mockOnVariableClick = jest.fn();
      
      render(
        <TemplateVariableDisplay 
          value="Hello {user_name}"
          availableVariables={mockAvailableVariables}
          showIndicators={true}
          onVariableClick={mockOnVariableClick}
        />
      );
      
      const badge = screen.getByText('user_name');
      await user.click(badge);
      
      expect(mockOnVariableClick).toHaveBeenCalledWith('user_name');
    });
  });

  describe('Undefined Variables Warning', () => {
    it('should show warning for single undefined variable', () => {
      render(
        <TemplateVariableDisplay 
          value="Hello {missing_var}"
          availableVariables={mockAvailableVariables}
        />
      );
      
      expect(screen.getByText('Variable "missing_var" is not defined')).toBeInTheDocument();
    });

    it('should show warning for multiple undefined variables', () => {
      render(
        <TemplateVariableDisplay 
          value="Hello {missing1} and {missing2}"
          availableVariables={mockAvailableVariables}
        />
      );
      
      expect(screen.getByText('2 variables are not defined')).toBeInTheDocument();
    });

    it('should not show warning in compact mode', () => {
      render(
        <TemplateVariableDisplay 
          value="Hello {missing_var}"
          availableVariables={mockAvailableVariables}
          compact={true}
        />
      );
      
      expect(screen.queryByText(/not defined/)).not.toBeInTheDocument();
    });
  });

  describe('Max Lines Handling', () => {
    it('should apply maxLines styling when specified', () => {
      const { container } = render(
        <TemplateVariableDisplay 
          value="Line 1\nLine 2\nLine 3\nLine 4\nLine 5"
          maxLines={3}
        />
      );
      
      const contentDiv = container.querySelector('.overflow-hidden');
      expect(contentDiv).toHaveStyle({ maxHeight: '4.5em' });
    });
  });
});

describe('useTemplateVariableDisplay', () => {
  const TestComponent = ({ value, availableVariables }: any) => {
    const { hasVariables, variableNames, undefinedVariables, isValid } = 
      useTemplateVariableDisplay(value, availableVariables);
    
    return (
      <div>
        <div data-testid="hasVariables">{hasVariables.toString()}</div>
        <div data-testid="variableNames">{variableNames.join(',')}</div>
        <div data-testid="undefinedVariables">{undefinedVariables.join(',')}</div>
        <div data-testid="isValid">{isValid.toString()}</div>
      </div>
    );
  };

  it('should detect variables correctly', () => {
    render(
      <TestComponent 
        value="Hello {user_name} and ${api_key}"
        availableVariables={[{ name: 'user_name' }, { name: 'api_key' }]}
      />
    );
    
    expect(screen.getByTestId('hasVariables')).toHaveTextContent('true');
    expect(screen.getByTestId('variableNames')).toHaveTextContent('user_name,api_key');
    expect(screen.getByTestId('undefinedVariables')).toHaveTextContent('');
    expect(screen.getByTestId('isValid')).toHaveTextContent('true');
  });

  it('should identify undefined variables', () => {
    render(
      <TestComponent 
        value="Hello {user_name} and {missing_var}"
        availableVariables={[{ name: 'user_name' }]}
      />
    );
    
    expect(screen.getByTestId('undefinedVariables')).toHaveTextContent('missing_var');
    expect(screen.getByTestId('isValid')).toHaveTextContent('false');
  });

  it('should handle text without variables', () => {
    render(
      <TestComponent 
        value="Hello world"
        availableVariables={[]}
      />
    );
    
    expect(screen.getByTestId('hasVariables')).toHaveTextContent('false');
    expect(screen.getByTestId('variableNames')).toHaveTextContent('');
    expect(screen.getByTestId('isValid')).toHaveTextContent('true');
  });
});
