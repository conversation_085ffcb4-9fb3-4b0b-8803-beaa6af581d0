/**
 * Template Variable Display Component
 * 
 * Enhanced display component for template variables that provides:
 * - Syntax highlighting for {variable_name} and ${variable_name}
 * - Preview/evaluation functionality
 * - Visual indicators for defined/undefined variables
 * - Proper handling of mixed content (text + template variables)
 */

import React, { useMemo, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Eye, EyeOff, AlertTriangle, CheckCircle } from 'lucide-react';
import {
  highlightTemplateVariables,
  detectTemplateVariables,
  extractVariableNames,
  type TextSegment
} from '@/utils/templateVariables';

export interface TemplateVariableDisplayProps {
  /** The text content containing template variables */
  value: string;
  /** Available template variables for validation and preview */
  availableVariables?: Array<{
    name: string;
    value?: string;
    description?: string;
    category?: string;
  }>;
  /** Whether to show syntax highlighting */
  showHighlighting?: boolean;
  /** Whether to show preview/evaluation functionality */
  showPreview?: boolean;
  /** Whether to show variable indicators (badges) */
  showIndicators?: boolean;
  /** Maximum number of lines to display before truncating */
  maxLines?: number;
  /** Custom CSS classes */
  className?: string;
  /** Whether the display is in compact mode */
  compact?: boolean;
  /** Callback when a variable is clicked */
  onVariableClick?: (variableName: string) => void;
}

/**
 * Template Variable Display Component
 */
export function TemplateVariableDisplay({
  value = '',
  availableVariables = [],
  showHighlighting = true,
  showPreview = false,
  showIndicators = true,
  maxLines,
  className,
  compact = false,
  onVariableClick,
}: TemplateVariableDisplayProps) {
  const [showPreviewMode, setShowPreviewMode] = React.useState(false);

  // Memoize the text segments for highlighting
  const textSegments = useMemo(() => {
    if (!showHighlighting || !value) return [];
    return highlightTemplateVariables(value);
  }, [value, showHighlighting]);

  // Memoize detected variables
  const detectedVariables = useMemo(() => {
    return extractVariableNames(value);
  }, [value]);

  // Memoize variable status (defined/undefined)
  const variableStatus = useMemo(() => {
    const availableNames = new Set(availableVariables.map(v => v.name));
    return detectedVariables.reduce((acc, varName) => {
      acc[varName] = {
        isDefined: availableNames.has(varName),
        variable: availableVariables.find(v => v.name === varName)
      };
      return acc;
    }, {} as Record<string, { isDefined: boolean; variable?: any }>);
  }, [detectedVariables, availableVariables]);

  // Memoize preview text (with variable substitution)
  const previewText = useMemo(() => {
    if (!showPreview || !showPreviewMode) return value;
    
    let result = value;
    availableVariables.forEach(variable => {
      if (variable.value !== undefined) {
        const patterns = [
          new RegExp(`\\{${variable.name}\\}`, 'g'),
          new RegExp(`\\$\\{${variable.name}\\}`, 'g')
        ];
        patterns.forEach(pattern => {
          result = result.replace(pattern, String(variable.value));
        });
      }
    });
    return result;
  }, [value, availableVariables, showPreview, showPreviewMode]);

  // Handle variable click
  const handleVariableClick = useCallback((variableName: string) => {
    onVariableClick?.(variableName);
  }, [onVariableClick]);

  // Render highlighted text segments
  const renderHighlightedText = () => {
    if (!showHighlighting || textSegments.length === 0) {
      return <span className="whitespace-pre-wrap">{value}</span>;
    }

    return (
      <span className="whitespace-pre-wrap">
        {textSegments.map((segment, index) => {
          if (segment.isVariable) {
            const varName = segment.text.replace(/[{}$]/g, '');
            const status = variableStatus[varName];
            const isClickable = onVariableClick && status?.isDefined;

            return (
              <TooltipProvider key={index}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span
                      className={cn(
                        "inline-flex items-center px-1 py-0.5 rounded text-xs font-mono",
                        "border transition-colors",
                        status?.isDefined
                          ? "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800"
                          : "bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800",
                        isClickable && "cursor-pointer hover:bg-opacity-80",
                        compact && "px-0.5 py-0"
                      )}
                      onClick={isClickable ? () => handleVariableClick(varName) : undefined}
                    >
                      {segment.text}
                      {!compact && (
                        <span className="ml-1">
                          {status?.isDefined ? (
                            <CheckCircle className="w-3 h-3" />
                          ) : (
                            <AlertTriangle className="w-3 h-3" />
                          )}
                        </span>
                      )}
                    </span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-xs">
                      <div className="font-medium">{varName}</div>
                      {status?.variable?.description && (
                        <div className="text-muted-foreground mt-1">
                          {status.variable.description}
                        </div>
                      )}
                      {status?.variable?.value !== undefined && (
                        <div className="mt-1">
                          <span className="text-muted-foreground">Value: </span>
                          <span className="font-mono">{String(status.variable.value)}</span>
                        </div>
                      )}
                      {!status?.isDefined && (
                        <div className="text-red-500 mt-1">Variable not defined</div>
                      )}
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            );
          }
          return <span key={index}>{segment.text}</span>;
        })}
      </span>
    );
  };

  // Don't render anything if no value
  if (!value) {
    return null;
  }

  const hasVariables = detectedVariables.length > 0;
  const undefinedVariables = detectedVariables.filter(name => !variableStatus[name]?.isDefined);

  return (
    <div className={cn("space-y-2", className)}>
      {/* Main content display */}
      <div
        className={cn(
          "relative p-2 rounded-md border bg-muted/30",
          maxLines && "overflow-hidden",
          compact && "p-1 text-xs"
        )}
        style={maxLines ? { 
          maxHeight: `${maxLines * 1.5}em`,
          lineHeight: '1.5em'
        } : undefined}
      >
        {showPreview && showPreviewMode ? (
          <span className="whitespace-pre-wrap text-muted-foreground">
            {previewText}
          </span>
        ) : (
          renderHighlightedText()
        )}
        
        {/* Preview toggle button */}
        {showPreview && hasVariables && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-1 right-1 h-6 w-6 p-0"
            onClick={() => setShowPreviewMode(!showPreviewMode)}
          >
            {showPreviewMode ? (
              <EyeOff className="w-3 h-3" />
            ) : (
              <Eye className="w-3 h-3" />
            )}
          </Button>
        )}
      </div>

      {/* Variable indicators */}
      {showIndicators && hasVariables && !compact && (
        <div className="flex flex-wrap gap-1">
          {detectedVariables.map((varName) => {
            const status = variableStatus[varName];
            return (
              <Badge
                key={varName}
                variant={status?.isDefined ? "default" : "destructive"}
                className="text-xs cursor-pointer"
                onClick={() => handleVariableClick(varName)}
              >
                {varName}
                {status?.isDefined ? (
                  <CheckCircle className="w-3 h-3 ml-1" />
                ) : (
                  <AlertTriangle className="w-3 h-3 ml-1" />
                )}
              </Badge>
            );
          })}
        </div>
      )}

      {/* Warning for undefined variables */}
      {undefinedVariables.length > 0 && !compact && (
        <div className="flex items-center gap-2 text-xs text-amber-600 dark:text-amber-400">
          <AlertTriangle className="w-4 h-4" />
          <span>
            {undefinedVariables.length === 1 
              ? `Variable "${undefinedVariables[0]}" is not defined`
              : `${undefinedVariables.length} variables are not defined`
            }
          </span>
        </div>
      )}
    </div>
  );
}

/**
 * Hook for using template variable display functionality
 */
export function useTemplateVariableDisplay(value: string, availableVariables: any[] = []) {
  const hasVariables = useMemo(() => {
    return detectTemplateVariables(value).length > 0;
  }, [value]);

  const variableNames = useMemo(() => {
    return extractVariableNames(value);
  }, [value]);

  const undefinedVariables = useMemo(() => {
    const availableNames = new Set(availableVariables.map(v => v.name));
    return variableNames.filter(name => !availableNames.has(name));
  }, [variableNames, availableVariables]);

  return {
    hasVariables,
    variableNames,
    undefinedVariables,
    isValid: undefinedVariables.length === 0
  };
}
