/**
 * Template Variable Input Component
 * 
 * Enhanced input component that provides template variable support including:
 * - Syntax highlighting for {variable_name} and ${variable_name}
 * - Real-time validation
 * - Autocomplete suggestions
 * - Visual indicators
 */

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, Lightbulb } from 'lucide-react';
import {
  detectTemplateVariables,
  validateTemplateVariables,
  getVariableSuggestions,
  insertTemplateVariable,
  highlightTemplateVariables,
  type TemplateVariableValidation,
  type TextSegment
} from '@/utils/templateVariables';

export interface TemplateVariableInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  multiline?: boolean;
  rows?: number;
  className?: string;
  availableVariables?: string[];
  showValidation?: boolean;
  showSuggestions?: boolean;
  id?: string;
}

/**
 * Template Variable Input Component
 */
export function TemplateVariableInput({
  value = '',
  onChange,
  placeholder,
  disabled = false,
  multiline = false,
  rows = 3,
  className,
  availableVariables = [],
  showValidation = true,
  showSuggestions = true,
  id,
}: TemplateVariableInputProps) {
  const [validation, setValidation] = useState<TemplateVariableValidation | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestionsList, setShowSuggestionsList] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);

  // Validate template variables when value changes
  useEffect(() => {
    if (showValidation && value) {
      const validationResult = validateTemplateVariables(value);
      setValidation(validationResult);
    } else {
      setValidation(null);
    }
  }, [value, showValidation]);

  // Handle input change
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    onChange(newValue);

    // Update cursor position
    setCursorPosition(e.target.selectionStart || 0);

    // Update suggestions if enabled
    if (showSuggestions && availableVariables.length > 0) {
      const newSuggestions = getVariableSuggestions(newValue, availableVariables);
      setSuggestions(newSuggestions);
      setShowSuggestionsList(newSuggestions.length > 0);
    }
  }, [onChange, showSuggestions, availableVariables]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((variableName: string) => {
    if (!inputRef.current) return;

    const currentPos = inputRef.current.selectionStart || 0;
    const { newValue, newCursorPosition } = insertTemplateVariable(
      value,
      variableName,
      currentPos,
      false // Use {variable} syntax by default
    );

    onChange(newValue);
    setShowSuggestionsList(false);

    // Set cursor position after insertion
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
        inputRef.current.focus();
      }
    }, 0);
  }, [value, onChange]);

  // Handle key events
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (showSuggestionsList && suggestions.length > 0) {
      if (e.key === 'Escape') {
        setShowSuggestionsList(false);
        e.preventDefault();
      } else if (e.key === 'Tab' && suggestions.length > 0) {
        handleSuggestionSelect(suggestions[0]);
        e.preventDefault();
      }
    }
  }, [showSuggestionsList, suggestions, handleSuggestionSelect]);

  // Handle focus events
  const handleFocus = useCallback(() => {
    if (showSuggestions && availableVariables.length > 0) {
      const currentSuggestions = getVariableSuggestions(value, availableVariables);
      if (currentSuggestions.length > 0) {
        setSuggestions(currentSuggestions);
        setShowSuggestionsList(true);
      }
    }
  }, [showSuggestions, availableVariables, value]);

  const handleBlur = useCallback(() => {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => setShowSuggestionsList(false), 150);
  }, []);

  // Render highlighted text for display (when not focused)
  const renderHighlightedText = useCallback(() => {
    if (!value) return null;

    const segments = highlightTemplateVariables(value);
    return (
      <div className="absolute inset-0 pointer-events-none p-3 text-transparent whitespace-pre-wrap break-words">
        {segments.map((segment, index) => (
          <span
            key={index}
            className={cn(
              segment.isVariable && 'bg-blue-100 text-blue-800 rounded px-1',
              segment.isVariable && 'dark:bg-blue-900 dark:text-blue-200'
            )}
          >
            {segment.text}
          </span>
        ))}
      </div>
    );
  }, [value]);

  // Get validation status
  const hasVariables = validation?.variables.length > 0;
  const hasErrors = validation && !validation.isValid;
  const hasWarnings = validation?.warnings.length > 0;

  const inputProps = {
    ref: inputRef as any,
    id,
    value,
    onChange: handleChange,
    onKeyDown: handleKeyDown,
    onFocus: handleFocus,
    onBlur: handleBlur,
    placeholder,
    disabled,
    className: cn(
      'relative z-10 bg-transparent',
      hasErrors && 'border-red-500 focus:border-red-500',
      hasWarnings && !hasErrors && 'border-yellow-500 focus:border-yellow-500',
      hasVariables && !hasErrors && !hasWarnings && 'border-blue-500 focus:border-blue-500',
      className
    ),
  };

  return (
    <div className="relative">
      {/* Input/Textarea with highlighting overlay */}
      <div className="relative">
        {multiline ? (
          <Textarea {...inputProps} rows={rows} />
        ) : (
          <Input {...inputProps} />
        )}
        {/* Highlighting overlay - only show when not focused */}
        {/* {renderHighlightedText()} */}
      </div>

      {/* Template variable indicators */}
      {hasVariables && (
        <div className="flex flex-wrap gap-1 mt-1">
          {validation.variables.map((variable, index) => (
            <Badge
              key={index}
              variant="secondary"
              className="text-xs h-5 px-1.5"
            >
              {variable.fullMatch}
            </Badge>
          ))}
        </div>
      )}

      {/* Validation messages */}
      {showValidation && validation && (
        <div className="mt-1 space-y-1">
          {hasErrors && (
            <div className="flex items-center gap-1 text-xs text-red-600">
              <AlertCircle className="h-3 w-3" />
              <span>{validation.message}</span>
            </div>
          )}
          {hasWarnings && !hasErrors && (
            <div className="flex items-center gap-1 text-xs text-yellow-600">
              <Lightbulb className="h-3 w-3" />
              <span>{validation.warnings[0]}</span>
            </div>
          )}
          {!hasErrors && !hasWarnings && hasVariables && (
            <div className="flex items-center gap-1 text-xs text-green-600">
              <CheckCircle className="h-3 w-3" />
              <span>{validation.variables.length} template variable{validation.variables.length !== 1 ? 's' : ''} detected</span>
            </div>
          )}
        </div>
      )}

      {/* Suggestions dropdown */}
      {showSuggestionsList && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto">
          {suggestions.map((suggestion, index) => (
            <button
              key={index}
              className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
              onClick={() => handleSuggestionSelect(suggestion)}
            >
              <span className="font-mono text-blue-600">{`{${suggestion}}`}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

/**
 * Hook for template variable input functionality
 */
export function useTemplateVariableInput(
  initialValue: string = '',
  availableVariables: string[] = []
) {
  const [value, setValue] = useState(initialValue);
  const [validation, setValidation] = useState<TemplateVariableValidation | null>(null);

  useEffect(() => {
    if (value) {
      const validationResult = validateTemplateVariables(value);
      setValidation(validationResult);
    } else {
      setValidation(null);
    }
  }, [value]);

  const insertVariable = useCallback((variableName: string, cursorPos: number = value.length) => {
    const { newValue, newCursorPosition } = insertTemplateVariable(
      value,
      variableName,
      cursorPos,
      false
    );
    setValue(newValue);
    return newCursorPosition;
  }, [value]);

  return {
    value,
    setValue,
    validation,
    insertVariable,
    hasVariables: validation?.variables.length > 0,
    isValid: validation?.isValid !== false,
  };
}
