'use client';

import React, { useState, useEffect } from 'react';
import { fetchProviders, fetchModels, fetchCredentials, Provider, Model, Credential } from '@/lib/api';

export default function TestAppDataPage() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [credentials, setCredentials] = useState<Credential[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🚀 Loading all application data...');
      
      const [providersResponse, modelsResponse, credentialsResponse] = await Promise.all([
        fetchProviders(),
        fetchModels(),
        fetchCredentials(),
      ]);

      console.log('Providers response:', providersResponse);
      console.log('Models response:', modelsResponse);
      console.log('Credentials response:', credentialsResponse);

      if (providersResponse.success) {
        setProviders(providersResponse.providers);
        console.log(`✅ Loaded ${providersResponse.providers.length} providers`);
      } else {
        console.warn('Failed to fetch providers:', providersResponse.message);
        setProviders([]);
      }

      if (modelsResponse.success) {
        setModels(modelsResponse.models);
        console.log(`✅ Loaded ${modelsResponse.models.length} models`);
      } else {
        console.warn('Failed to fetch models:', modelsResponse.message);
        setModels([]);
      }

      if (credentialsResponse.success) {
        setCredentials(credentialsResponse.credentials);
        console.log(`✅ Loaded ${credentialsResponse.credentials.length} credentials`);
      } else {
        console.warn('Failed to fetch credentials:', credentialsResponse.message);
        setCredentials([]);
      }

      console.log('✅ All application data loaded successfully');

    } catch (err) {
      console.error('❌ Error loading application data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">Application Data Test</h1>
      
      <div className="mb-6">
        <button
          onClick={loadAllData}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Reload Data'}
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Error: {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Providers */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            🏢 Providers 
            <span className="ml-2 text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
              {providers.length}
            </span>
          </h2>
          
          {loading ? (
            <div className="text-gray-500">Loading providers...</div>
          ) : providers.length > 0 ? (
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {providers.map((provider) => (
                <div key={provider.id} className="border rounded p-3">
                  <div className="font-medium">{provider.provider}</div>
                  <div className="text-sm text-gray-600">{provider.description}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    ID: {provider.id}
                  </div>
                  <div className="text-xs text-gray-500">
                    Models: {provider.modelCount} | Active: {provider.isActive ? 'Yes' : 'No'}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500">No providers found</div>
          )}
        </div>

        {/* Models */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            🤖 Models 
            <span className="ml-2 text-sm bg-green-100 text-green-800 px-2 py-1 rounded">
              {models.length}
            </span>
          </h2>
          
          {loading ? (
            <div className="text-gray-500">Loading models...</div>
          ) : models.length > 0 ? (
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {models.slice(0, 10).map((model) => (
                <div key={model.id} className="border rounded p-3">
                  <div className="font-medium">{model.model}</div>
                  <div className="text-sm text-gray-600">{model.description}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    Provider: {model.provider.provider}
                  </div>
                  <div className="text-xs text-gray-500">
                    Type: {model.providerType} | Active: {model.isActive ? 'Yes' : 'No'}
                  </div>
                </div>
              ))}
              {models.length > 10 && (
                <div className="text-xs text-gray-500 text-center">
                  ... and {models.length - 10} more models
                </div>
              )}
            </div>
          ) : (
            <div className="text-gray-500">No models found</div>
          )}
        </div>

        {/* Credentials */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            🔑 Credentials 
            <span className="ml-2 text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded">
              {credentials.length}
            </span>
          </h2>
          
          {loading ? (
            <div className="text-gray-500">Loading credentials...</div>
          ) : credentials.length > 0 ? (
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {credentials.map((credential) => (
                <div key={credential.id} className="border rounded p-3">
                  <div className="font-medium">{credential.name}</div>
                  <div className="text-sm text-gray-600">{credential.description}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    Type: {credential.credentialType}
                  </div>
                  <div className="text-xs text-gray-500">
                    ID: {credential.id}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500">No credentials found</div>
          )}
        </div>
      </div>

      {/* Summary */}
      <div className="mt-6 bg-gray-50 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">📊 Summary</h2>
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-blue-600">{providers.length}</div>
            <div className="text-sm text-gray-600">Providers</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">{models.length}</div>
            <div className="text-sm text-gray-600">Models</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-600">{credentials.length}</div>
            <div className="text-sm text-gray-600">Credentials</div>
          </div>
        </div>
        
        <div className="mt-4 p-3 bg-blue-100 border border-blue-400 rounded">
          <p className="text-sm font-medium">🎯 Expected Behavior:</p>
          <ul className="text-sm mt-2 space-y-1">
            <li>• All three data types should load successfully</li>
            <li>• Data should be available for use in components</li>
            <li>• Check browser console for detailed logs</li>
            <li>• This simulates what happens in the workflow editor</li>
          </ul>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 bg-gray-50 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">🧪 Test Instructions</h2>
        <ol className="list-decimal list-inside space-y-2 text-sm">
          <li>Check that all three sections show data (providers, models, credentials)</li>
          <li>Open browser DevTools → Console to see loading logs</li>
          <li>Click "Reload Data" to test the loading process</li>
          <li>Verify that the same data loads in the workflow editor</li>
          <li>Check that API calls are made to the correct endpoints</li>
        </ol>
      </div>
    </div>
  );
}
