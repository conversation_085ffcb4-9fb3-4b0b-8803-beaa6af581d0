"use client";

import React, { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { Loader2, CheckCircle, XCircle, ArrowLeft } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { workflowsRoute } from "@/shared/routes";
import { storeOAuthSuccess, getAndClearOAuthReturnUrl } from "@/lib/oauthUtils";

// Force dynamic rendering to prevent prerendering issues with useSearchParams
export const dynamic = 'force-dynamic';

interface OAuthCallbackState {
  status: "loading" | "success" | "error";
  message: string;
  provider?: string;
  toolName?: string;
}

function OAuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [callbackState, setCallbackState] = useState<OAuthCallbackState>({
    status: "loading",
    message: "Processing OAuth callback...",
  });

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        // Extract parameters from URL (check both query params and hash)
        let code = searchParams.get("code");
        let error = searchParams.get("error");
        let errorDescription = searchParams.get("error_description");
        let provider = searchParams.get("provider");
        let toolName = searchParams.get("tool_name");

        // If no code in query params, check URL hash (some OAuth providers use hash)
        if (!code && window.location.hash) {
          const hashParams = new URLSearchParams(window.location.hash.substring(1));
          code = hashParams.get("code") || code;
          error = hashParams.get("error") || error;
          errorDescription = hashParams.get("error_description") || errorDescription;
          provider = hashParams.get("provider") || provider;
          toolName = hashParams.get("tool_name") || toolName;
        }

        console.log('🔍 OAuth callback received parameters:', {
          code: code ? `present (${code.substring(0, 10)}...)` : 'missing',
          error,
          errorDescription,
          provider,
          toolName,
          fullURL: window.location.href,
          queryParams: Object.fromEntries(searchParams.entries()),
          hashParams: window.location.hash ? Object.fromEntries(new URLSearchParams(window.location.hash.substring(1)).entries()) : {}
        });

        // Handle OAuth error responses
        if (error) {
          const errorMessage = errorDescription || `OAuth error: ${error}`;
          setCallbackState({
            status: "error",
            message: errorMessage,
            provider: provider || undefined,
            toolName: toolName || undefined,
          });
          toast.error(`OAuth connection failed: ${errorMessage}`);

          // Send error message to parent window (for new tab flow)
          if (window.opener) {
            window.opener.postMessage({
              type: 'OAUTH_ERROR',
              error: errorMessage,
            }, window.location.origin);

            // Close this tab after a short delay
            setTimeout(() => {
              window.close();
            }, 2000);
          }
          return;
        }

        // Handle missing authorization code
        if (!code) {
          // Check if this might be a successful OAuth flow without explicit code
          // Some OAuth flows might complete successfully but not provide a code in the callback
          const hasSuccessIndicators = window.location.href.includes('oauth/callback') &&
                                      !error &&
                                      (provider || toolName);

          if (hasSuccessIndicators) {
            console.log('🤔 OAuth callback without code but with success indicators, treating as success');

            // Store success data anyway
            const successData = {
              provider: provider || "unknown",
              toolName: toolName || "unknown",
              timestamp: Date.now(),
            };

            console.log('💾 Storing OAuth success data (fallback):', successData);
            storeOAuthSuccess(successData);

            // Send success message to parent window
            if (window.opener) {
              try {
                window.opener.postMessage({
                  type: 'OAUTH_SUCCESS',
                  provider: provider || "unknown",
                  toolName: toolName || "unknown",
                }, window.location.origin);
                console.log('✅ Success message sent to parent (fallback)');
              } catch (postError) {
                console.warn('Failed to send success message to parent:', postError);
              }
            }

            // Close window
            setTimeout(() => {
              try {
                window.close();
              } catch (closeError) {
                console.warn('Failed to close window:', closeError);
              }
            }, 1000);

            return;
          }

          const errorMessage = "No authorization code received from OAuth provider";
          console.error('❌ OAuth callback missing authorization code:', {
            fullURL: window.location.href,
            searchParams: Object.fromEntries(searchParams.entries()),
            hash: window.location.hash,
            search: window.location.search
          });

          setCallbackState({
            status: "error",
            message: errorMessage,
            provider: provider || undefined,
            toolName: toolName || undefined,
          });
          toast.error(`OAuth connection failed: ${errorMessage}`);

          // Send error message to parent window (for new tab flow)
          if (window.opener) {
            try {
              window.opener.postMessage({
                type: 'OAUTH_ERROR',
                error: errorMessage,
              }, window.location.origin);
            } catch (postError) {
              console.warn('Failed to send error message to parent:', postError);
            }

            // Close this tab after a short delay
            setTimeout(() => {
              try {
                window.close();
              } catch (closeError) {
                console.warn('Failed to close window:', closeError);
              }
            }, 2000);
          }
          return;
        }

        // Process successful OAuth callback
        console.log('✅ Processing successful OAuth callback');

        setCallbackState({
          status: "success",
          message: `Successfully connected to ${provider || "OAuth provider"}${toolName ? ` for ${toolName}` : ""}`,
          provider: provider || undefined,
          toolName: toolName || undefined,
        });

        toast.success(`OAuth connection successful!${toolName ? ` You can now use ${toolName} in your workflows.` : ""}`);

        // Store success state in sessionStorage for the parent window to detect
        const successData = {
          provider: provider || "",
          toolName: toolName || "",
          timestamp: Date.now(),
        };

        console.log('💾 Storing OAuth success data:', successData);
        storeOAuthSuccess(successData);

        // Send success message to parent window (for new tab flow)
        if (window.opener) {
          console.log('📤 Sending success message to parent window');
          try {
            window.opener.postMessage({
              type: 'OAUTH_SUCCESS',
              provider: provider || "",
              toolName: toolName || "",
            }, window.location.origin);
            console.log('✅ Success message sent to parent');
          } catch (postMessageError) {
            console.warn('⚠️ Failed to send message to parent (COOP issue):', postMessageError);
          }

          // Close this tab after a short delay
          setTimeout(() => {
            console.log('🔒 Closing OAuth popup window');
            try {
              window.close();
            } catch (closeError) {
              console.warn('⚠️ Failed to close window (COOP issue):', closeError);
            }
          }, 1500);
        } else {
          // Fallback: redirect to the original page or workflows (for same-tab flow)
          const returnUrl = getAndClearOAuthReturnUrl() || workflowsRoute;
          setTimeout(() => {
            router.push(returnUrl);
          }, 2000);
        }

      } catch (error) {
        console.error("Error processing OAuth callback:", error);
        const errorMessage = "An unexpected error occurred while processing the OAuth callback";
        setCallbackState({
          status: "error",
          message: errorMessage,
        });
        toast.error("OAuth connection failed: Unexpected error");

        // Send error message to parent window (for new tab flow)
        if (window.opener) {
          window.opener.postMessage({
            type: 'OAUTH_ERROR',
            error: errorMessage,
          }, window.location.origin);

          // Close this tab after a short delay
          setTimeout(() => {
            window.close();
          }, 2000);
        }
      }
    };

    handleOAuthCallback();
  }, [searchParams, router]);

  const handleReturnToWorkflows = () => {
    if (window.opener) {
      // If this is a popup, close it
      window.close();
    } else {
      // Otherwise, redirect to the return URL
      const returnUrl = getAndClearOAuthReturnUrl() || workflowsRoute;
      router.push(returnUrl);
    }
  };

  const getStatusIcon = () => {
    switch (callbackState.status) {
      case "loading":
        return <Loader2 className="h-8 w-8 animate-spin text-blue-500" />;
      case "success":
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      case "error":
        return <XCircle className="h-8 w-8 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (callbackState.status) {
      case "loading":
        return "border-blue-200 bg-blue-50";
      case "success":
        return "border-green-200 bg-green-50";
      case "error":
        return "border-red-200 bg-red-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className={`w-full max-w-md ${getStatusColor()}`}>
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {getStatusIcon()}
          </div>
          <CardTitle className="text-xl">
            {callbackState.status === "loading" && "Processing OAuth Connection"}
            {callbackState.status === "success" && "Connection Successful!"}
            {callbackState.status === "error" && "Connection Failed"}
          </CardTitle>
          <CardDescription>
            {callbackState.provider && (
              <span className="font-medium">Provider: {callbackState.provider}</span>
            )}
            {callbackState.toolName && (
              <span className="block font-medium">Tool: {callbackState.toolName}</span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-sm text-gray-600">
            {callbackState.message}
          </p>
          
          {callbackState.status === "success" && (
            <div className="space-y-2">
              <p className="text-sm text-green-700">
                {window.opener ?
                  "This tab will close automatically in a moment..." :
                  "You will be redirected back to your workflow in a moment..."
                }
              </p>
              <Button
                onClick={handleReturnToWorkflows}
                className="w-full"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                {window.opener ? "Close Tab" : "Return Now"}
              </Button>
            </div>
          )}
          
          {callbackState.status === "error" && (
            <div className="space-y-2">
              <p className="text-sm text-red-700">
                Please try connecting again or contact support if the issue persists.
              </p>
              <Button
                onClick={handleReturnToWorkflows}
                variant="outline"
                className="w-full"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Return to Workflow
              </Button>
            </div>
          )}
          
          {callbackState.status === "loading" && (
            <p className="text-xs text-gray-500">
              This may take a few moments...
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default function OAuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <Card className="w-full max-w-md border-blue-200 bg-blue-50">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            </div>
            <CardTitle className="text-xl">Loading OAuth Callback</CardTitle>
            <CardDescription>Processing your authentication...</CardDescription>
          </CardHeader>
        </Card>
      </div>
    }>
      <OAuthCallbackContent />
    </Suspense>
  );
}
