'use client';

import React, { useState, useEffect } from 'react';
import { fetchProviders, fetchModels, Provider, Model } from '@/lib/api';
import { enhanceComponentsWithDynamicData, getModelsForProvider } from '@/lib/componentEnhancer';

export default function TestProvidersPage() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [providerModels, setProviderModels] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Test component enhancement
  const [testComponents, setTestComponents] = useState<any>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Fetching providers and models...');
      
      const [providersResponse, modelsResponse] = await Promise.all([
        fetchProviders(),
        fetchModels()
      ]);

      console.log('Providers response:', providersResponse);
      console.log('Models response:', modelsResponse);

      if (providersResponse.success) {
        setProviders(providersResponse.providers);
      } else {
        console.warn('Failed to fetch providers:', providersResponse.message);
        setProviders([]);
      }

      if (modelsResponse.success) {
        setModels(modelsResponse.models);
      } else {
        console.warn('Failed to fetch models:', modelsResponse.message);
        setModels([]);
      }

      // Test component enhancement
      const mockComponents = {
        AI: {
          'chat_agent': {
            name: 'chat_agent',
            display_name: 'Chat Agent',
            category: 'AI',
            inputs: [
              {
                name: 'model_provider',
                display_name: 'Model Provider',
                input_type: 'dropdown',
                options: ['OpenAI', 'Anthropic'],
                value: 'OpenAI'
              },
              {
                name: 'model_name',
                display_name: 'Model',
                input_type: 'dropdown',
                options: ['gpt-4', 'gpt-3.5-turbo'],
                value: 'gpt-4'
              }
            ]
          }
        }
      };

      console.log('Testing component enhancement...');
      const enhancedComponents = await enhanceComponentsWithDynamicData(mockComponents);
      console.log('Enhanced components:', enhancedComponents);
      setTestComponents(enhancedComponents);

    } catch (err) {
      console.error('Error loading data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const handleProviderChange = async (providerName: string) => {
    setSelectedProvider(providerName);
    if (providerName) {
      try {
        const models = await getModelsForProvider(providerName);
        setProviderModels(models);
      } catch (err) {
        console.error('Error fetching models for provider:', err);
        setProviderModels([]);
      }
    } else {
      setProviderModels([]);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Provider and Model Test Page</h1>
      
      {loading && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
          Loading data...
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Error: {error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Providers Section */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Providers ({providers.length})</h2>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {providers.map((provider) => (
              <div 
                key={provider.id} 
                className={`p-3 border rounded cursor-pointer hover:bg-gray-50 ${
                  selectedProvider === provider.provider ? 'bg-blue-50 border-blue-300' : ''
                }`}
                onClick={() => handleProviderChange(provider.provider)}
              >
                <div className="font-medium">{provider.provider}</div>
                <div className="text-sm text-gray-600">{provider.description}</div>
                <div className="text-xs text-gray-500">
                  Models: {provider.modelCount} | Active: {provider.isActive ? 'Yes' : 'No'}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Models Section */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">All Models ({models.length})</h2>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {models.map((model) => (
              <div key={model.id} className="p-3 border rounded">
                <div className="font-medium">{model.model}</div>
                <div className="text-sm text-gray-600">Provider: {model.provider.provider}</div>
                <div className="text-xs text-gray-500">
                  Type: {model.providerType} | Price: ${model.pricePerTokens}/token
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Provider-specific Models */}
      {selectedProvider && (
        <div className="mt-6 bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">
            Models for {selectedProvider} ({providerModels.length})
          </h2>
          <div className="flex flex-wrap gap-2">
            {providerModels.map((model, index) => (
              <span 
                key={index}
                className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
              >
                {model}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Component Enhancement Test */}
      {testComponents && (
        <div className="mt-6 bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Component Enhancement Test</h2>
          <div className="bg-gray-100 p-4 rounded">
            <pre className="text-sm overflow-x-auto">
              {JSON.stringify(testComponents, null, 2)}
            </pre>
          </div>
        </div>
      )}

      <div className="mt-6 flex gap-4">
        <button
          onClick={loadData}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Refresh Data'}
        </button>
        
        <button
          onClick={() => {
            setProviders([]);
            setModels([]);
            setSelectedProvider('');
            setProviderModels([]);
            setTestComponents(null);
            setError(null);
          }}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Clear Data
        </button>
      </div>
    </div>
  );
}
