'use client';

import React, { useState, useEffect } from 'react';
import { 
  getCachedCredentials, 
  refreshCredentialCache, 
  clearCredentialCache,
  getCredentialCacheStatus,
  credentialOperations
} from '@/lib/credentialEnhancer';
import type { Credential } from '@/types/credentials';

export default function TestCredentialsPage() {
  const [credentials, setCredentials] = useState<Credential[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cacheStatus, setCacheStatus] = useState<any>(null);

  useEffect(() => {
    loadData();
    updateCacheStatus();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Loading credentials from cache...');
      const cachedCredentials = await getCachedCredentials();
      setCredentials(cachedCredentials);
      console.log('Loaded credentials:', cachedCredentials);
    } catch (err) {
      console.error('Error loading credentials:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
      updateCacheStatus();
    }
  };

  const refreshData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Refreshing credential cache...');
      const refreshedCredentials = await refreshCredentialCache();
      setCredentials(refreshedCredentials);
      console.log('Refreshed credentials:', refreshedCredentials);
    } catch (err) {
      console.error('Error refreshing credentials:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
      updateCacheStatus();
    }
  };

  const clearCache = () => {
    clearCredentialCache();
    setCredentials([]);
    updateCacheStatus();
    console.log('Cache cleared');
  };

  const updateCacheStatus = () => {
    const status = getCredentialCacheStatus();
    setCacheStatus(status);
  };

  const testCreateCredential = async () => {
    try {
      setLoading(true);
      const newCredential = await credentialOperations.create({
        name: `Test Credential ${Date.now()}`,
        description: 'Test credential created from cache test page',
        value: 'test-value-123'
      });
      console.log('Created credential:', newCredential);
      await loadData(); // Reload to see the new credential
    } catch (err) {
      console.error('Error creating credential:', err);
      setError(err instanceof Error ? err.message : 'Failed to create credential');
    } finally {
      setLoading(false);
    }
  };

  const testDeleteCredential = async (credentialId: string) => {
    try {
      setLoading(true);
      await credentialOperations.delete(credentialId);
      console.log('Deleted credential:', credentialId);
      await loadData(); // Reload to see the updated list
    } catch (err) {
      console.error('Error deleting credential:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete credential');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">Credential Cache Test Page</h1>
      
      {loading && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
          Loading...
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Error: {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cache Status */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Cache Status</h2>
          {cacheStatus && (
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Cached:</span>
                <span className={cacheStatus.isCached ? 'text-green-600' : 'text-red-600'}>
                  {cacheStatus.isCached ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Valid:</span>
                <span className={cacheStatus.isValid ? 'text-green-600' : 'text-orange-600'}>
                  {cacheStatus.isValid ? 'Yes' : 'Expired'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Count:</span>
                <span>{cacheStatus.count}</span>
              </div>
              <div className="flex justify-between">
                <span>Loading:</span>
                <span className={cacheStatus.isLoading ? 'text-blue-600' : 'text-gray-600'}>
                  {cacheStatus.isLoading ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Last Updated:</span>
                <span className="text-sm">
                  {cacheStatus.lastUpdated 
                    ? cacheStatus.lastUpdated.toLocaleString() 
                    : 'Never'
                  }
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Actions</h2>
          <div className="space-y-3">
            <button
              onClick={loadData}
              disabled={loading}
              className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              Load from Cache
            </button>
            
            <button
              onClick={refreshData}
              disabled={loading}
              className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
            >
              Refresh Cache
            </button>
            
            <button
              onClick={clearCache}
              disabled={loading}
              className="w-full px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
            >
              Clear Cache
            </button>
            
            <button
              onClick={testCreateCredential}
              disabled={loading}
              className="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
            >
              Test Create Credential
            </button>
          </div>
        </div>
      </div>

      {/* Credentials List */}
      <div className="mt-6 bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">
          Cached Credentials ({credentials.length})
        </h2>
        
        {credentials.length === 0 ? (
          <p className="text-gray-500">No credentials found</p>
        ) : (
          <div className="space-y-3">
            {credentials.map((credential) => (
              <div key={credential.id} className="border rounded p-4 flex justify-between items-start">
                <div>
                  <h3 className="font-medium">{credential.name}</h3>
                  <p className="text-sm text-gray-600">{credential.description}</p>
                  <p className="text-xs text-gray-500">ID: {credential.id}</p>
                  <p className="text-xs text-gray-500">
                    Created: {new Date(credential.createdAt).toLocaleString()}
                  </p>
                </div>
                <button
                  onClick={() => testDeleteCredential(credential.id)}
                  disabled={loading}
                  className="px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600 disabled:opacity-50"
                >
                  Delete
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="mt-6 bg-gray-50 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Test Instructions</h2>
        <ol className="list-decimal list-inside space-y-2 text-sm">
          <li>Open browser DevTools → Network tab</li>
          <li>Click "Load from Cache" - should use cached data (no API call if cache is valid)</li>
          <li>Click "Refresh Cache" - should make API call to /api/v1/credentials</li>
          <li>Click "Clear Cache" - clears local cache</li>
          <li>Click "Test Create Credential" - creates a credential and auto-refreshes cache</li>
          <li>Click "Delete" on any credential - deletes it and auto-refreshes cache</li>
          <li>Navigate to other pages and come back - cache should persist</li>
          <li><strong>🔍 Check Console for "🔄 Fetching fresh credential data" - this should NOT appear on every keystroke!</strong></li>
        </ol>

        <div className="mt-4 p-3 bg-yellow-100 border border-yellow-400 rounded">
          <p className="text-sm font-medium">🐛 Debugging Keystroke Issue:</p>
          <p className="text-sm">If you see "🔄 Fetching fresh credential data" in console on every keystroke, the caching is not working properly.</p>
        </div>
      </div>
    </div>
  );
}
