/**
 * Token Refresh API Route Handler
 * 
 * This endpoint handles automatic token refresh using the HTTP-only refresh token cookie.
 * It's called by the axios interceptor when a 401/403 error is encountered.
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { authApi } from '@/lib/authApi';
import { setAuthCookies, clearAuthCookies } from '@/lib/cookies';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const refreshToken = cookieStore.get('refreshToken')?.value;

    if (!refreshToken) {
      console.log('No refresh token found in cookies');
      return NextResponse.json(
        { success: false, message: 'No refresh token found' },
        { status: 401 }
      );
    }

    console.log('Attempting to refresh token...');
    
    // Use the authApi to generate a new access token
    const result = await authApi.generateAccessToken(refreshToken);

    if (result.success && result.access_token) {
      // Calculate token age in seconds from tokenExpireAt
      const expireAt = new Date(result.tokenExpireAt).getTime();
      const now = new Date().getTime();
      const accessTokenAge = Math.floor((expireAt - now) / 1000);

      // Set the new access token in cookies (keep the existing refresh token)
      await setAuthCookies(
        result.access_token,
        refreshToken, // Keep the existing refresh token
        accessTokenAge,
        30 * 24 * 60 * 60 // 30 days for refresh token
      );

      console.log('Token refreshed successfully');
      
      return NextResponse.json({
        success: true,
        accessToken: result.access_token,
        message: 'Token refreshed successfully'
      });
    } else {
      console.log('Token refresh failed:', result);
      
      // Clear cookies if refresh failed
      await clearAuthCookies();
      
      return NextResponse.json(
        { success: false, message: 'Failed to refresh token' },
        { status: 401 }
      );
    }
  } catch (error: any) {
    console.error('Error in refresh token endpoint:', error);
    
    // Clear cookies on error
    await clearAuthCookies();
    
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Token refresh failed',
        details: error.response?.data || null
      },
      { status: error.response?.status || 401 }
    );
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}