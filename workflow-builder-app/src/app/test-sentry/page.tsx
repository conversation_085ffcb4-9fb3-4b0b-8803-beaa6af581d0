"use client";

import React from "react";

export default function TestSentryPage() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100vh', textAlign: 'center' }}>
      <h1>Sentry Test Page</h1>
      <p>Click the button below to trigger a client-side Sentry error.</p>
      <button
        type="button"
        onClick={() => {
          throw new Error("Sentry Test Error from Test Page");
        }}
        style={{
          padding: '10px 20px',
          backgroundColor: '#f44336',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer',
          marginTop: '20px',
        }}
      >
        Break the world
      </button>
      <p style={{ marginTop: '20px', fontSize: '0.8em', color: '#666' }}>
        After clicking, check your Sentry dashboard for the error and session replay.
      </p>
    </div>
  );
}
