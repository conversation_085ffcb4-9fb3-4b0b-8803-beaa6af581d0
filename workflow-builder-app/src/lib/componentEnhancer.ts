/**
 * Component Enhancer for Dynamic Provider and Model Data
 * 
 * This module enhances AI components with dynamic provider and model data
 * fetched from the API instead of using static lists.
 */

import { fetchProviders, fetchModels, fetchModelsByProvider, Provider, Model } from './api';
import { getCachedCredentials, Credential } from './credentialEnhancer';
import { ComponentsApiResponse } from '@/types';

// Cache for provider and model data
let providersCache: Provider[] | null = null;
let modelsCache: Model[] | null = null;
let cacheTimestamp: number | null = null;
const CACHE_DURATION = 15 * 60 * 1000; // 15 minutes in milliseconds

// Cache for enhanced components to prevent repeated enhancement
let enhancedComponentsCache: ComponentsApiResponse | null = null;
let enhancedComponentsCacheTimestamp: number | null = null;
let enhancedComponentsCacheKey: string | null = null;

/**
 * Check if the cache is still valid
 */
function isCacheValid(): boolean {
  if (!cacheTimestamp) return false;
  return Date.now() - cacheTimestamp < CACHE_DURATION;
}

/**
 * Fetch and cache provider data only (models will be fetched dynamically)
 */
async function fetchAndCacheData(): Promise<{ providers: Provider[], models: Model[] }> {
  try {
    console.log('Fetching fresh provider data (models will be fetched dynamically)...');

    // Only fetch providers upfront, models will be fetched when needed
    const providersResponse = await fetchProviders();
    const providers = providersResponse.success ? providersResponse.providers : [];

    // Update cache
    providersCache = providers;
    modelsCache = []; // Empty models cache since we're not fetching all models upfront
    cacheTimestamp = Date.now();

    console.log(`Cached ${providers.length} providers (models will be fetched dynamically)`);

    return { providers, models: [] };
  } catch (error) {
    console.error('Failed to fetch provider data:', error);
    
    // Return fallback data
    const fallbackProviders: Provider[] = [
      {
        id: 'fallback-openai',
        provider: 'OpenAI',
        description: 'OpenAI provider',
        baseUrl: 'https://api.openai.com/v1',
        isActive: true,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        modelCount: 5
      },
      {
        id: 'fallback-anthropic',
        provider: 'Anthropic',
        description: 'Anthropic provider',
        baseUrl: 'https://api.anthropic.com',
        isActive: true,
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        modelCount: 3
      }
    ];

    // No fallback models - they will be fetched dynamically when needed
    const fallbackModels: Model[] = [];

    return { providers: fallbackProviders, models: fallbackModels };
  }
}

/**
 * Get cached or fresh provider data (models are fetched dynamically)
 */
async function getProviderAndModelData(): Promise<{ providers: Provider[], models: Model[] }> {
  if (isCacheValid() && providersCache) {
    console.log('Using cached provider data (models fetched dynamically)');
    return { providers: providersCache, models: [] };
  }

  return await fetchAndCacheData();
}

/**
 * Extract provider names from provider data
 */
function extractProviderNames(providers: Provider[]): string[] {
  const providerNames = providers
    .filter(p => p.isActive)
    .map(p => p.provider)
    .sort();
  
  // Add "Custom" option at the end
  if (!providerNames.includes('Custom')) {
    providerNames.push('Custom');
  }
  
  return providerNames;
}

/**
 * Extract model names from model data
 */
function extractModelNames(models: Model[]): string[] {
  return models
    .filter(m => m.isActive)
    .map(m => m.model)
    .sort();
}

/**
 * Create provider-to-ID mapping for dynamic model fetching
 */
function createProviderIdMapping(providers: Provider[]): Record<string, {
  providerId: string;
  providerName: string;
  isActive: boolean;
}> {
  const mapping: Record<string, {
    providerId: string;
    providerName: string;
    isActive: boolean;
  }> = {};

  providers.forEach(provider => {
    mapping[provider.provider] = {
      providerId: provider.id,
      providerName: provider.provider,
      isActive: provider.isActive
    };
  });

  // Add entry for Custom provider
  mapping['Custom'] = {
    providerId: 'custom',
    providerName: 'Custom',
    isActive: true
  };

  return mapping;
}

/**
 * Extract credential options for dropdowns
 */
function extractCredentialOptions(credentials: Credential[]): Array<{id: string, name: string, description?: string}> {
  return credentials.map(cred => ({
    id: cred.id,
    name: cred.name,
    description: cred.description
  })).sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * Check if a component is an AI component that needs enhancement
 */
function isAIComponent(componentName: string, componentDef: any): boolean {
  // Check if it's in the AI category
  if (componentDef.category === 'AI') return true;

  // Check if it has model_provider input (common pattern for AI components)
  if (componentDef.inputs?.some((input: any) => input.name === 'model_provider')) return true;

  // Check component name patterns
  const aiComponentPatterns = [
    /agent/i,
    /chat/i,
    /llm/i,
    /ai/i,
    /gpt/i,
    /claude/i,
    /gemini/i,
    /mistral/i
  ];

  return aiComponentPatterns.some(pattern => pattern.test(componentName));
}

/**
 * Check if a component has credential inputs that need enhancement
 */
function hasCredentialInputs(componentDef: any): boolean {
  if (!componentDef.inputs) return false;

  return componentDef.inputs.some((input: any) =>
    input.input_type === 'credential' ||
    input.credential_type ||
    input.name?.includes('credential') ||
    input.name?.includes('api_key') ||
    input.name?.includes('token')
  );
}

/**
 * Enhance AI component inputs with dynamic provider, model, and credential data
 */
function enhanceAIComponentInputs(componentDef: any, providers: Provider[], models: Model[], credentials: Credential[]): any {
  if (!componentDef.inputs) return componentDef;

  const providerNames = extractProviderNames(providers);
  const providerIdMapping = createProviderIdMapping(providers);
  const credentialOptions = extractCredentialOptions(credentials);

  const enhancedInputs = componentDef.inputs.map((input: any) => {
    // Enhance model_provider dropdown
    if (input.name === 'model_provider' && input.input_type === 'dropdown') {
      return {
        ...input,
        options: providerNames,
        value: providerNames.includes('OpenAI') ? 'OpenAI' : providerNames[0] || 'Custom'
      };
    }

    // Enhance model_name dropdown with dynamic filtering capability
    if (input.name === 'model_name' && input.input_type === 'dropdown') {
      return {
        ...input,
        options: [], // Start with empty options - will be populated dynamically
        value: '', // Start with empty value - will be set when provider is selected
        info: 'Select the model to use. The list is dynamically fetched from the model provider API when you select a provider.',
        // Add metadata for dynamic filtering
        _dynamicFiltering: true,
        _filterByField: 'model_provider',
        _providerIdMapping: providerIdMapping
      };
    }

    // Enhance credential inputs
    if (input.input_type === 'credential') {
      return {
        ...input,
        _availableCredentials: credentialOptions,
        info: input.info || 'Select a credential or enter a value directly. Credentials are dynamically loaded from your account.'
      };
    }

    return input;
  });

  return {
    ...componentDef,
    inputs: enhancedInputs,
    // Add metadata about the enhancement
    _enhanced: true,
    _enhancementTimestamp: Date.now(),
    _providerIdMapping: providerIdMapping,
    _availableCredentials: credentialOptions
  };
}

/**
 * Enhance components with dynamic provider, model, and credential data
 *
 * @param components - The components response from the API
 * @param preloadedData - Optional pre-loaded data to avoid API calls
 * @returns Enhanced components with dynamic data
 */
export async function enhanceComponentsWithDynamicData(
  components: ComponentsApiResponse,
  preloadedData?: {
    providers?: Provider[];
    models?: Model[];
    credentials?: Credential[];
  }
): Promise<ComponentsApiResponse> {
  try {
    // Create a cache key based on the components structure
    const componentsKey = JSON.stringify(Object.keys(components).sort());

    // Check if we have a valid cached enhanced version
    if (enhancedComponentsCache &&
        enhancedComponentsCacheTimestamp &&
        enhancedComponentsCacheKey === componentsKey &&
        Date.now() - enhancedComponentsCacheTimestamp < CACHE_DURATION) {
      console.log('Using cached enhanced components');
      return enhancedComponentsCache;
    }

    console.log('Enhancing components with dynamic provider, model, and credential data...');

    let providers: Provider[];
    let models: Model[];
    let credentials: Credential[];

    // Use preloaded data if available, otherwise fetch from API/cache
    if (preloadedData && preloadedData.providers && preloadedData.models && preloadedData.credentials) {
      console.log('Using preloaded data for component enhancement');
      providers = preloadedData.providers;
      models = preloadedData.models;
      credentials = preloadedData.credentials;
    } else {
      console.log('Fetching data for component enhancement...');
      // Get provider, model, and credential data in parallel
      const [{ providers: fetchedProviders, models: fetchedModels }, fetchedCredentials] = await Promise.all([
        getProviderAndModelData(),
        getCachedCredentials()
      ]);
      providers = fetchedProviders;
      models = fetchedModels;
      credentials = fetchedCredentials;
    }

    if (providers.length === 0 || models.length === 0) {
      console.warn('No providers or models found, skipping provider/model enhancement');
    }

    console.log(`Enhancement data: ${providers.length} providers, ${models.length} models, ${credentials.length} credentials`);

    // Create enhanced components object
    const enhancedComponents: ComponentsApiResponse = {};

    // Process each category
    Object.entries(components).forEach(([category, categoryComponents]) => {
      enhancedComponents[category] = {};

      // Process each component in the category
      Object.entries(categoryComponents).forEach(([componentName, componentDef]) => {
        if (isAIComponent(componentName, componentDef) || hasCredentialInputs(componentDef)) {
          console.log(`Enhancing component: ${category}/${componentName}`);
          enhancedComponents[category][componentName] = enhanceAIComponentInputs(
            componentDef,
            providers,
            models,
            credentials
          );
        } else {
          // Keep non-AI components as-is
          enhancedComponents[category][componentName] = componentDef;
        }
      });
    });

    console.log('Component enhancement completed');

    // Cache the enhanced components
    enhancedComponentsCache = enhancedComponents;
    enhancedComponentsCacheTimestamp = Date.now();
    enhancedComponentsCacheKey = componentsKey;

    return enhancedComponents;

  } catch (error) {
    console.error('Failed to enhance components with dynamic data:', error);
    // Return original components if enhancement fails
    return components;
  }
}

/**
 * Get models for a specific provider
 * 
 * @param providerName - The name of the provider
 * @returns Array of model names for the provider
 */
export async function getModelsForProvider(providerName: string): Promise<string[]> {
  try {
    const { models } = await getProviderAndModelData();
    
    return models
      .filter(m => m.isActive && m.provider.provider === providerName)
      .map(m => m.model)
      .sort();
  } catch (error) {
    console.error(`Failed to get models for provider ${providerName}:`, error);
    return ['custom-model'];
  }
}

/**
 * Clear the cache (useful for testing or manual refresh)
 */
export function clearCache(): void {
  providersCache = null;
  modelsCache = null;
  cacheTimestamp = null;
  enhancedComponentsCache = null;
  enhancedComponentsCacheTimestamp = null;
  enhancedComponentsCacheKey = null;
  console.log('Provider, model, and enhanced components cache cleared');
}
