// src/lib/agentApi.ts
import { Agent, PaginatedAgentResponse } from "@/types/agents";
import { API_BASE_URL } from "./api";
import { getClientAccessToken } from "./clientCookies";
import { getAccessToken } from "./cookies";

/**
 * Fetch agents from the API with robust error handling
 * @param params Optional query parameters
 * @returns A promise that resolves to a PaginatedAgentResponse
 */
export async function fetchAgents(params: {
  page?: number;
  page_size?: number;
  department?: string;
  status?: string;
  visibility?: string;
  category?: string;
  organization_id?: string;
  is_bench_employee?: boolean;
  is_a2a?: boolean;
  is_customizable?: boolean;
  search?: string;
} = {}): Promise<PaginatedAgentResponse> {
  try {
    // Build query string from params
    const queryParams = new URLSearchParams();
    
    // Add all non-undefined params to the query string
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, String(value));
      }
    });

    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    console.log(`Sending request to ${API_BASE_URL}/agents?${queryParams.toString()}`);

    const response = await fetch(`${API_BASE_URL}/agents?${queryParams.toString()}`, {
      method: "GET",
      headers: headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Agent API Error: ${response.status} ${response.statusText}`);
      console.error(`Error details: ${errorText}`);
      throw new Error(`Agent API Error: ${response.status} ${errorText || response.statusText}`);
    }

    const data: PaginatedAgentResponse = await response.json();
    console.log("Successfully fetched agents:", data);
    return data;
  } catch (error) {
    console.error("Failed to fetch agents:", error);
    // Return empty response in case of error to prevent UI from breaking
    return {
      data: [],
      metadata: {
        total: 0,
        totalPages: 0,
        currentPage: 0,
        pageSize: 0,
        hasNextPage: false,
        hasPreviousPage: false
      }
    };
  }
}

/**
 * Fetch a specific agent by ID
 * @param agentId The ID of the agent to fetch
 * @returns A promise that resolves to an Agent object
 */
export async function fetchAgentById(agentId: string): Promise<Agent | null> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_BASE_URL}/agents/${agentId}`, {
      method: "GET",
      headers: headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Agent API Error: ${response.status} ${response.statusText}`);
      console.error(`Error details: ${errorText}`);
      throw new Error(`Agent API Error: ${response.status} ${errorText || response.statusText}`);
    }

    const data: Agent = await response.json();
    console.log(`Successfully fetched agent ${agentId}:`, data);
    return data;
  } catch (error) {
    console.error(`Failed to fetch agent ${agentId}:`, error);
    return null;
  }
}
