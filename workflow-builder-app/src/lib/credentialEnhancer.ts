/**
 * Credential Enhancer for Centralized Credential Management
 * 
 * This module provides centralized credential fetching with caching to avoid
 * multiple API calls throughout the application.
 */

import { fetchCredentials, Credential } from './api';
import type { CredentialListResponse } from '@/types/credentials';

// Re-export types for convenience
export type { Credential };

// Cache for credential data
let credentialsCache: Credential[] | null = null;
let cacheTimestamp: number | null = null;
const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes in milliseconds

// Loading state to prevent multiple simultaneous requests
let isLoading = false;
let loadingPromise: Promise<Credential[]> | null = null;

/**
 * Check if the cache is still valid
 */
function isCacheValid(): boolean {
  if (!cacheTimestamp) return false;
  return Date.now() - cacheTimestamp < CACHE_DURATION;
}

/**
 * Fetch and cache credential data
 */
async function fetchAndCacheCredentials(): Promise<Credential[]> {
  // If already loading, return the existing promise
  if (isLoading && loadingPromise) {
    return loadingPromise;
  }

  isLoading = true;
  loadingPromise = (async () => {
    try {
      const response: CredentialListResponse = await fetchCredentials();
      const credentials = response.credentials || [];

      // Update cache
      credentialsCache = credentials;
      cacheTimestamp = Date.now();

      return credentials;
    } catch (error) {
      console.error('Failed to fetch credential data:', error);
      
      // Return empty array as fallback
      const fallbackCredentials: Credential[] = [];
      
      // Still cache the empty result to avoid repeated failed requests
      credentialsCache = fallbackCredentials;
      cacheTimestamp = Date.now();
      
      return fallbackCredentials;
    } finally {
      isLoading = false;
      loadingPromise = null;
    }
  })();

  return loadingPromise;
}

/**
 * Get cached or fresh credential data
 */
export async function getCachedCredentials(): Promise<Credential[]> {
  if (isCacheValid() && credentialsCache) {
    console.log(`Using cached credential data (${credentialsCache.length} credentials)`);
    return credentialsCache;
  }

  console.log('Cache invalid or empty, fetching fresh credential data...');
  return await fetchAndCacheCredentials();
}

/**
 * Refresh the credential cache
 * Call this after creating, updating, or deleting credentials
 */
export async function refreshCredentialCache(): Promise<Credential[]> {
  console.log('Refreshing credential cache...');
  
  // Clear existing cache
  credentialsCache = null;
  cacheTimestamp = null;
  
  return await fetchAndCacheCredentials();
}

/**
 * Clear the credential cache
 * Useful for testing or manual cache invalidation
 */
export function clearCredentialCache(): void {
  credentialsCache = null;
  cacheTimestamp = null;
  isLoading = false;
  loadingPromise = null;
  console.log('Credential cache cleared');
}

/**
 * Get credential by ID from cache
 */
export async function getCachedCredentialById(credentialId: string): Promise<Credential | null> {
  const credentials = await getCachedCredentials();
  return credentials.find(cred => cred.id === credentialId) || null;
}

/**
 * Get credentials filtered by type from cache
 */
export async function getCachedCredentialsByType(credentialType?: string): Promise<Credential[]> {
  const credentials = await getCachedCredentials();
  
  if (!credentialType) {
    return credentials;
  }
  
  // Note: The current credential type is stored in the name/description
  // This is a simple filter - you might want to enhance this based on your credential structure
  return credentials.filter(cred => 
    cred.name.toLowerCase().includes(credentialType.toLowerCase()) ||
    (cred.description && cred.description.toLowerCase().includes(credentialType.toLowerCase()))
  );
}

/**
 * Check if credentials are currently being loaded
 */
export function isCredentialsLoading(): boolean {
  return isLoading;
}

/**
 * Get cache status information
 */
export function getCredentialCacheStatus(): {
  isCached: boolean;
  isValid: boolean;
  count: number;
  lastUpdated: Date | null;
  isLoading: boolean;
} {
  return {
    isCached: credentialsCache !== null,
    isValid: isCacheValid(),
    count: credentialsCache?.length || 0,
    lastUpdated: cacheTimestamp ? new Date(cacheTimestamp) : null,
    isLoading
  };
}

/**
 * Preload credentials
 * Call this early in the app lifecycle to warm up the cache
 */
export async function preloadCredentials(): Promise<void> {
  try {
    await getCachedCredentials();
    console.log('Credentials preloaded successfully');
  } catch (error) {
    console.error('Failed to preload credentials:', error);
  }
}

/**
 * Enhanced credential operations that automatically refresh cache
 */
export const credentialOperations = {
  /**
   * Create credential and refresh cache
   */
  async create(credentialData: any): Promise<Credential> {
    const { createCredential } = await import('./api');
    const result = await createCredential(credentialData);
    
    // Refresh cache after creation
    await refreshCredentialCache();
    
    return result;
  },

  /**
   * Delete credential and refresh cache
   */
  async delete(credentialId: string): Promise<void> {
    const { deleteCredential } = await import('./api');
    await deleteCredential(credentialId);
    
    // Refresh cache after deletion
    await refreshCredentialCache();
  },

  /**
   * Update credential and refresh cache
   */
  async update(credentialId: string, credentialData: any): Promise<Credential> {
    // Note: You'll need to implement updateCredential in api.ts if it doesn't exist
    const { updateCredential } = await import('./api');
    const result = await updateCredential(credentialId, credentialData);
    
    // Refresh cache after update
    await refreshCredentialCache();
    
    return result;
  }
};

/**
 * Hook-like function for React components
 * Returns credentials and loading state
 */
export async function useCredentials(): Promise<{
  credentials: Credential[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}> {
  const loading = isCredentialsLoading();
  let credentials: Credential[] = [];
  let error: string | null = null;

  try {
    credentials = await getCachedCredentials();
  } catch (err) {
    error = err instanceof Error ? err.message : 'Failed to load credentials';
  }

  return {
    credentials,
    loading,
    error,
    refresh: refreshCredentialCache
  };
}
