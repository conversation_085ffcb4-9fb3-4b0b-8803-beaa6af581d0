/**
 * Hook for managing template variables in workflows
 * 
 * Provides functionality for:
 * - Managing template variable definitions
 * - Tracking variable usage across workflow
 * - Integration with workflow state management
 * - Persistence and synchronization
 */

import { useState, useCallback, useMemo, useEffect } from 'react';
import { useWorkflowStore } from '@/store/workflowStore';
import { 
  detectTemplateVariables, 
  validateTemplateVariables,
  type TemplateVariable 
} from '@/utils/templateVariables';
import type { 
  TemplateVariableDefinition, 
  TemplateVariableUsage 
} from '@/components/modals/TemplateVariableManagementDialog';

export interface UseTemplateVariablesOptions {
  /**
   * Whether to automatically detect and track variable usage
   * @default true
   */
  autoDetectUsage?: boolean;
  
  /**
   * Whether to persist variables to local storage
   * @default true
   */
  persistToStorage?: boolean;
  
  /**
   * Storage key for persisting variables
   * @default 'workflow-template-variables'
   */
  storageKey?: string;
}

export interface UseTemplateVariablesReturn {
  // Variable management
  variables: TemplateVariableDefinition[];
  createVariable: (variable: Omit<TemplateVariableDefinition, 'id' | 'createdAt' | 'updatedAt'>) => string;
  updateVariable: (id: string, updates: Partial<TemplateVariableDefinition>) => void;
  deleteVariable: (id: string) => void;
  getVariable: (id: string) => TemplateVariableDefinition | undefined;
  getVariableByName: (name: string) => TemplateVariableDefinition | undefined;
  
  // Usage tracking
  workflowUsage: Record<string, TemplateVariableUsage[]>;
  updateUsageTracking: () => void;
  getVariableUsageCount: (variableName: string) => number;
  
  // Validation and detection
  validateWorkflowVariables: () => { valid: boolean; errors: string[]; warnings: string[] };
  getUndefinedVariables: () => string[];
  getUnusedVariables: () => TemplateVariableDefinition[];
  
  // Utility functions
  exportVariables: () => string;
  importVariables: (jsonData: string) => { success: boolean; error?: string; imported: number };
  clearAllVariables: () => void;
}

/**
 * Generate a unique ID for template variables
 */
function generateVariableId(): string {
  return `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Default storage key for template variables
 */
const DEFAULT_STORAGE_KEY = 'workflow-template-variables';

/**
 * Hook for managing template variables
 */
export function useTemplateVariables(options: UseTemplateVariablesOptions = {}): UseTemplateVariablesReturn {
  const {
    autoDetectUsage = true,
    persistToStorage = true,
    storageKey = DEFAULT_STORAGE_KEY,
  } = options;

  // Get workflow data from store
  const { workflow } = useWorkflowStore();

  // State for template variables
  const [variables, setVariables] = useState<TemplateVariableDefinition[]>([]);
  const [workflowUsage, setWorkflowUsage] = useState<Record<string, TemplateVariableUsage[]>>({});

  // Load variables from storage on mount
  useEffect(() => {
    if (persistToStorage) {
      try {
        const stored = localStorage.getItem(storageKey);
        if (stored) {
          const parsedVariables = JSON.parse(stored);
          // Convert date strings back to Date objects
          const variablesWithDates = parsedVariables.map((variable: any) => ({
            ...variable,
            createdAt: new Date(variable.createdAt),
            updatedAt: new Date(variable.updatedAt),
            lastUsed: variable.lastUsed ? new Date(variable.lastUsed) : undefined,
          }));
          setVariables(variablesWithDates);
        }
      } catch (error) {
        console.error('Failed to load template variables from storage:', error);
      }
    }
  }, [persistToStorage, storageKey]);

  // Save variables to storage when they change
  useEffect(() => {
    if (persistToStorage && variables.length > 0) {
      try {
        localStorage.setItem(storageKey, JSON.stringify(variables));
      } catch (error) {
        console.error('Failed to save template variables to storage:', error);
      }
    }
  }, [variables, persistToStorage, storageKey]);

  // Update usage tracking when workflow changes
  useEffect(() => {
    if (autoDetectUsage && workflow) {
      updateUsageTracking();
    }
  }, [workflow, autoDetectUsage]);

  // Create a new template variable
  const createVariable = useCallback((
    variableData: Omit<TemplateVariableDefinition, 'id' | 'createdAt' | 'updatedAt'>
  ): string => {
    const id = generateVariableId();
    const now = new Date();
    
    const newVariable: TemplateVariableDefinition = {
      ...variableData,
      id,
      createdAt: now,
      updatedAt: now,
    };

    setVariables(prev => [...prev, newVariable]);
    return id;
  }, []);

  // Update an existing template variable
  const updateVariable = useCallback((id: string, updates: Partial<TemplateVariableDefinition>) => {
    setVariables(prev => prev.map(variable => 
      variable.id === id 
        ? { ...variable, ...updates, updatedAt: new Date() }
        : variable
    ));
  }, []);

  // Delete a template variable
  const deleteVariable = useCallback((id: string) => {
    setVariables(prev => prev.filter(variable => variable.id !== id));
  }, []);

  // Get variable by ID
  const getVariable = useCallback((id: string): TemplateVariableDefinition | undefined => {
    return variables.find(variable => variable.id === id);
  }, [variables]);

  // Get variable by name
  const getVariableByName = useCallback((name: string): TemplateVariableDefinition | undefined => {
    return variables.find(variable => variable.name === name);
  }, [variables]);

  // Update usage tracking by scanning the workflow
  const updateUsageTracking = useCallback(() => {
    if (!workflow) {
      setWorkflowUsage({});
      return;
    }

    const usage: Record<string, TemplateVariableUsage[]> = {};

    // Scan all nodes for template variables
    workflow.nodes?.forEach(node => {
      if (node.data?.inputs) {
        Object.entries(node.data.inputs).forEach(([fieldName, fieldValue]) => {
          if (typeof fieldValue === 'string') {
            const detectedVariables = detectTemplateVariables(fieldValue);
            
            detectedVariables.forEach(variable => {
              if (!usage[variable.name]) {
                usage[variable.name] = [];
              }
              
              usage[variable.name].push({
                nodeId: node.id,
                nodeName: node.data?.name || node.type || 'Unknown Node',
                fieldPath: `inputs.${fieldName}`,
                fieldName: fieldName,
                context: fieldValue,
              });
            });
          }
        });
      }
    });

    // Update usage counts in variable definitions
    setVariables(prev => prev.map(variable => {
      const usageCount = usage[variable.name]?.length || 0;
      const lastUsed = usageCount > 0 ? new Date() : variable.lastUsed;
      
      return {
        ...variable,
        usageCount,
        lastUsed,
      };
    }));

    setWorkflowUsage(usage);
  }, [workflow]);

  // Get usage count for a specific variable
  const getVariableUsageCount = useCallback((variableName: string): number => {
    return workflowUsage[variableName]?.length || 0;
  }, [workflowUsage]);

  // Validate all template variables in the workflow
  const validateWorkflowVariables = useCallback(() => {
    if (!workflow) {
      return { valid: true, errors: [], warnings: [] };
    }

    const allErrors: string[] = [];
    const allWarnings: string[] = [];

    // Validate each node's inputs
    workflow.nodes?.forEach(node => {
      if (node.data?.inputs) {
        Object.entries(node.data.inputs).forEach(([fieldName, fieldValue]) => {
          if (typeof fieldValue === 'string') {
            const validation = validateTemplateVariables(fieldValue);
            
            if (!validation.valid) {
              validation.errors.forEach(error => {
                allErrors.push(`${node.data?.name || node.id} (${fieldName}): ${error}`);
              });
            }
            
            validation.warnings.forEach(warning => {
              allWarnings.push(`${node.data?.name || node.id} (${fieldName}): ${warning}`);
            });
          }
        });
      }
    });

    return {
      valid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
    };
  }, [workflow]);

  // Get variables that are used but not defined
  const getUndefinedVariables = useCallback((): string[] => {
    const definedVariableNames = new Set(variables.map(v => v.name));
    const usedVariableNames = new Set(Object.keys(workflowUsage));
    
    return Array.from(usedVariableNames).filter(name => !definedVariableNames.has(name));
  }, [variables, workflowUsage]);

  // Get variables that are defined but not used
  const getUnusedVariables = useCallback((): TemplateVariableDefinition[] => {
    const usedVariableNames = new Set(Object.keys(workflowUsage));
    return variables.filter(variable => !usedVariableNames.has(variable.name));
  }, [variables, workflowUsage]);

  // Export variables as JSON
  const exportVariables = useCallback((): string => {
    return JSON.stringify(variables, null, 2);
  }, [variables]);

  // Import variables from JSON
  const importVariables = useCallback((jsonData: string): { success: boolean; error?: string; imported: number } => {
    try {
      const importedVariables = JSON.parse(jsonData);
      
      if (!Array.isArray(importedVariables)) {
        return { success: false, error: 'Invalid format: expected array of variables' };
      }

      // Validate and process imported variables
      const processedVariables: TemplateVariableDefinition[] = [];
      
      for (const variable of importedVariables) {
        if (!variable.name || !variable.description) {
          continue; // Skip invalid variables
        }
        
        // Generate new ID and update timestamps
        const processedVariable: TemplateVariableDefinition = {
          id: generateVariableId(),
          name: variable.name,
          description: variable.description,
          defaultValue: variable.defaultValue || '',
          type: variable.type || 'string',
          required: variable.required || false,
          category: variable.category || 'General',
          tags: variable.tags || [],
          usageCount: 0, // Reset usage count
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        
        processedVariables.push(processedVariable);
      }

      // Merge with existing variables (avoid duplicates by name)
      setVariables(prev => {
        const existingNames = new Set(prev.map(v => v.name));
        const newVariables = processedVariables.filter(v => !existingNames.has(v.name));
        return [...prev, ...newVariables];
      });

      return { success: true, imported: processedVariables.length };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }, []);

  // Clear all variables
  const clearAllVariables = useCallback(() => {
    setVariables([]);
    setWorkflowUsage({});
    
    if (persistToStorage) {
      localStorage.removeItem(storageKey);
    }
  }, [persistToStorage, storageKey]);

  return {
    // Variable management
    variables,
    createVariable,
    updateVariable,
    deleteVariable,
    getVariable,
    getVariableByName,
    
    // Usage tracking
    workflowUsage,
    updateUsageTracking,
    getVariableUsageCount,
    
    // Validation and detection
    validateWorkflowVariables,
    getUndefinedVariables,
    getUnusedVariables,
    
    // Utility functions
    exportVariables,
    importVariables,
    clearAllVariables,
  };
}
