/**
 * @jest-environment jsdom
 */

import { renderHook, act } from '@testing-library/react';
import { useTemplateVariables } from '../useTemplateVariables';
import { useWorkflowStore } from '@/store/workflowStore';

// Mock the workflow store - create a simple mock since it doesn't exist yet
const mockWorkflowStore = {
  workflow: null,
};

jest.mock('@/store/workflowStore', () => ({
  useWorkflowStore: jest.fn(() => mockWorkflowStore),
}));

// Mock the template variables utilities
jest.mock('@/utils/templateVariables', () => ({
  detectTemplateVariables: jest.fn(),
  validateTemplateVariables: jest.fn(),
}));

// Remove the mockUseWorkflowStore since we're using a direct mock
const mockDetectTemplateVariables = require('@/utils/templateVariables').detectTemplateVariables;
const mockValidateTemplateVariables = require('@/utils/templateVariables').validateTemplateVariables;

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Sample workflow data
const mockWorkflow = {
  nodes: [
    {
      id: 'node1',
      type: 'api_request',
      data: {
        name: 'API Request',
        inputs: {
          url: 'https://api.example.com/{endpoint}',
          headers: {
            authorization: 'Bearer {api_key}',
          },
        },
      },
    },
    {
      id: 'node2',
      type: 'database_query',
      data: {
        name: 'Database Query',
        inputs: {
          connection_string: '{database_url}',
          query: 'SELECT * FROM users WHERE id = {user_id}',
        },
      },
    },
  ],
  edges: [],
};

describe('useTemplateVariables', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    mockWorkflowStore.workflow = null;
    
    // Default mock implementations
    mockDetectTemplateVariables.mockReturnValue([]);
    mockValidateTemplateVariables.mockReturnValue({ valid: true, errors: [], warnings: [] });
  });

  describe('Variable Management', () => {
    it('creates a new variable', () => {
      const { result } = renderHook(() => useTemplateVariables());

      let variableId: string;

      act(() => {
        variableId = result.current.createVariable({
          name: 'test_var',
          description: 'Test variable',
          type: 'string',
          required: false,
          category: 'General',
          tags: [],
          usageCount: 0,
        });
      });

      expect(variableId).toBeDefined();
      expect(result.current.variables).toHaveLength(1);
      expect(result.current.variables[0].name).toBe('test_var');
    });

    it('updates an existing variable', () => {
      const { result } = renderHook(() => useTemplateVariables());

      let variableId: string;

      act(() => {
        variableId = result.current.createVariable({
          name: 'test_var',
          description: 'Test variable',
          type: 'string',
          required: false,
          category: 'General',
          tags: [],
          usageCount: 0,
        });
      });

      act(() => {
        result.current.updateVariable(variableId, {
          description: 'Updated description',
          required: true,
        });
      });

      expect(result.current.variables[0].description).toBe('Updated description');
      expect(result.current.variables[0].required).toBe(true);
    });

    it('deletes a variable', () => {
      const { result } = renderHook(() => useTemplateVariables());

      let variableId: string;

      act(() => {
        variableId = result.current.createVariable({
          name: 'test_var',
          description: 'Test variable',
          type: 'string',
          required: false,
          category: 'General',
          tags: [],
          usageCount: 0,
        });
      });

      act(() => {
        result.current.deleteVariable(variableId);
      });

      expect(result.current.variables).toHaveLength(0);
    });

    it('gets variable by ID', () => {
      const { result } = renderHook(() => useTemplateVariables());

      let variableId: string;

      act(() => {
        variableId = result.current.createVariable({
          name: 'test_var',
          description: 'Test variable',
          type: 'string',
          required: false,
          category: 'General',
          tags: [],
          usageCount: 0,
        });
      });

      const variable = result.current.getVariable(variableId);
      expect(variable).toBeDefined();
      expect(variable?.name).toBe('test_var');
    });

    it('gets variable by name', () => {
      const { result } = renderHook(() => useTemplateVariables());

      act(() => {
        result.current.createVariable({
          name: 'test_var',
          description: 'Test variable',
          type: 'string',
          required: false,
          category: 'General',
          tags: [],
          usageCount: 0,
        });
      });

      const variable = result.current.getVariableByName('test_var');
      expect(variable).toBeDefined();
      expect(variable?.name).toBe('test_var');
    });
  });

  describe('Usage Tracking', () => {
    it('updates usage tracking when workflow changes', () => {
      mockWorkflowStore.workflow = mockWorkflow;
      mockDetectTemplateVariables.mockImplementation((text: string) => {
        // Handle nested object case for headers
        if (typeof text === 'object') {
          return [];
        }
        const matches = text.match(/{([^}]+)}/g) || [];
        return matches.map(match => ({ name: match.slice(1, -1), start: 0, end: 0 }));
      });

      const { result } = renderHook(() => useTemplateVariables());

      act(() => {
        result.current.updateUsageTracking();
      });

      const usageKeys = Object.keys(result.current.workflowUsage);
      expect(usageKeys).toContain('endpoint');
      expect(usageKeys).toContain('database_url');
      expect(usageKeys).toContain('user_id');
      // Note: api_key might not be detected if it's in a nested object
    });

    it('gets variable usage count', () => {
      mockWorkflowStore.workflow = mockWorkflow;
      mockDetectTemplateVariables.mockImplementation((text: string) => {
        if (typeof text === 'object') {
          return [];
        }
        const matches = text.match(/{([^}]+)}/g) || [];
        return matches.map(match => ({ name: match.slice(1, -1), start: 0, end: 0 }));
      });

      const { result } = renderHook(() => useTemplateVariables());

      act(() => {
        result.current.updateUsageTracking();
      });

      expect(result.current.getVariableUsageCount('endpoint')).toBe(1);
      expect(result.current.getVariableUsageCount('nonexistent')).toBe(0);
    });
  });

  describe('Validation', () => {
    it('validates workflow variables', () => {
      mockWorkflowStore.workflow = mockWorkflow;
      mockValidateTemplateVariables.mockReturnValue({
        valid: false,
        errors: ['Invalid variable syntax'],
        warnings: ['Unused variable'],
      });

      const { result } = renderHook(() => useTemplateVariables());

      const validation = result.current.validateWorkflowVariables();

      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('API Request (url): Invalid variable syntax');
      expect(validation.warnings).toContain('API Request (url): Unused variable');
    });

    it('gets undefined variables', () => {
      // Create a mock workflow with undefined variables
      const mockWorkflowWithUndefined = {
        nodes: [
          {
            id: 'node1',
            type: 'test',
            data: {
              inputs: {
                field1: 'Using {defined_var} here',
                field2: 'Using {undefined_var} here',
              },
            },
          },
        ],
        edges: [],
      };

      mockWorkflowStore.workflow = mockWorkflowWithUndefined;
      mockDetectTemplateVariables.mockImplementation((text: string) => {
        if (typeof text === 'object') return [];
        const matches = text.match(/{([^}]+)}/g) || [];
        return matches.map(match => ({ name: match.slice(1, -1), start: 0, end: 0 }));
      });

      const { result } = renderHook(() => useTemplateVariables());

      act(() => {
        result.current.createVariable({
          name: 'defined_var',
          description: 'Defined variable',
          type: 'string',
          required: false,
          category: 'General',
          tags: [],
          usageCount: 0,
        });
      });

      act(() => {
        result.current.updateUsageTracking();
      });

      const undefinedVars = result.current.getUndefinedVariables();
      expect(undefinedVars).toContain('undefined_var');
      expect(undefinedVars).not.toContain('defined_var');
    });

    it('gets unused variables', () => {
      // Create a mock workflow that only uses one variable
      const mockWorkflowWithUsed = {
        nodes: [
          {
            id: 'node1',
            type: 'test',
            data: {
              inputs: {
                field1: 'Using {used_var} here',
              },
            },
          },
        ],
        edges: [],
      };

      mockWorkflowStore.workflow = mockWorkflowWithUsed;
      mockDetectTemplateVariables.mockImplementation((text: string) => {
        if (typeof text === 'object') return [];
        const matches = text.match(/{([^}]+)}/g) || [];
        return matches.map(match => ({ name: match.slice(1, -1), start: 0, end: 0 }));
      });

      const { result } = renderHook(() => useTemplateVariables());

      act(() => {
        result.current.createVariable({
          name: 'used_var',
          description: 'Used variable',
          type: 'string',
          required: false,
          category: 'General',
          tags: [],
          usageCount: 0,
        });

        result.current.createVariable({
          name: 'unused_var',
          description: 'Unused variable',
          type: 'string',
          required: false,
          category: 'General',
          tags: [],
          usageCount: 0,
        });
      });

      act(() => {
        result.current.updateUsageTracking();
      });

      const unusedVars = result.current.getUnusedVariables();
      expect(unusedVars).toHaveLength(1);
      expect(unusedVars[0].name).toBe('unused_var');
    });
  });

  describe('Import/Export', () => {
    it('exports variables as JSON', () => {
      const { result } = renderHook(() => useTemplateVariables());

      act(() => {
        result.current.createVariable({
          name: 'test_var',
          description: 'Test variable',
          type: 'string',
          required: false,
          category: 'General',
          tags: [],
          usageCount: 0,
        });
      });

      const exported = result.current.exportVariables();
      const parsed = JSON.parse(exported);

      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed[0].name).toBe('test_var');
    });

    it('imports variables from JSON', () => {
      const { result } = renderHook(() => useTemplateVariables());

      const importData = JSON.stringify([
        {
          name: 'imported_var',
          description: 'Imported variable',
          type: 'string',
          required: true,
          category: 'API',
          tags: ['import'],
        },
      ]);

      act(() => {
        const importResult = result.current.importVariables(importData);
        expect(importResult.success).toBe(true);
        expect(importResult.imported).toBe(1);
      });

      expect(result.current.variables).toHaveLength(1);
      expect(result.current.variables[0].name).toBe('imported_var');
    });

    it('handles invalid import data', () => {
      const { result } = renderHook(() => useTemplateVariables());

      act(() => {
        const importResult = result.current.importVariables('invalid json');
        expect(importResult.success).toBe(false);
        expect(importResult.error).toBeDefined();
      });
    });
  });

  describe('Storage Persistence', () => {
    it('loads variables from localStorage on mount', () => {
      const storedVariables = [
        {
          id: 'stored1',
          name: 'stored_var',
          description: 'Stored variable',
          type: 'string',
          required: false,
          category: 'General',
          tags: [],
          usageCount: 0,
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z',
        },
      ];

      localStorageMock.getItem.mockReturnValue(JSON.stringify(storedVariables));

      const { result } = renderHook(() => useTemplateVariables());

      expect(result.current.variables).toHaveLength(1);
      expect(result.current.variables[0].name).toBe('stored_var');
    });

    it('saves variables to localStorage when they change', () => {
      const { result } = renderHook(() => useTemplateVariables());

      act(() => {
        result.current.createVariable({
          name: 'test_var',
          description: 'Test variable',
          type: 'string',
          required: false,
          category: 'General',
          tags: [],
          usageCount: 0,
        });
      });

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'workflow-template-variables',
        expect.stringContaining('test_var')
      );
    });

    it('clears all variables and storage', () => {
      const { result } = renderHook(() => useTemplateVariables());

      act(() => {
        result.current.createVariable({
          name: 'test_var',
          description: 'Test variable',
          type: 'string',
          required: false,
          category: 'General',
          tags: [],
          usageCount: 0,
        });
      });

      act(() => {
        result.current.clearAllVariables();
      });

      expect(result.current.variables).toHaveLength(0);
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('workflow-template-variables');
    });
  });

  describe('Options', () => {
    it('respects persistToStorage option', () => {
      renderHook(() => useTemplateVariables({ persistToStorage: false }));

      expect(localStorageMock.getItem).not.toHaveBeenCalled();
    });

    it('uses custom storage key', () => {
      const customKey = 'custom-variables-key';
      renderHook(() => useTemplateVariables({ storageKey: customKey }));

      expect(localStorageMock.getItem).toHaveBeenCalledWith(customKey);
    });
  });
});
