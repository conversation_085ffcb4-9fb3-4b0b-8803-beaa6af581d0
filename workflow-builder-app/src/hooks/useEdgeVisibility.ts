import { useCallback, useEffect, useRef } from 'react';
import { Edge, Node, useReactFlow } from 'reactflow';
import { WorkflowNodeData } from '@/types';

/**
 * Custom hook to handle edge visibility issues, particularly with loop nodes
 */
export const useEdgeVisibility = (
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
  setEdges: React.Dispatch<React.SetStateAction<Edge[]>>
) => {
  const { fitView } = useReactFlow();
  const lastEdgeCountRef = useRef(edges.length);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const forceEdgeRefresh = useCallback(() => {
    // Clear any existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    // Force edge refresh with a small delay
    refreshTimeoutRef.current = setTimeout(() => {
      setEdges(currentEdges => 
        currentEdges.map(edge => ({
          ...edge,
          style: {
            ...edge.style,
            strokeWidth: edge.style?.strokeWidth || 2,
            stroke: edge.style?.stroke || "var(--primary)",
            opacity: 1,
            zIndex: 5
          }
        }))
      );

      // Also trigger a gentle fitView to force re-render
      setTimeout(() => {
        fitView({ padding: 0.05, duration: 50 });
      }, 50);
    }, 100);
  }, [setEdges, fitView]);

  // Monitor edge count changes
  useEffect(() => {
    const currentEdgeCount = edges.length;
    const hasLoopNodes = nodes.some(node => 
      node.data.originalType === "LoopNode" || node.data.type === "loop"
    );

    // If edges were added and we have loop nodes, force refresh
    if (currentEdgeCount > lastEdgeCountRef.current && hasLoopNodes) {
      forceEdgeRefresh();
    }

    lastEdgeCountRef.current = currentEdgeCount;
  }, [edges.length, nodes, forceEdgeRefresh]);

  // Monitor node changes that might affect edge visibility
  useEffect(() => {
    const hasLoopNodes = nodes.some(node => 
      node.data.originalType === "LoopNode" || node.data.type === "loop"
    );

    if (hasLoopNodes && edges.length > 0) {
      forceEdgeRefresh();
    }
  }, [nodes.length, forceEdgeRefresh, edges.length]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
    };
  }, []);

  return { forceEdgeRefresh };
};