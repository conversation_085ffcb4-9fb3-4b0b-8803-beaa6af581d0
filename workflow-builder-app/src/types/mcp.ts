export type MCPCategory = 
  | 'notifications_alerts'
  | 'communication'
  | 'social_media'
  | 'database'
  | 'cloud_storage'
  | 'devops_system'
  | 'file_handling';

export const MCP_CATEGORIES: MCPCategory[] = [
  'notifications_alerts',
  'communication',
  'social_media',
  'database',
  'cloud_storage',
  'devops_system',
  'file_handling'
];

export const MCP_CATEGORY_LABELS: Record<MCPCategory, string> = {
  notifications_alerts: 'Notifications & Alerts',
  communication: 'Communication',
  social_media: 'Social Media',
  database: 'Database',
  cloud_storage: 'Cloud Storage',
  devops_system: 'DevOps & System',
  file_handling: 'File Handling'
};

export interface MCPTool {
  id: string;
  name: string;
  description: string;
  inputSchema: any;
  category?: MCPCategory;
}

export interface MCPServer {
  id: string;
  name: string;
  description: string;
  tools: MCPTool[];
  isConnected: boolean;
  category?: MCPCategory;
}

export interface MCPConnection {
  id: string;
  name: string;
  description: string;
  isConnected: boolean;
  tools: MCPTool[];
  category?: MCPCategory;
}