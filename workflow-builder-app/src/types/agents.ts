// Define the Agent interface based on the API response structure
export interface Agent {
  id: string;
  name: string;
  description: string;
  avatar: string;
  owner_id: string;
  user_ids: string[];
  owner_type: string;
  template_id: string;
  template_owner_id: string;
  is_imported: boolean;
  is_bench_employee: boolean;
  is_changes_marketplace: boolean;
  is_updated: boolean;
  is_a2a: boolean;
  is_customizable: boolean;
  agent_category: string;
  system_message: string;
  model_provider: string;
  model_name: string;
  temperature: number;
  max_tokens: number;
  workflow_ids: string[];
  mcp_server_ids: string[];
  agent_topic_type: string;
  visibility: string;
  tags: string[];
  status: string;
  department: string;
  organization_id: string;
  tone: string;
  files: string[];
  urls: string[];
  agent_capabilities?: {
    capabilities: Array<{
      title: string;
      description: string;
    }>;
    input_modes: string[];
    output_modes: string[];
    response_model: string[];
    id: string;
    created_at: string;
    updated_at: string;
  };
  example_prompts?: string[];
  category?: string;
  created_at: string;
  updated_at: string;
  variables?: Array<{
    name: string;
    description: string;
    type: string;
    default_value: string;
    id: string;
    created_at: string;
    updated_at: string;
  }>;
}

// Define the paginated response for agents
export interface PaginatedAgentResponse {
  data: Agent[];
  metadata: {
    total: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}
