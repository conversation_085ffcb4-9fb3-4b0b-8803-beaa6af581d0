# JIRA Ticket

## **Ticket ID:** WORKFLOW-2847
## **Title:** Implement Dual-Mode Conditional Routing with Variable Toggle

---

### **Issue Type:** 
🚀 **Feature**

### **Priority:** 
🔴 **High**

### **Component:** 
Workflow Builder - Conditional Node

### **Affects Version:** 
v2.1.0

### **Fix Version:** 
v2.2.0

---

## **Summary**

Implement a dual-mode conditional routing system that allows users to toggle between evaluating conditions against input data directly or against global variables, while maintaining the same data flow for routing.

---

## **Description**

### **Business Need**
Users need the flexibility to:
1. **Direct Mode**: Evaluate conditions against the connected input data (current behavior)
2. **Variable Mode**: Evaluate conditions against global workflow variables while routing the main input data

This enables advanced workflow scenarios where decision logic is based on workflow state/flags while the main data flows through unchanged.

### **Current State**
- ConditionalNode evaluates conditions against input data only
- No option to separate evaluation criteria from routing data
- Limited flexibility for complex workflow scenarios

### **Proposed Solution**
Add a toggle-based dual-mode system with:
- **Inspector Panel Toggle**: "Use Variable for Conditions" (boolean)
- **Conditional Field**: "Condition Variable Name" (appears when toggle is ON)
- **Backward Compatibility**: Default behavior unchanged (direct mode)
- **Frontend Integration**: Follows established visibility rule patterns

---

## **Acceptance Criteria**

### **✅ Functional Requirements**

1. **Toggle Implementation**
   - [ ] Add "Use Variable for Conditions" boolean toggle in inspector panel
   - [ ] Toggle defaults to OFF (direct mode)
   - [ ] Toggle controls visibility of variable name field

2. **Conditional Field**
   - [ ] Add "Condition Variable Name" string input field
   - [ ] Field only visible when toggle is ON
   - [ ] Field is required when visible
   - [ ] Field accepts valid variable names

3. **Dual-Mode Evaluation**
   - [ ] **Direct Mode** (toggle OFF): Conditions evaluate against input_data
   - [ ] **Variable Mode** (toggle ON): Conditions evaluate against global variable
   - [ ] Data routing always uses input_data regardless of mode

4. **Error Handling**
   - [ ] Validate variable name is provided when toggle is ON
   - [ ] Handle missing global variables gracefully
   - [ ] Provide clear error messages for invalid configurations

### **✅ Technical Requirements**

5. **Backend Implementation**
   - [ ] Update ConditionalNode input schema
   - [ ] Implement dual-mode evaluation logic
   - [ ] Update ConditionalComponent in node executor
   - [ ] Add comprehensive validation and error handling

6. **Frontend Compatibility**
   - [ ] Follow established InputVisibilityRule patterns
   - [ ] Integrate with existing checkInputVisibility system
   - [ ] Maintain performance with caching optimizations
   - [ ] Ensure accessibility compliance

7. **Documentation**
   - [ ] Update component documentation
   - [ ] Add usage examples for both modes
   - [ ] Document transition schema changes

### **✅ Testing Requirements**

8. **Unit Tests**
   - [ ] Test toggle visibility logic
   - [ ] Test both evaluation modes
   - [ ] Test error handling scenarios
   - [ ] Test frontend compatibility

9. **Integration Tests**
   - [ ] Test with various global variable types
   - [ ] Test workflow execution in both modes
   - [ ] Test transition schema generation

---

## **Technical Implementation**

### **Backend Changes**

#### **Workflow Service**
- **File**: `workflow-service/app/components/control_flow/conditionalNode.py`
- **Changes**: Add toggle input, conditional variable input, dual-mode evaluation logic

#### **Node Executor Service**
- **File**: `node-executor-service/app/components/conditional_component.py`
- **Changes**: Update schema, add dual-mode processing, extend evaluation methods

### **Frontend Changes**
- **Compatibility**: No frontend changes required
- **Integration**: Uses existing visibility rule system
- **UI Components**: Leverages existing BoolInput and StringInput components

---

## **User Stories**

### **Story 1: Direct Mode (Default)**
```
As a workflow designer
I want conditions to evaluate against input data directly
So that I can route data based on its content
```

### **Story 2: Variable Mode**
```
As a workflow designer
I want conditions to evaluate against global variables
So that I can route data based on workflow state while preserving data flow
```

### **Story 3: Toggle Experience**
```
As a workflow designer
I want a simple toggle to switch between evaluation modes
So that I can easily configure the behavior I need
```

---

## **Definition of Done**

- [ ] All acceptance criteria met
- [ ] Code reviewed and approved
- [ ] Unit tests passing (>95% coverage)
- [ ] Integration tests passing
- [ ] Documentation updated
- [ ] Frontend compatibility validated
- [ ] Performance benchmarks met
- [ ] Security review completed
- [ ] Feature flag implemented (if applicable)

---

## **Testing Strategy**

### **Test Cases**
1. **Toggle OFF**: Verify variable field is hidden
2. **Toggle ON**: Verify variable field appears and is required
3. **Direct Mode**: Verify conditions evaluate against input_data
4. **Variable Mode**: Verify conditions evaluate against global variable
5. **Error Handling**: Verify graceful handling of missing variables
6. **Data Flow**: Verify input_data routing in both modes

### **Browser Testing**
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

---

## **Risk Assessment**

### **🟡 Medium Risk**
- **Backward Compatibility**: Mitigated by defaulting to existing behavior
- **Performance Impact**: Mitigated by leveraging existing caching
- **User Confusion**: Mitigated by clear UI patterns and documentation

### **Risk Mitigation**
- Comprehensive testing of both modes
- Clear documentation and examples
- Gradual rollout with feature flag
- Monitor user feedback and metrics

---

## **Dependencies**

- **Internal**: None
- **External**: None
- **Blocking**: None

---

## **Release Notes**

### **New Features**
- ✨ **Dual-Mode Conditional Routing**: Toggle between direct input evaluation and global variable evaluation
- 🎛️ **Inspector Panel Toggle**: Easy mode switching with "Use Variable for Conditions" toggle
- 🔧 **Advanced Workflow Support**: Separate decision logic from data routing

### **Improvements**
- 📈 **Workflow Flexibility**: Enhanced conditional logic capabilities
- 🎯 **User Experience**: Intuitive toggle-based configuration
- 🔒 **Backward Compatibility**: Existing workflows unchanged

---

## **Labels**
`feature` `conditional-routing` `workflow-builder` `high-priority` `backend` `inspector-panel`

---

## **Assignee**
Backend Development Team

## **Reporter**
Product Management

## **Created**
2025-01-15

## **Due Date**
2025-01-30