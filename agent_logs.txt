lay_name': 'AI Agent Executor', 'description': 'Executes an AI agent with tools and memory using AutoGen.', 'category': 'AI', 'icon': 'Bot', 'type': 'component', 'beta': True, 'requires_approval': False, 'inputs': [{'name': 'model_provider', 'display_name': 'Model Provider', 'info': 'The AI model provider to use.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'OpenAI', 'options': ['OpenAI', 'Azure OpenAI', 'Anthropic', 'Claude', 'Google', 'Gemini', 'Mistral', 'Ollama', 'Custom'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'base_url', 'display_name': 'Base URL', 'info': 'Base URL for the API (leave empty for default provider URL).', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'model_provider', 'field_value': 'Custom', 'operator': 'equals'}, {'field_name': 'model_provider', 'field_value': 'Azure OpenAI', 'operator': 'equals'}, {'field_name': 'model_provider', 'field_value': 'Ollama', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'api_key', 'display_name': 'API Key', 'info': 'API key for the model provider. Can be entered directly or referenced from secure storage.', 'input_type': 'credential', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR', 'credential_type': 'api_key', 'use_credential_id': False, 'credential_id': ''}, {'name': 'model_name', 'display_name': 'Model', 'info': 'Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'gpt-4o', 'options': ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo', 'claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022', 'claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-2.1', 'claude-2.0', 'gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-pro', 'gemini-pro-vision', 'mistral-large-latest', 'mistral-medium-latest', 'mistral-small-latest', 'open-mistral-7b', 'open-mixtral-8x7b', 'open-mixtral-8x22b', 'llama3.2', 'llama3.1', 'llama3', 'llama2', 'mistral', 'mixtral', 'phi3', 'gemma', 'codellama', 'qwen2'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'temperature', 'display_name': 'Temperature', 'info': 'Controls randomness: 0 is deterministic, higher values are more random.', 'input_type': 'float', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 0.7, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'description', 'display_name': 'Description', 'info': 'Description of the agent for UI display.', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'execution_type', 'display_name': 'Execution Type', 'info': 'Determines if agent handles single response or multi-turn conversation.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'response', 'options': ['response', 'interactive'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'query', 'display_name': 'Query/Objective', 'info': 'The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'system_message', 'display_name': 'System Message', 'info': 'System prompt/instructions for the agent. If empty, will use default based on query.', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'termination_condition', 'display_name': 'Termination Condition', 'info': 'Defines when multi-turn conversations should end. Required for interactive execution type.', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'execution_type', 'field_value': 'interactive', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'max_tokens', 'display_name': 'Max Tokens', 'info': 'Maximum response length in tokens.', 'input_type': 'int', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 1000, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_variables', 'display_name': 'Input Variables', 'info': 'Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'tools', 'display_name': 'Tools', 'info': 'Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.', 'input_type': 'handle', 'input_types': ['Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'memory', 'display_name': 'Memory Object', 'info': 'Connect a memory object from another node.', 'input_type': 'handle', 'input_types': ['Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'autogen_agent_type', 'display_name': 'AutoGen Agent Type', 'info': 'The type of AutoGen agent to create internally.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': True, 'value': 'Assistant', 'options': ['Assistant', 'UserProxy', 'CodeExecutor'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}], 'outputs': [{'name': 'final_answer', 'display_name': 'Final Answer', 'output_type': 'string', 'semantic_type': None, 'method': None}, {'name': 'intermediate_steps', 'display_name': 'Intermediate Steps', 'output_type': 'list', 'semantic_type': None, 'method': None}, {'name': 'updated_memory', 'display_name': 'Updated Memory', 'output_type': 'Memory', 'semantic_type': None, 'method': None}, {'name': 'error', 'display_name': 'Error', 'output_type': 'str', 'semantic_type': None, 'method': None}], 'is_valid': True, 'path': 'components.ai.agenticai', 'interface_issues': []}, 'config': {'id': 'AgenticAI-1750799083416', 'name': 'AI Agent Executor', 'model_provider': 'OpenAI', 'base_url': '', 'api_key': '', 'model_name': 'gpt-4o', 'temperature': 0.7, 'description': '', 'execution_type': 'response', 'query': 'i want to know what is the ${combined_text}', 'system_message': '', 'termination_condition': '', 'max_tokens': 1000, 'input_variables': {}, 'autogen_agent_type': 'Assistant', 'tools': []}}, 'style': {'opacity': 1}, 'width': 208, 'height': 228, 'selected': False, 'positionAbsolute': {'x': 480, 'y': 40}, 'dragging': False}, {'id': 'CombineTextComponent-1750799228480', 'type': 'WorkflowNode', 'position': {'x': 300, 'y': -60}, 'data': {'label': 'Combine Text', 'type': 'component', 'originalType': 'CombineTextComponent', 'definition': {'name': 'CombineTextComponent', 'display_name': 'Combine Text', 'description': 'Joins text inputs with a separator, supporting a variable number of inputs.', 'category': 'Processing', 'icon': 'Link', 'type': 'component', 'beta': False, 'requires_approval': False, 'inputs': [{'name': 'main_input', 'display_name': 'Main Input', 'info': 'The main text or list to combine. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'list', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'num_additional_inputs', 'display_name': 'Number of Additional Inputs', 'info': 'Set the number of additional text inputs to show (1-10).', 'input_type': 'int', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 2, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'separator', 'display_name': 'Separator', 'info': 'The character or string to join the text with.', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '\\n', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_1', 'display_name': 'Input 1', 'info': 'Text for input 1. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '1', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '2', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '3', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '4', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_2', 'display_name': 'Input 2', 'info': 'Text for input 2. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '2', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '3', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '4', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_3', 'display_name': 'Input 3', 'info': 'Text for input 3. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '3', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '4', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_4', 'display_name': 'Input 4', 'info': 'Text for input 4. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '4', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_5', 'display_name': 'Input 5', 'info': 'Text for input 5. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_6', 'display_name': 'Input 6', 'info': 'Text for input 6. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_7', 'display_name': 'Input 7', 'info': 'Text for input 7. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_8', 'display_name': 'Input 8', 'info': 'Text for input 8. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_9', 'display_name': 'Input 9', 'info': 'Text for input 9. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_10', 'display_name': 'Input 10', 'info': 'Text for input 10. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}], 'outputs': [{'name': 'result', 'display_name': 'Combined Text', 'output_type': 'string', 'semantic_type': None, 'method': None}, {'name': 'error', 'display_name': 'Error', 'output_type': 'str', 'semantic_type': None, 'method': None}], 'is_valid': True, 'path': 'components.processing.combinetextcomponent', 'interface_issues': []}, 'config': {'main_input': '', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello', 'input_2': '', 'input_3': '', 'input_4': '', 'input_5': '', 'input_6': '', 'input_7': '', 'input_8': '', 'input_9': '', 'input_10': ''}}, 'style': {'opacity': 1}, 'width': 208, 'height': 148, 'selected': True, 'positionAbsolute': {'x': 300, 'y': -60}, 'dragging': False}], 'edges': [{'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'CombineTextComponent-1750799228480', 'sourceHandle': 'result', 'target': 'AgenticAI-1750799083416', 'targetHandle': 'input_variables', 'type': 'default', 'id': 'reactflow__edge-CombineTextComponent-1750799228480result-AgenticAI-1750799083416input_variables'}, {'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'start-node', 'sourceHandle': 'flow', 'target': 'CombineTextComponent-1750799228480', 'targetHandle': 'main_input', 'type': 'default', 'id': 'reactflow__edge-start-nodeflow-CombineTextComponent-1750799228480main_input'}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflow_builders/31cdfed5-b038-42ab-a84b-f7d65e3d9f30.json
[DEBUG] GCS builder upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflow_builders/31cdfed5-b038-42ab-a84b-f7d65e3d9f30.json
[DEBUG] Starting workflow conversion for PATCH
[DEBUG] Workflow data keys: ['nodes', 'edges']
[DEBUG] Number of nodes: 3
[DEBUG] Number of edges: 2
[DEBUG] Node 0: id=start-node, type=component, originalType=StartNode
[DEBUG] Node 1: id=AgenticAI-1750799083416, type=agent, originalType=AgenticAI
[DEBUG] Node 2: id=CombineTextComponent-1750799228480, type=component, originalType=CombineTextComponent
[DEBUG] Edge 0: id=reactflow__edge-CombineTextComponent-1750799228480result-AgenticAI-1750799083416input_variables, source=CombineTextComponent-1750799228480, target=AgenticAI-1750799083416, sourceHandle=result
[DEBUG] Edge 1: id=reactflow__edge-start-nodeflow-CombineTextComponent-1750799228480main_input, source=start-node, target=CombineTextComponent-1750799228480, sourceHandle=flow
[DEBUG] Validating template variables for PATCH
[DEBUG] Template variable validation successful for PATCH: 1 variables found

================================================================================
🚀 STARTING WORKFLOW CONVERSION TO TRANSITION SCHEMA
================================================================================
📊 WORKFLOW COMPONENTS EXTRACTED:
   - Nodes: 3
   - Edges: 2
   - MCP Configs: 0

🔧 CHECKING FOR TOOL NODES IN WORKFLOW...
   ℹ️  No separate tool nodes found, creating virtual nodes from config...
      🔍 Checking AgenticAI node: AgenticAI-1750799083416
         - Config keys: ['id', 'name', 'model_provider', 'base_url', 'api_key', 'model_name', 'temperature', 'description', 'execution_type', 'query', 'system_message', 'termination_condition', 'max_tokens', 'input_variables', 'autogen_agent_type', 'tools']
         - No tools found in config.tools
         - Total tool connections to process: 0
   ℹ️  No tool connections found in AgenticAI configs
[DEBUG] is_conditional_node(start-node): node_type='component', original_type='StartNode', result=False
[DEBUG] is_conditional_node(AgenticAI-1750799083416): node_type='agent', original_type='AgenticAI', result=False
[DEBUG] is_conditional_node(CombineTextComponent-1750799228480): node_type='component', original_type='CombineTextComponent', result=False
📋 NODE TYPE BREAKDOWN:
   - StartNode: 1
   - AgenticAI: 1
   - CombineTextComponent: 1
ℹ️  NO CONDITIONAL NODES DETECTED

🔍 VALIDATING HANDLE MAPPINGS...
✅ Handle mapping validation successful

🎯 IDENTIFYING START NODE...
   Checking node 0: start-node (type: StartNode)
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - CombineTextComponent-1750799228480
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 2
   - Edge mappings: 2
   - All nodes: 3
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 2 nodes
   - Grouped into 2 levels

🔍 IDENTIFYING TOOL NODES...
      🔍 Found 0 tool nodes that will be integrated into agents
   🔧 INTEGRATING TOOLS INTO AGENT CONFIGURATIONS...

🔄 PHASE 1: CONVERTING NODES TO TRANSITION SCHEMA FORMAT
============================================================

📦 Processing node 1/3: start-node
   Type: StartNode (component)
   ⏭️  SKIPPED: Start node (will not appear in final schema)

📦 Processing node 2/3: AgenticAI-1750799083416
   Type: AgenticAI (agent)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(AgenticAI-1750799083416): node_type='agent', original_type='AgenticAI', result=False
         ⏭️  SKIPPED: Tool input field (tools should be callable functions, not input sources)
   ✅ CONVERTED: Added to transition_nodes array

📦 Processing node 3/3: CombineTextComponent-1750799228480
   Type: CombineTextComponent (component)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(CombineTextComponent-1750799228480): node_type='component', original_type='CombineTextComponent', result=False
   ✅ CONVERTED: Added to transition_nodes array

🔗 COMBINING DUPLICATE NODES...
   No duplicate nodes found (2 nodes)

📊 PHASE 1 SUMMARY:
   - Start nodes skipped: 1 ['start-node']
   - Tool nodes skipped (integrated into agents): 0 []
   - Component nodes processed: 2 ['AgenticAI-1750799083416', 'CombineTextComponent-1750799228480']
   - Final transition_nodes count: 2
   - Agent tool integrations: 0 agents with tools

🔄 PHASE 2: CREATING TRANSITIONS FROM WORKFLOW LOGIC
============================================================
🎯 Start node marked as processed: start-node

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['CombineTextComponent-1750799228480']

   📦 Processing node: CombineTextComponent-1750799228480
      Type: CombineTextComponent (component)
[DEBUG] is_conditional_node(CombineTextComponent-1750799228480): node_type='component', original_type='CombineTextComponent', result=False
      Is conditional: False
[DEBUG] is_output_node(CombineTextComponent-1750799228480): node_type='component', result=False
[DEBUG] is_conditional_node(AgenticAI-1750799083416): node_type='agent', original_type='AgenticAI', result=False
      ✅ REGULAR TRANSITION CREATED:
         - ID: transition-CombineTextComponent-1750799228480
         - Sequence: 1
         - Execution Type: Components
         - Tools: 1
         - Input Data: 0
         - Output Data: 1

🏗️  PROCESSING LEVEL 1
   Nodes at this level: ['AgenticAI-1750799083416']

   📦 Processing node: AgenticAI-1750799083416
      Type: AgenticAI (agent)
[DEBUG] is_conditional_node(AgenticAI-1750799083416): node_type='agent', original_type='AgenticAI', result=False
      Is conditional: False
[DEBUG] is_output_node(AgenticAI-1750799083416): node_type='agent', result=False

================================================================================
🎉 WORKFLOW CONVERSION COMPLETED SUCCESSFULLY
================================================================================
📊 FINAL STATISTICS:
   - Total nodes in final schema: 2
   - Total transitions created: 2
   - Conditional transitions: 0
   - Regular transitions: 1

⚙️  REGULAR TRANSITIONS CREATED:
   - CombineTextComponent-1750799228480: 1 tools

🔍 SCHEMA VALIDATION:
   ✅ ALL SCHEMA VALIDATION CHECKS PASSED!

🚀 CONVERSION COMPLETE - SCHEMA READY FOR EXECUTION
================================================================================
[DEBUG] Workflow conversion successful for PATCH
✅ Transition schema is valid.
[DEBUG] Transition schema validation successful for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': 'AgenticAI', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'AgenticAI', 'input_schema': {'predefined_fields': [{'field_name': 'model_provider', 'data_type': {'type': 'string', 'description': 'The AI model provider to use.'}, 'required': False}, {'field_name': 'base_url', 'data_type': {'type': 'string', 'description': 'Base URL for the API (leave empty for default provider URL).'}, 'required': False}, {'field_name': 'api_key', 'data_type': {'type': 'string', 'description': 'API key for the model provider. Can be entered directly or referenced from secure storage.'}, 'required': False}, {'field_name': 'model_name', 'data_type': {'type': 'string', 'description': 'Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.'}, 'required': False}, {'field_name': 'temperature', 'data_type': {'type': 'number', 'description': 'Controls randomness: 0 is deterministic, higher values are more random.'}, 'required': False}, {'field_name': 'description', 'data_type': {'type': 'string', 'description': 'Description of the agent for UI display.'}, 'required': False}, {'field_name': 'execution_type', 'data_type': {'type': 'string', 'description': 'Determines if agent handles single response or multi-turn conversation.'}, 'required': False}, {'field_name': 'query', 'data_type': {'type': 'string', 'description': 'The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.'}, 'required': True}, {'field_name': 'system_message', 'data_type': {'type': 'string', 'description': 'System prompt/instructions for the agent. If empty, will use default based on query.'}, 'required': False}, {'field_name': 'termination_condition', 'data_type': {'type': 'string', 'description': 'Defines when multi-turn conversations should end. Required for interactive execution type.'}, 'required': False}, {'field_name': 'max_tokens', 'data_type': {'type': 'number', 'description': 'Maximum response length in tokens.'}, 'required': False}, {'field_name': 'input_variables', 'data_type': {'type': 'object', 'description': 'Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'memory', 'data_type': {'type': 'string', 'description': 'Connect a memory object from another node.'}, 'required': False}, {'field_name': 'autogen_agent_type', 'data_type': {'type': 'string', 'description': 'The type of AutoGen agent to create internally.'}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'final_answer', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}, {'field_name': 'intermediate_steps', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}, {'field_name': 'updated_memory', 'data_type': {'type': 'string', 'description': '', 'format': 'datetime'}}, {'field_name': 'error', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}]}}]}, {'id': 'CombineTextComponent', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'CombineTextComponent', 'input_schema': {'predefined_fields': [{'field_name': 'main_input', 'data_type': {'type': 'string', 'description': 'The main text or list to combine. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'num_additional_inputs', 'data_type': {'type': 'number', 'description': 'Set the number of additional text inputs to show (1-10).'}, 'required': False}, {'field_name': 'separator', 'data_type': {'type': 'string', 'description': 'The character or string to join the text with.'}, 'required': False}, {'field_name': 'input_1', 'data_type': {'type': 'string', 'description': 'Text for input 1. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_2', 'data_type': {'type': 'string', 'description': 'Text for input 2. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_3', 'data_type': {'type': 'string', 'description': 'Text for input 3. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_4', 'data_type': {'type': 'string', 'description': 'Text for input 4. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_5', 'data_type': {'type': 'string', 'description': 'Text for input 5. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_6', 'data_type': {'type': 'string', 'description': 'Text for input 6. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_7', 'data_type': {'type': 'string', 'description': 'Text for input 7. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_8', 'data_type': {'type': 'string', 'description': 'Text for input 8. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_9', 'data_type': {'type': 'string', 'description': 'Text for input 9. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_10', 'data_type': {'type': 'string', 'description': 'Text for input 10. Can be connected from another node or entered directly.'}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'result', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}, {'field_name': 'error', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}]}}]}], 'transitions': [{'id': 'transition-CombineTextComponent-1750799228480', 'sequence': 1, 'transition_type': 'initial', 'execution_type': 'Components', 'node_info': {'node_id': 'CombineTextComponent', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'CombineTextComponent', 'tool_params': {'items': [{'field_name': 'main_input', 'data_type': 'string', 'field_value': ''}, {'field_name': 'num_additional_inputs', 'data_type': 'number', 'field_value': '1'}, {'field_name': 'separator', 'data_type': 'string', 'field_value': '\\n'}, {'field_name': 'input_1', 'data_type': 'string', 'field_value': 'hello'}, {'field_name': 'input_2', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_3', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_4', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_5', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_6', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_7', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_8', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_9', 'data_type': 'string', 'field_value': ''}, {'field_name': 'input_10', 'data_type': 'string', 'field_value': ''}]}}], 'input_data': [], 'output_data': [{'to_transition_id': 'transition-AgenticAI-1750799083416', 'target_node_id': 'AI Agent Executor', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'result', 'result_path': 'result', 'edge_id': 'reactflow__edge-CombineTextComponent-1750799228480result-AgenticAI-1750799083416input_variables'}]}}]}, 'result_resolution': {'node_type': 'component', 'expected_result_structure': 'direct', 'handle_registry': {'input_handles': [{'handle_id': 'main_input', 'handle_name': 'Main Input', 'data_type': 'string', 'required': False, 'description': 'The main text or list to combine. Can be connected from another node or entered directly.'}, {'handle_id': 'input_1', 'handle_name': 'Input 1', 'data_type': 'string', 'required': False, 'description': 'Text for input 1. Can be connected from another node or entered directly.'}, {'handle_id': 'input_2', 'handle_name': 'Input 2', 'data_type': 'string', 'required': False, 'description': 'Text for input 2. Can be connected from another node or entered directly.'}, {'handle_id': 'input_3', 'handle_name': 'Input 3', 'data_type': 'string', 'required': False, 'description': 'Text for input 3. Can be connected from another node or entered directly.'}, {'handle_id': 'input_4', 'handle_name': 'Input 4', 'data_type': 'string', 'required': False, 'description': 'Text for input 4. Can be connected from another node or entered directly.'}, {'handle_id': 'input_5', 'handle_name': 'Input 5', 'data_type': 'string', 'required': False, 'description': 'Text for input 5. Can be connected from another node or entered directly.'}, {'handle_id': 'input_6', 'handle_name': 'Input 6', 'data_type': 'string', 'required': False, 'description': 'Text for input 6. Can be connected from another node or entered directly.'}, {'handle_id': 'input_7', 'handle_name': 'Input 7', 'data_type': 'string', 'required': False, 'description': 'Text for input 7. Can be connected from another node or entered directly.'}, {'handle_id': 'input_8', 'handle_name': 'Input 8', 'data_type': 'string', 'required': False, 'description': 'Text for input 8. Can be connected from another node or entered directly.'}, {'handle_id': 'input_9', 'handle_name': 'Input 9', 'data_type': 'string', 'required': False, 'description': 'Text for input 9. Can be connected from another node or entered directly.'}, {'handle_id': 'input_10', 'handle_name': 'Input 10', 'data_type': 'string', 'required': False, 'description': 'Text for input 10. Can be connected from another node or entered directly.'}], 'output_handles': [{'handle_id': 'result', 'handle_name': 'Combined Text', 'data_type': 'string', 'description': ''}, {'handle_id': 'error', 'handle_name': 'Error', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'result': 'result', 'error': 'error'}, 'dynamic_discovery': {'enabled': False, 'fallback_patterns': ['result.result', 'output_data.result', 'response.result', 'data.result', 'result.error', 'output_data.error', 'response.error', 'data.error', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': True, 'supports_nested_results': False, 'requires_dynamic_discovery': False, 'primary_output_handle': 'result'}}, 'approval_required': False, 'end': False}, {'id': 'transition-AgenticAI-1750799083416', 'sequence': 2, 'transition_type': 'standard', 'execution_type': 'agent', 'node_info': {'node_id': 'AgenticAI', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'AgenticAI', 'tool_params': {'items': [{'field_name': 'agent_type', 'data_type': 'string', 'field_value': 'component'}, {'field_name': 'execution_type', 'data_type': 'string', 'field_value': 'response'}, {'field_name': 'query', 'data_type': 'string', 'field_value': 'i want to know what is the ${combined_text}'}, {'field_name': 'agent_config', 'data_type': 'object', 'field_value': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'description': '', 'system_message': '', 'autogen_agent_type': 'Assistant', 'termination_condition': '', 'input_variables': {}}}]}}], 'input_data': [{'from_transition_id': 'transition-CombineTextComponent-1750799228480', 'source_node_id': 'Combine Text', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-CombineTextComponent-1750799228480', 'source_handle_id': 'result', 'target_handle_id': 'input_variables', 'edge_id': 'reactflow__edge-CombineTextComponent-1750799228480result-AgenticAI-1750799083416input_variables'}]}], 'output_data': []}, 'result_resolution': {'node_type': 'agent', 'expected_result_structure': 'direct', 'handle_registry': {'input_handles': [{'handle_id': 'query', 'handle_name': 'Query/Objective', 'data_type': 'string', 'required': True, 'description': 'The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.'}, {'handle_id': 'input_variables', 'handle_name': 'Input Variables', 'data_type': 'object', 'required': False, 'description': 'Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.'}, {'handle_id': 'tools', 'handle_name': 'Tools', 'data_type': 'string', 'required': False, 'description': 'Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.'}, {'handle_id': 'memory', 'handle_name': 'Memory Object', 'data_type': 'string', 'required': False, 'description': 'Connect a memory object from another node.'}], 'output_handles': [{'handle_id': 'final_answer', 'handle_name': 'Final Answer', 'data_type': 'string', 'description': ''}, {'handle_id': 'intermediate_steps', 'handle_name': 'Intermediate Steps', 'data_type': 'string', 'description': ''}, {'handle_id': 'updated_memory', 'handle_name': 'Updated Memory', 'data_type': 'string', 'description': ''}, {'handle_id': 'error', 'handle_name': 'Error', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'final_answer': 'final_answer', 'intermediate_steps': 'intermediate_steps', 'updated_memory': 'updated_memory', 'error': 'error'}, 'dynamic_discovery': {'enabled': False, 'fallback_patterns': ['result.final_answer', 'output_data.final_answer', 'response.final_answer', 'data.final_answer', 'result.intermediate_steps', 'output_data.intermediate_steps', 'response.intermediate_steps', 'data.intermediate_steps', 'result.updated_memory', 'output_data.updated_memory', 'response.updated_memory', 'data.updated_memory', 'result.error', 'output_data.error', 'response.error', 'data.error', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': True, 'supports_nested_results': False, 'requires_dynamic_discovery': False, 'primary_output_handle': 'final_answer'}}, 'approval_required': False, 'end': True}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflows/93572ad1-bab0-41e8-bbe1-240c4d3dd961.json
[DEBUG] Converted workflow GCS upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflows/93572ad1-bab0-41e8-bbe1-240c4d3dd961.json
Extracting available nodes...
[TRANSITION_ID] transition-start-node
[TRANSITION_ID] transition-AgenticAI-1750799083416
[TRANSITION_ID] transition-CombineTextComponent-1750799228480
Available nodes: [{'name': 'CombineTextComponent', 'display_name': 'Combine Text', 'type': 'component', 'transition_id': 'transition-CombineTextComponent-1750799228480'}]
[DEBUG] Version-relevant fields changed, setting is_updated=True
2025-06-25 02:37:41 [info     ] Set is_updated=True for workflow 05b1ab60-fc1f-40a5-a8e9-8dcfa0213538 due to version-relevant changes
2025-06-25 02:37:43 [info     ] Checking derived workflow update conditions: template_relevant_fields_changed=True, workflow.visibility=WorkflowVisibilityEnum.PRIVATE, is_public=False
2025-06-25 02:37:43 [info     ] Skipping derived workflow update for workflow 05b1ab60-fc1f-40a5-a8e9-8dcfa0213538
