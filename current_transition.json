{"tools_to_use": [{"tool_id": 1, "tool_name": "ConditionalNode", "tool_params": {"items": [{"field_name": "input_data", "data_type": "string", "field_value": ""}, {"field_name": "use_variable_for_conditions", "data_type": "boolean", "field_value": true}, {"field_name": "condition_variable_name", "data_type": "string", "field_value": "hello"}, {"field_name": "condition_1_operator", "data_type": "string", "field_value": "contains"}, {"field_name": "condition_1_expected_value", "data_type": "string", "field_value": "hello"}, {"field_name": "num_additional_conditions", "data_type": "number", "field_value": 0}, {"field_name": "evaluation_strategy", "data_type": "string", "field_value": "all_matches"}, {"field_name": "condition_2_operator", "data_type": "string", "field_value": "equals"}, {"field_name": "condition_2_expected_value", "data_type": "string", "field_value": ""}, {"field_name": "condition_3_operator", "data_type": "string", "field_value": "equals"}, {"field_name": "condition_3_expected_value", "data_type": "string", "field_value": ""}, {"field_name": "condition_4_operator", "data_type": "string", "field_value": "equals"}, {"field_name": "condition_4_expected_value", "data_type": "string", "field_value": ""}, {"field_name": "condition_5_operator", "data_type": "string", "field_value": "equals"}, {"field_name": "condition_5_expected_value", "data_type": "string", "field_value": ""}, {"field_name": "condition_6_operator", "data_type": "string", "field_value": "equals"}, {"field_name": "condition_6_expected_value", "data_type": "string", "field_value": ""}, {"field_name": "condition_7_operator", "data_type": "string", "field_value": "equals"}, {"field_name": "condition_7_expected_value", "data_type": "string", "field_value": ""}, {"field_name": "condition_8_operator", "data_type": "string", "field_value": "equals"}, {"field_name": "condition_8_expected_value", "data_type": "string", "field_value": ""}, {"field_name": "condition_9_operator", "data_type": "string", "field_value": "equals"}, {"field_name": "condition_9_expected_value", "data_type": "string", "field_value": ""}, {"field_name": "condition_10_operator", "data_type": "string", "field_value": "equals"}, {"field_name": "condition_10_expected_value", "data_type": "string", "field_value": ""}]}}]}