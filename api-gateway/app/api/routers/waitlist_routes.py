from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from app.services.user_service import UserServiceClient
from app.core.auth_guard import role_required
from app.schemas.user import (
    ApproveMultipleResponse,
    ApproveMultipleWaitlistUsersRequest,
    MessageResponse,
    PaginatedWaitlistResponse,
    PaginationMetadata,
    WaitlistCreate,
    WaitlistEntryResponse,
)
from fastapi.responses import JSONResponse
from app.core.auth_guard import role_required
from app.utils.parse_error import parse_error


waitlist_router = APIRouter(prefix="/waitlist", tags=["waitlist"])
user_service = UserServiceClient()


# --- New /waitlist Endpoint ---
@waitlist_router.post(
    "/waitlist",
    summary="Add user email to the waitlist",
    description="Adds a user's email (and optional details) to the waitlist for early access or notification.",
    responses={
        200: {
            "description": "Successfully added to the waitlist",
            "content": {"application/json": {"example": {"success": True, "message": "You've been added to the waitlist!"}}},
        },
        400: {
            "description": "Invalid input (e.g., bad email format)",
            "content": {"application/json": {"example": {"detail": "Invalid email format"}}},
        },
        409: { # Use 409 Conflict for already exists
            "description": "Email already registered or already on waitlist",
            "content": {"application/json": {"example": {"detail": "This email is already registered."}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def add_to_waitlist(waitlist_data: WaitlistCreate):
    """
    Adds an email to the waitlist. Checks if the user is already fully registered
    or already present on the waitlist before adding.
    """
    try:
        print(f"Adding {waitlist_data.email} to waitlist...")
        response = await user_service.add_to_waitlist(
            email=waitlist_data.email
        )
        print(f"Response: {response}")
        return JSONResponse(content={"success": response.success, "message": response.message})
    
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

# --- Waitlist Routes ---
@waitlist_router.get(
    "/waitlist",
    response_model=PaginatedWaitlistResponse,
    summary="Get all waitlist entries",
    description="Get a paginated list of all waitlist entries. Admin only.",
    responses={
        200: {"description": "Successfully retrieved waitlist entries"},
        500: {"description": "Internal Server Error"},
    },
)
async def get_waitlist(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    status: Optional[str] = Query(None, description="Filter by status (pending/approved)"),
    current_user: dict = Depends(role_required(["admin"]))
):
    """
    Get a paginated list of all waitlist entries.
    """
    try:
        response = await user_service.get_waitlist(
            page=page,
            page_size=page_size,
            status_filter=status
        )
        
        # Convert to Pydantic model
        entries = []
        for entry in response.entries:
            entries.append(WaitlistEntryResponse(
                id=entry.id,
                email=entry.email,
                joined_at=entry.joined_at,
                status=entry.status
            ))
        
        # Calculate pagination metadata
        has_next = response.page < response.total_pages
        has_prev = response.page > 1
        
        metadata = PaginationMetadata(
            total=response.total,
            totalPages=response.total_pages,
            currentPage=response.page,
            pageSize=page_size,
            hasNextPage=has_next,
            hasPreviousPage=has_prev
        )
        
        return PaginatedWaitlistResponse(
            data=entries,
            metadata=metadata
        )
        
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@waitlist_router.post(
    "/waitlist/{user_id}/approve",
    response_model=MessageResponse,
    summary="Approve a single user from the waitlist",
    description="Change a user's status from pending to approved. Admin only.",
    responses={
        200: {"description": "User successfully approved"},
        404: {"description": "User not found"},
        500: {"description": "Internal Server Error"},
    },
)
async def approve_waitlist_user(user_id: str, current_user: dict = Depends(role_required(["admin"]))):
    """
    Approve a single user from the waitlist.
    """
    try:
        response = await user_service.approve_waitlist_user(user_id=user_id)
        return MessageResponse(success=response.success, message=response.message)
    
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@waitlist_router.post(
    "/waitlist/approve-multiple",
    response_model=ApproveMultipleResponse,
    summary="Approve multiple users from the waitlist",
    description="Change multiple users' status from pending to approved. Admin only.",
    responses={
        200: {"description": "Users successfully approved"},
        400: {"description": "Invalid request"},
        500: {"description": "Internal Server Error"},
    },
)
async def approve_multiple_waitlist_users(request: ApproveMultipleWaitlistUsersRequest , current_user: dict = Depends(role_required(["admin"]))):
    """
    Approve multiple users from the waitlist.
    """
    try:
        if not request.user_ids:
            raise HTTPException(status_code=400, detail="No user IDs provided")
            
        response = await user_service.approve_multiple_waitlist_users(user_ids=request.user_ids)
        
        return ApproveMultipleResponse(
            success=response.success,
            message=response.message,
            approved_count=response.approved_count,
            failed_ids=response.failed_ids
        )
    
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
