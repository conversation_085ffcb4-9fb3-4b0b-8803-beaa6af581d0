"""
Generalized OAuth Routes (v2) - Authentication Service Integration

This module provides OAuth endpoints that integrate with the dedicated
authentication service via gRPC for all OAuth operations.
"""

# Removed unused imports - API Gateway only handles gRPC communication
from typing import Optional
from urllib.parse import quote_plus
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import RedirectResponse, JSONResponse

# Database imports removed - API Gateway is now a pure gRPC proxy

from app.core.security import validate_server_auth_key
from app.core.auth_guard import role_required
from app.core.oauth_providers import OAuthProvider
from app.schemas.oauth import (
    OAuthCredentialResponse,
    OAuthErrorResponse,
    OAuthProvidersListResponse,
    OAuthProviderInfo,
    OAuthToolScopesResponse,
    ServerOAuthCredentialResponse,
)

# Config import removed - not needed for gRPC proxy
# Database dependency removed - using authentication service via gRPC
from app.services.authentication_service import get_auth_service_client
import logging

logger = logging.getLogger(__name__)

# Initialize router - prefix is set in main.py
oauth_router = APIRouter(tags=["OAuth v2"])


@oauth_router.get(
    "/providers",
    response_model=OAuthProvidersListResponse,
    summary="List available OAuth providers",
    description="Get a list of all configured OAuth providers and their supported tools.",
)
async def list_oauth_providers():
    """List all available OAuth providers and their capabilities."""
    try:
        # Call authentication service to get providers
        result = await get_auth_service_client().list_oauth_providers()

        if result["success"]:
            # Convert to API response format
            provider_infos = []
            for provider_data in result["providers"]:
                provider_info = OAuthProviderInfo(
                    provider=provider_data["provider"],
                    name=provider_data["provider"],
                    auth_url="",  # Not needed in response
                    supported_tools=provider_data["supported_tools"],
                )
                provider_infos.append(provider_info)

            return OAuthProvidersListResponse(
                success=True, message=result["message"], providers=provider_infos
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result["message"],
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing OAuth providers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve OAuth providers",
        )


@oauth_router.get(
    "/tools/{tool_name}/scopes",
    response_model=OAuthToolScopesResponse,
    summary="Get tool scope requirements",
    description="Get the required OAuth scopes for a specific tool across all providers.",
)
async def get_tool_scopes(tool_name: str):
    """Get scope requirements for a specific tool."""
    try:
        # Call authentication service to get tool scopes for Google (default provider)
        result = await get_auth_service_client().get_tool_scopes(tool_name, OAuthProvider.GOOGLE)

        if result["success"]:
            return OAuthToolScopesResponse(
                success=True,
                message=result["message"],
                tool_name=result["tool_name"],
                provider_scopes={"google": result["scopes"]},  # For backward compatibility
            )
        else:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting tool scopes: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tool scopes",
        )


@oauth_router.get(
    "/authorize",
    summary="Initiate OAuth authorization flow",
    description="""
    Initiate OAuth authorization flow for any supported provider and tool.

    This endpoint supports:
    - Multiple OAuth providers (Google, Microsoft, GitHub, etc.)
    - Dynamic scope resolution based on tool requirements
    - Custom scope specification
    - Secure state management

    **Usage in Swagger:**
    1. Click "Try it out"
    2. Select provider (e.g., "google")
    3. Enter tool_name (e.g., "google_calendar")
    4. Enter user_id (e.g., "user123")
    5. Optionally enter redirect_url for custom redirect after OAuth completion
    6. Click "Execute"
    7. You'll get a 302 redirect to the OAuth provider

    **Frontend Usage:**
    Use direct browser navigation: window.location.href = "/api/v1/oauth/authorize?provider=google&tool_name=calendar&user_id=user123&redirect_url=https://myapp.com/callback"
    """,
    responses={
        302: {"description": "Redirect to OAuth provider's authorization page"},
        400: {"description": "Bad Request - Invalid parameters"},
        500: {"description": "Internal Server Error"},
    },
)
async def oauth_authorize(
    provider: OAuthProvider = Query(..., description="OAuth provider to use"),
    tool_name: str = Query(..., description="The name of the tool"),
    user_id: str = Query(..., description="The user ID"),
    scopes: Optional[str] = Query(None, description="Custom scopes (comma-separated, optional)"),
    redirect_url: Optional[str] = Query(
        None, description="Optional redirect URL after OAuth completion"
    ),
):
    """Initiate OAuth authorization flow for any provider."""
    try:
        # Validate user_id parameter
        if not user_id or not user_id.strip():
            logger.error(f"Missing or empty user_id parameter")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="user_id parameter is required",
            )

        logger.info(
            f"Initiating OAuth for User: {user_id}, Provider: {provider}, "
            f"Tool: {tool_name}, Redirect URL: {redirect_url}"
        )

        # Parse custom scopes if provided
        custom_scopes = None
        if scopes:
            custom_scopes = [scope.strip() for scope in scopes.split(",")]

        # Call authentication service to initiate OAuth
        result = await get_auth_service_client().initiate_oauth(
            user_id=user_id,
            tool_name=tool_name,
            provider=provider,
            scopes=custom_scopes,
            redirect_uri=redirect_url,
        )

        print(f"🔍 OAuth initiate result: {result}")

        if result["success"]:
            logger.info(
                f"Generated authorization URL for {provider}: {result['authorization_url']}"
            )
            print(f"🔗 OAuth Authorization URL: {result['authorization_url']}")
            return RedirectResponse(
                url=result["authorization_url"], status_code=status.HTTP_302_FOUND
            )
        else:
            logger.error(f"Failed to generate authorization URL: {result['message']}")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in oauth_authorize: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate OAuth flow",
        )


@oauth_router.get(
    "/callback",
    summary="Handle OAuth callback",
    description="""
    Handle OAuth callback from any supported provider.

    This endpoint:
    - Validates the state parameter
    - Exchanges authorization code for tokens
    - Stores credentials securely
    - Works with any configured OAuth provider
    - Optionally redirects to a custom URL after processing (if provided during authorization)

    If redirect_url was provided during the authorization request, the user will be redirected to that URL with query parameters:
    - success=true/false
    - message=<result_message>
    - provider=<oauth_provider> (on success)
    - tool_name=<tool_name> (on success)
    - error=<error_code> (on failure)
    """,
    responses={
        200: {"description": "OAuth flow completed successfully"},
        302: {"description": "Redirect to custom URL (when redirect_url is provided)"},
        400: {"description": "Bad Request - Invalid callback parameters"},
        500: {"description": "Internal Server Error"},
    },
)
async def oauth_callback(
    code: Optional[str] = Query(None, description="Authorization code from OAuth provider"),
    state: str = Query(..., description="State parameter from OAuth provider"),
    error: Optional[str] = Query(None, description="Error code if authorization failed"),
):
    """Handle OAuth callback from any provider."""
    try:
        # Call authentication service to handle callback first to get redirect_url
        result = await get_auth_service_client().handle_oauth_callback(
            code=code, state=state, error=error
        )

        # Extract redirect_url from the authentication service response
        redirect_url = result.get("redirect_url")

        # Handle OAuth errors
        if error:
            logger.error(f"OAuth error received in callback: {error}")
            user_message = f"Authorization failed: {error}"
            if error == "access_denied":
                user_message = "You denied the authorization request."

            # If redirect_url is provided, redirect with error parameters
            if redirect_url:
                error_redirect_url = f"{redirect_url}?success=false&error={quote_plus(error)}&message={quote_plus(user_message)}"
                logger.info(f"Redirecting to error URL: {error_redirect_url}")
                return RedirectResponse(url=error_redirect_url, status_code=status.HTTP_302_FOUND)

            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"success": False, "message": user_message},
            )

        if not code:
            logger.error("Callback received without code or error parameter.")
            error_message = "Missing authorization code in callback."

            # If redirect_url is provided, redirect with error parameters
            if redirect_url:
                error_redirect_url = f"{redirect_url}?success=false&error=missing_code&message={quote_plus(error_message)}"
                logger.info(f"Redirecting to error URL: {error_redirect_url}")
                return RedirectResponse(url=error_redirect_url, status_code=status.HTTP_302_FOUND)

            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"success": False, "message": error_message},
            )

        # Process the result from authentication service
        if result["success"]:
            logger.info(
                f"OAuth callback handled successfully for {result.get('provider', 'unknown')}"
            )

            # If redirect_url is provided, redirect with success parameters
            if redirect_url:
                success_redirect_url = (
                    f"{redirect_url}?success=true&message={quote_plus(result['message'])}"
                    f"&provider={quote_plus(result.get('provider', ''))}&tool_name={quote_plus(result.get('tool_name', ''))}"
                )
                logger.info(f"Redirecting to success URL: {success_redirect_url}")
                return RedirectResponse(url=success_redirect_url, status_code=status.HTTP_302_FOUND)

            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": result["message"],
                    "provider": result.get("provider"),
                    "tool_name": result.get("tool_name"),
                },
            )
        else:
            logger.error(f"OAuth callback failed: {result['message']}")

            # If redirect_url is provided, redirect with failure parameters
            if redirect_url:
                failure_redirect_url = f"{redirect_url}?success=false&error=callback_failed&message={quote_plus(result['message'])}"
                logger.info(f"Redirecting to failure URL: {failure_redirect_url}")
                return RedirectResponse(url=failure_redirect_url, status_code=status.HTTP_302_FOUND)

            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"success": False, "message": result["message"]},
            )

    except Exception as e:
        logger.error(f"Unexpected error in oauth_callback: {str(e)}", exc_info=True)
        error_message = "An internal error occurred during OAuth callback."

        # If redirect_url is provided, redirect with error parameters
        if redirect_url:
            exception_redirect_url = f"{redirect_url}?success=false&error=internal_error&message={quote_plus(error_message)}"
            logger.info(f"Redirecting to exception URL: {exception_redirect_url}")
            return RedirectResponse(url=exception_redirect_url, status_code=status.HTTP_302_FOUND)

        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "message": error_message,
            },
        )


@oauth_router.get(
    "/credentials",
    response_model=OAuthCredentialResponse,
    summary="Get OAuth credentials",
    description="""
    Retrieve OAuth credentials for a specific tool and provider.

    This endpoint:
    - Retrieves stored OAuth credentials
    - Works with any configured provider
    - Updates last used timestamp
    - Returns fresh access tokens
    """,
    responses={
        200: {"description": "Credentials retrieved successfully"},
        401: {"description": "Unauthorized - User not authenticated"},
        404: {"description": "Credentials not found"},
        500: {"description": "Internal Server Error"},
    },
)
async def get_oauth_credentials(
    tool_name: str = Query(..., description="The name of the tool"),
    provider: Optional[OAuthProvider] = Query(OAuthProvider.GOOGLE, description="OAuth provider"),
    current_user: dict = Depends(role_required(["user"])),
):
    """Retrieve OAuth credentials for a user and tool."""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authenticated user context missing ID",
            )

        logger.info(
            f"Retrieving OAuth credentials for User: {user_id}, "
            f"Tool: {tool_name}, Provider: {provider}"
        )

        # Call authentication service to get credentials
        result = await get_auth_service_client().get_oauth_credentials(
            user_id=user_id, tool_name=tool_name, provider=provider
        )

        if result["success"]:
            return OAuthCredentialResponse(
                success=True,
                message=result["message"],
                token_type=result["token_type"],
                expires_in=result["expires_in"],
                scope=result["scope"],
            )
        else:
            if "not found" in result["message"]:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=result["message"])
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=result["message"]
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving OAuth credentials: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve OAuth credentials",
        )


@oauth_router.get(
    "/server/credentials",
    response_model=ServerOAuthCredentialResponse,
    summary="Get OAuth credentials (Server Authentication)",
    description="""
    Retrieve OAuth credentials using server authentication.

    This endpoint is for server-to-server communication and includes
    additional metadata about the credential.
    """,
    responses={
        200: {"description": "Credentials retrieved successfully"},
        401: {"description": "Unauthorized - Invalid server auth key"},
        404: {"description": "Credentials not found"},
        500: {"description": "Internal Server Error"},
    },
    dependencies=[Depends(validate_server_auth_key)],
)
async def get_oauth_credentials_server(
    user_id: str = Query(..., description="The user ID"),
    tool_name: str = Query(..., description="The name of the tool"),
    provider: Optional[OAuthProvider] = Query(OAuthProvider.GOOGLE, description="OAuth provider"),
):
    """Retrieve OAuth credentials using server authentication."""
    try:
        logger.info(
            f"Server retrieving OAuth credentials for User: {user_id}, "
            f"Tool: {tool_name}, Provider: {provider}"
        )

        # Call authentication service with authentication service specific server auth key
        from app.core.config import settings

        auth_service_key = settings.AUTH_SERVICE_SERVER_AUTH_KEY
        result = await get_auth_service_client().get_server_oauth_credentials(
            server_auth_key=auth_service_key,
            user_id=user_id,
            tool_name=tool_name,
            provider=provider,
        )

        if result["success"]:
            return ServerOAuthCredentialResponse(
                success=True,
                message=result["message"],
                user_id=result["user_id"],
                tool_name=result["tool_name"],
                provider=result["provider"],
                access_token=result["access_token"],
                refresh_token=result.get("refresh_token"),
                token_type=result["token_type"],
                expires_in=result["expires_in"],
                scope=result["scope"],
                user_scope=result.get("user_scope"),
                bot_token=result.get("bot_token"),
                bot_user_id=result.get("bot_user_id"),
                user_token=result.get("user_token"),
                user_id_slack=result.get("user_id_slack"),
                team_id=result.get("team_id"),
                team_name=result.get("team_name")
            )
        else:
            if "not found" in result["message"]:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=result["message"])
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=result["message"]
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error in server OAuth credentials retrieval: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve OAuth credentials",
        )


@oauth_router.delete(
    "/credentials",
    response_model=OAuthErrorResponse,
    summary="Delete OAuth credentials",
    description="""
    Delete OAuth credentials for a specific tool and provider.

    This endpoint:
    - Deletes stored OAuth credentials from both database and Secret Manager
    - Works with any configured provider
    - Requires user authentication
    - Returns success/failure status
    """,
    responses={
        200: {"description": "Credentials deleted successfully"},
        401: {"description": "Unauthorized - User not authenticated"},
        404: {"description": "Credentials not found"},
        500: {"description": "Internal Server Error"},
    },
)
async def delete_oauth_credentials(
    tool_name: str = Query(..., description="The name of the tool"),
    provider: Optional[OAuthProvider] = Query(OAuthProvider.GOOGLE, description="OAuth provider"),
    current_user: dict = Depends(role_required(["user"])),
):
    """Delete OAuth credentials for a user and tool."""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authenticated user context missing ID",
            )

        logger.info(
            f"Deleting OAuth credentials for User: {user_id}, "
            f"Tool: {tool_name}, Provider: {provider}"
        )

        # Call authentication service to delete credentials
        result = await get_auth_service_client().delete_oauth_credentials(
            user_id=user_id, tool_name=tool_name, provider=provider
        )

        if not result["success"]:
            # Check if it's a not found case
            if "not found" in result["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=result["message"],
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=result["message"],
                )

        logger.info(f"Successfully deleted OAuth credentials for user {user_id}, tool {tool_name}")
        return OAuthErrorResponse(success=True, message=result["message"])

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error in delete OAuth credentials: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete OAuth credentials",
        )


@oauth_router.get(
    "/health",
    summary="Authentication service health check",
    description="Check the health of the authentication service",
)
async def auth_service_health():
    """Check authentication service health."""
    try:
        result = await get_auth_service_client().health_check()

        return JSONResponse(
            status_code=(
                status.HTTP_200_OK if result["healthy"] else status.HTTP_503_SERVICE_UNAVAILABLE
            ),
            content=result,
        )
    except Exception as e:
        logger.error(f"Authentication service health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={"healthy": False, "status": "error", "message": str(e)},
        )
