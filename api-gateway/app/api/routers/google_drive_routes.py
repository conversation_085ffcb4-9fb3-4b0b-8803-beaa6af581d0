from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from typing import Optional, List

from app.core.security import validate_org_auth_key
from app.services.google_drive_service import GoogleDriveServiceClient
from app.schemas.google_drive import (
    DisconnectDriveRequest,
    DisconnectDriveResponse,
    SyncDriveRequest,
    SyncDriveResponse,
    ListFilesRequest,
    ListFilesResponse,
    GetFileDetailsRequest,
    GetFileDetailsResponse,
    GetFolderByIdRequest,
    GetFolderByIdResponse,
    SyncFolderByIdsRequest,
    SyncFolderByIdsResponse,
    CheckFileAccessRequest,
    CheckFileAccessResponse,
    SearchSimilarDocumentsRequest,
    SearchSimilarDocumentsResponse,
    BatchSearchSimilarDocumentsRequest,
    BatchSearchSimilarDocumentsResponse,
    SyncFileByUrlRequest,
    SyncFileByUrlResponse,
    SyncedFileInfo,
    ListTopLevelFoldersRequest,
    ListTopLevelFoldersResponse,
    DriveFileModel,
    FolderInfo,
    SearchResultItem,
    QueryResults,
    EntityInfo,
    RelationshipInfo,
    GraphContext
)

from app.core.auth_guard import role_required
from app.utils.parse_error import parse_error


google_drive_router = APIRouter(prefix="/google-drive", tags=["google-drive"])
google_drive_service = GoogleDriveServiceClient()


@google_drive_router.post(
    "/disconnect",
    response_model=DisconnectDriveResponse,
    status_code=status.HTTP_200_OK,
    summary="Disconnect Google Drive account",
)
async def disconnect_drive(
    request: DisconnectDriveRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """Disconnect Google Drive account."""
    try:
        # Call the Google Drive service
        response = await google_drive_service.disconnect_drive(
            organisation_id=request.organisation_id
        )
        
        # Map response to schema
        return DisconnectDriveResponse(
            success=response.success,
            message=response.message
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@google_drive_router.post(
    "/sync",
    response_model=SyncDriveResponse,
    status_code=status.HTTP_200_OK,
    summary="Sync Google Drive files and folders",
)
async def sync_drive(
    request: SyncDriveRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """Sync Google Drive files and folders."""
    try:
        # Validate that the user_id in the request matches the authenticated user
        if request.user_id != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="User ID mismatch")
        
        # Call the Google Drive service
        response = await google_drive_service.sync_drive(
            user_id=request.user_id,
            organisation_id=request.organisation_id,
            full_sync=request.full_sync
        )
        
        # Map response to schema
        return SyncDriveResponse(
            success=response.success,
            message=response.message,
            files_synced=response.files_synced,
            folders_synced=response.folders_synced,
            sync_status=response.sync_status
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@google_drive_router.post(
    "/list-files",
    response_model=ListFilesResponse,
    status_code=status.HTTP_200_OK,
    summary="List Google Drive files and folders",
)
async def list_files(
    request: ListFilesRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """List Google Drive files and folders."""
    try:
        # Validate that the user_id in the request matches the authenticated user
        if request.user_id != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="User ID mismatch")
        
        # Call the Google Drive service
        response = await google_drive_service.list_files(
            user_id=request.user_id,
            folder_id=request.folder_id,
            page=request.page,
            page_size=request.page_size
        )
        
        # Map response to schema
        files = [
            DriveFileModel(
                id=file.id,
                name=file.name,
                mime_type=file.mime_type,
                web_view_link=file.web_view_link,
                created_time=file.created_time,
                modified_time=file.modified_time,
                parent_folder_id=file.parent_folder_id,
                size=file.size,
                shared_with=list(file.shared_with),
                is_folder=file.is_folder,
                child_count=file.child_count
            )
            for file in response.files
        ]
        
        return ListFilesResponse(
            success=response.success,
            message=response.message,
            files=files,
            total_count=response.total_count,
            page=response.page,
            page_size=response.page_size
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@google_drive_router.post(
    "/file-details",
    response_model=GetFileDetailsResponse,
    status_code=status.HTTP_200_OK,
    summary="Get file details",
)
async def get_file_details(
    request: GetFileDetailsRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """Get file details."""
    try:
        # Validate that the user_id in the request matches the authenticated user
        if request.user_id != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="User ID mismatch")
        
        # Call the Google Drive service
        response = await google_drive_service.get_file_details(
            user_id=request.user_id,
            file_id=request.file_id
        )
        
        # Map response to schema
        file_data = None
        if response.file:
            file_data = DriveFileModel(
                id=response.file.id,
                name=response.file.name,
                mime_type=response.file.mime_type,
                web_view_link=response.file.web_view_link,
                created_time=response.file.created_time,
                modified_time=response.file.modified_time,
                parent_folder_id=response.file.parent_folder_id,
                size=response.file.size,
                shared_with=list(response.file.shared_with),
                is_folder=response.file.is_folder,
                child_count=response.file.child_count
            )
        
        return GetFileDetailsResponse(
            success=response.success,
            message=response.message,
            file=file_data
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])



@google_drive_router.post(
    "/check-access",
    response_model=CheckFileAccessResponse,
    status_code=status.HTTP_200_OK,
    summary="Check if user has access to a file",
)
async def check_file_access(
    request: CheckFileAccessRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """Check if user has access to a file."""
    try:
        # Validate that the user_id in the request matches the authenticated user
        if request.user_id != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="User ID mismatch")
        
        # Call the Google Drive service
        response = await google_drive_service.check_file_access(
            user_id=request.user_id,
            file_id=request.file_id
        )
        
        # Map response to schema
        return CheckFileAccessResponse(
            success=response.success,
            message=response.message,
            has_access=response.has_access
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@google_drive_router.post(
    "/search",
    dependencies=[Depends(validate_org_auth_key)],
    response_model=SearchSimilarDocumentsResponse,
    status_code=status.HTTP_200_OK,
    summary="Search for semantically similar documents",
)
async def search_similar_documents(
    request: SearchSimilarDocumentsRequest,
):
    """Search for documents semantically similar to a query."""
    try:
        # Call the Google Drive service
        response = await google_drive_service.search_similar_documents(
            user_id=request.user_id,
            query_text=request.query_text,
            top_k=request.top_k,
            agent_id=request.agent_id,
            organisation_id=request.organisation_id,
            file_ids=request.file_ids,
            least_score=request.least_score
        )
        
        # Map response to schema
        results = []
        for result in response.results:
            results.append(SearchResultItem(
                file_id=result.file_id,
                file_name=result.file_name,
                mime_type=result.mime_type,
                web_view_link=result.web_view_link,
                created_time=result.created_time,
                modified_time=result.modified_time,
                score=result.score,
                vector_id=result.vector_id,
                chunk_text=result.chunk_text,
                search_type=result.search_type
            ))
        
        # Map graph context if present
        graph_context = None
        if response.graph_context:
            # Map all entities
            all_entities = [
                EntityInfo(
                    id=entity.id,
                    name=entity.name,
                    type=entity.type,
                    properties=dict(entity.properties),
                    relevance_score=entity.relevance_score
                )
                for entity in response.graph_context.all_entities
            ]
            
            # Map all relationships
            all_relationships = [
                RelationshipInfo(
                    id=rel.id,
                    type=rel.type,
                    source_entity_id=rel.source_entity_id,
                    target_entity_id=rel.target_entity_id,
                    source_entity_name=rel.source_entity_name,
                    target_entity_name=rel.target_entity_name,
                    properties=dict(rel.properties),
                    confidence_score=rel.confidence_score,
                    relevance_score=rel.relevance_score,
                    context=rel.context
                )
                for rel in response.graph_context.all_relationships
            ]
            
            graph_context = GraphContext(
                all_entities=all_entities,
                all_relationships=all_relationships
            )
        
        return SearchSimilarDocumentsResponse(
            success=response.success,
            message=response.message,
            results=results,
            graph_context=graph_context
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@google_drive_router.post(
    "/batch-search",
    dependencies=[Depends(validate_org_auth_key)],
    response_model=BatchSearchSimilarDocumentsResponse,
    status_code=status.HTTP_200_OK,
    summary="Batch search for semantically similar documents",
)
async def batch_search_similar_documents(
    request: BatchSearchSimilarDocumentsRequest,
):
    """Batch search for documents semantically similar to multiple queries."""
    try:
        # Call the Google Drive service
        response = await google_drive_service.batch_search_similar_documents(
            user_id=request.user_id,
            query_texts=request.query_texts,
            top_k=request.top_k,
            agent_id=request.agent_id,
            organisation_id=request.organisation_id,
            file_ids=request.file_ids,
            least_score=request.least_score
        )
        
        # Map response to schema
        query_results = []
        for query_result in response.query_results:
            results = []
            for result in query_result.results:
                results.append(SearchResultItem(
                    file_id=result.file_id,
                    file_name=result.file_name,
                    mime_type=result.mime_type,
                    web_view_link=result.web_view_link,
                    created_time=result.created_time,
                    modified_time=result.modified_time,
                    score=result.score,
                    vector_id=result.vector_id,
                    chunk_text=result.chunk_text,
                    search_type=result.search_type
                ))
            
            query_results.append(QueryResults(
                query_text=query_result.query_text,
                results=results
            ))
        
        return BatchSearchSimilarDocumentsResponse(
            success=response.success,
            message=response.message,
            query_results=query_results
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@google_drive_router.post(
    "/folder-by-id",
    response_model=GetFolderByIdResponse,
    status_code=status.HTTP_200_OK,
    summary="Get folder by ID and its contents",
    description="""
    This endpoint retrieves a folder by its ID and its contents.
    
    - Requires user authentication
    - Returns folder details and its children
    """,
)
async def get_folder_by_id(
    request: GetFolderByIdRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """Get folder by ID and its contents."""
    try:

        # Call the Google Drive service
        response = await google_drive_service.get_folder_by_id(
            organisation_id=request.organisation_id,
            folder_id=request.folder_id
        )
        
        # Map response to schema
        folder = None
        if response.folder:
            folder = DriveFileModel(
                id=response.folder.id,
                name=response.folder.name,
                mime_type=response.folder.mime_type,
                web_view_link=response.folder.web_view_link,
                created_time=response.folder.created_time,
                modified_time=response.folder.modified_time,
                parent_folder_id=response.folder.parent_folder_id,
                size=response.folder.size,
                shared_with=list(response.folder.shared_with),
                is_folder=response.folder.is_folder,
                child_count=response.folder.child_count
            )
        
        children = [
            DriveFileModel(
                id=child.id,
                name=child.name,
                mime_type=child.mime_type,
                web_view_link=child.web_view_link,
                created_time=child.created_time,
                modified_time=child.modified_time,
                parent_folder_id=child.parent_folder_id,
                size=child.size,
                shared_with=list(child.shared_with),
                is_folder=child.is_folder,
                child_count=child.child_count
            )
            for child in response.children
        ]
        
        return GetFolderByIdResponse(
            success=response.success,
            message=response.message,
            folder=folder,
            children=children
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@google_drive_router.post(
    "/sync-folders",
    dependencies=[Depends(validate_org_auth_key)],
    response_model=SyncFolderByIdsResponse,
    status_code=status.HTTP_200_OK,
    summary="Sync folders by IDs",
    description="""
    This endpoint syncs specific folders by their IDs.
    
    - Requires user authentication
    - Returns sync status and list of synced folders
    """,
)
async def sync_folder_by_ids(
    request: SyncFolderByIdsRequest,
):
    """Sync folders by IDs."""
    try:        
        # Call the Google Drive service
        response = await google_drive_service.sync_folder_by_ids(
            organisation_id=request.organisation_id,
            folder_ids=request.folder_ids
        )
        
        # Map response to schema
        synced_folders = [
            FolderInfo(id=folder.id, name=folder.name)
            for folder in response.synced_folders
        ]
        
        return SyncFolderByIdsResponse(
            success=response.success,
            message=response.message,
            files_synced=response.files_synced,
            folders_synced=response.folders_synced,
            sync_status=response.sync_status,
            synced_folders=synced_folders
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@google_drive_router.post(
    "/sync-file-by-url",
    dependencies=[Depends(validate_org_auth_key)],
    response_model=SyncFileByUrlResponse,
    status_code=status.HTTP_200_OK,
    summary="Sync Google Drive files by URLs",
    description="""
    This endpoint syncs multiple Google Drive files by their URLs.
    
    - Requires X-Org-Auth-Key header authentication
    - Accepts a list of Google Drive URLs
    - Returns detailed sync status for each file
    """,
)
async def sync_file_by_url(
    request: SyncFileByUrlRequest,
):
    """Sync multiple Google Drive files by URLs."""
    try:
        # Call the Google Drive service
        response = await google_drive_service.sync_file_by_url(
            drive_urls=request.drive_url,  # Now passing list of URLs
            agent_id=request.agent_id,
            user_id=request.user_id,
            organisation_id=request.organisation_id
        )
        
        # Map response to schema
        synced_files = []
        for synced_file in response.synced_files:
            synced_files.append(SyncedFileInfo(
                file_id=synced_file.file_id,
                file_name=synced_file.file_name,
                drive_url=synced_file.drive_url,
                sync_status=synced_file.sync_status,
                error_message=synced_file.error_message
            ))
        
        return SyncFileByUrlResponse(
            success=response.success,
            message=response.message,
            synced_files=synced_files,
            total_files=response.total_files,
            successful_syncs=response.successful_syncs,
            failed_syncs=response.failed_syncs
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@google_drive_router.post(
    "/top-level-folders",
    response_model=ListTopLevelFoldersResponse,
    status_code=status.HTTP_200_OK,
    summary="List top-level folders using service account",
    description="""
    This endpoint lists top-level folders using the service account.
    
    - Requires user authentication with 'user' role
    - Returns a list of top-level folders
    """,
)
async def list_top_level_folders(
    request: ListTopLevelFoldersRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """List top-level folders using service account."""
    try:
        # Call the Google Drive service
        response = await google_drive_service.list_top_level_folders(
            organisation_id=request.organisation_id
        )
        
        # Map response to schema
        folders = [
            FolderInfo(id=folder.id, name=folder.name)
            for folder in response.folders
        ]
        
        return ListTopLevelFoldersResponse(
            success=response.success,
            message=response.message,
            folders=folders
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])