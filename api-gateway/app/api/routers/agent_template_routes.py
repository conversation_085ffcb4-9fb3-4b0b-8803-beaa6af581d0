from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from app.services.agent_service import AgentServiceClient
from app.schemas.agent import (
    AgentTemplateResponse,
    ListTemplatesResponse,
)
from app.core.auth_guard import role_required
from google.protobuf.json_format import MessageToDict
from app.services.user_service import UserServiceClient
from app.utils.parse_error import parse_error
import json

template_router = APIRouter(prefix="/agent-templates", tags=["agent-templates"])
agent_service = AgentServiceClient()
user_service = UserServiceClient()

@template_router.get("/{template_id}", response_model=AgentTemplateResponse)
async def get_template(template_id: str):
    try:
        print(f"[DEBUG] Getting template with ID: {template_id}")
        response = await agent_service.getTemplate(template_id)
        print(f"[DEBUG] Template response: {response}")

        template_dict = MessageToDict(response.template, preserving_proto_field_name=True)

        # Parse JSON string tags to dict if it's a string
        if isinstance(template_dict.get("tags"), str):
            template_dict["tags"] = json.loads(template_dict["tags"])

        # Convert string lists to actual lists
        if isinstance(template_dict.get("workflow_ids"), str):
            template_dict["workflow_ids"] = [template_dict["workflow_ids"]]
        if isinstance(template_dict.get("mcp_server_ids"), str):
            template_dict["mcp_server_ids"] = [template_dict["mcp_server_ids"]]

        return AgentTemplateResponse(
            success=response.success, message=response.message, template=template_dict
        )
    except Exception as e:
        print(f"[ERROR] Error in get_template: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@template_router.get("", response_model=ListTemplatesResponse)
async def list_templates(
    page: int = 1,
    page_size: int = 10,
    department: Optional[str] = Query(None, description="Filter by department"),
    organization_id: Optional[str] = Query(None, description="Filter by organization ID"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
):
    try:
        print("[DEBUG] Listing templates")

        response = await agent_service.listTemplates(page, page_size, department, organization_id,
            user_id
        )
        templates = []

        for template in response.templates:
            template_dict = MessageToDict(template, preserving_proto_field_name=True)

            # Parse JSON string tags to dict if it's a string
            if isinstance(template_dict.get("tags"), str):
                template_dict["tags"] = json.loads(template_dict["tags"])
            # Convert string lists to actual lists
            if isinstance(template_dict.get("workflow_ids"), str):
                print(f"[DEBUG] Workflow IDs before conversion: {template_dict['workflow_ids']}")
                template_dict["workflow_ids"] = [template_dict["workflow_ids"]]
            if isinstance(template_dict.get("mcp_server_ids"), str):
                print(f"[DEBUG] MCP Server IDs before conversion: {template_dict['mcp_server_ids']}")
                template_dict["mcp_server_ids"] = [template_dict["mcp_server_ids"]]

            templates.append(template_dict)

        print(f"[DEBUG] Processed templates: {templates}")
        return ListTemplatesResponse(
            templates=templates,
            total=response.total,
            page=response.page,
            total_pages=response.total_pages,
        )
    except Exception as e:
        print(f"[ERROR] Error in list_templates: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

