# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Logs
*.log
logs/

# Local development
.DS_Store
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/
test.py
test_mcp.py

# gRPC generated files
app/grpc_/*_pb2.py
app/grpc_/*_pb2_grpc.py
proto-definitions/
.aider*

# Markdown files (except README.md)
*.md
!README.md
