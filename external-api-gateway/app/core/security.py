from typing import List
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIKeyHeader
from jose import JWTError, jwt
from app.core.config import settings
from dotenv import load_dotenv
import os

load_dotenv()

# Keep OAuth2 scheme for backward compatibility
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/users/login", auto_error=False
)


async def get_current_user(token: str = Depends(oauth2_scheme)) -> dict:
    """
    Simplified user authentication for workflow routes.
    This is kept for backward compatibility with existing code.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    if not token:
        raise credentials_exception

    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        if not payload.get("sub"):
            raise credentials_exception
        return payload
    except JW<PERSON>rror:
        raise credentials_exception


server_auth_header = APIKeyHeader(name="X-Server-Auth-Key", auto_error=False)


def validate_server_auth_key(server_auth_key: str = Depends(server_auth_header)) -> bool:
    """
    Validate the server authentication key for server-to-server communication.

    Args:
        server_auth_key (str): The authentication key provided in the request header.

    Returns:
        bool: Returns True if the authentication key is valid.

    Raises:
        HTTPException: If the authentication key is missing or invalid.
    """
    # Retrieve the expected server authentication key from environment variables
    EXPECTED_SERVER_AUTH_KEY = os.getenv(
        "SERVER_AUTH_KEY", "hCbxAlpjFyXPd1UiJwZqTWgC20DrMb6YDxN7trz7OXZ"
    )

    # Check if the key is present and matches the expected key
    if not server_auth_key or server_auth_key != EXPECTED_SERVER_AUTH_KEY:
        raise HTTPException(status_code=403, detail="Invalid or missing server authentication key")

    return True


# API Key validation for workflow client
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)


def get_valid_api_keys() -> List[str]:
    """Get list of valid API keys from environment or use default development key."""
    # Get API keys from environment variable (comma-separated list)
    api_keys_env = os.getenv("WORKFLOW_CLIENT_API_KEYS", "dev-workflow-key")
    return [key.strip() for key in api_keys_env.split(",")]


def validate_api_key(api_key: str = Depends(api_key_header)) -> str:
    """
    Validate the API key for workflow client routes.

    This provides a simpler authentication mechanism for developers who want to use
    the workflow functionality without implementing the full JWT authentication.

    Args:
        api_key: The API key provided in the request header

    Returns:
        The validated API key

    Raises:
        HTTPException: If the API key is missing or invalid
    """
    valid_keys = get_valid_api_keys()

    if not api_key or api_key not in valid_keys:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Invalid or missing API key"
        )

    return api_key
