import threading
import time
import queue
import logging
from typing import Dict, Any


logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class SseManager:
    """
    Advanced Server-Sent Events (SSE) management class
    Handles client-specific and broadcast event distribution
    """

    _instance = None

    def __new__(cls):
        if not cls._instance:
            cls._instance = super(SseManager, cls).__new__(cls)
            cls._instance.initialize()
        return cls._instance

    def initialize(self):
        # Thread-safe dictionary for connected clients
        self.connected_clients: Dict[str, queue.Queue] = {}
        self.clients_lock = threading.Lock()
        # Shutdown flag
        self.is_shutting_down = False

    def add_client(self, client_id: str, client_queue: queue.Queue):
        """
        Register a new client connection
        """
        with self.clients_lock:
            self.connected_clients[client_id] = client_queue
        logger.info(f"New client connected: {client_id}")

    def remove_client(self, client_id: str):
        """
        Remove a client connection
        """
        with self.clients_lock:
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]
                logger.info(f"Client disconnected: {client_id}")

    def send_update(self, event_name: str, data: Any, client_id: str = None):
        """
        Send an update to specific client or broadcast

        :param event_name: Name of the event
        :param data: Event data
        :param client_id: Optional specific client ID to send to
        """
        event = {
            "event": "message",
            "data": data,
            "type": event_name,
            "timestamp": time.time(),
            "client_id": client_id,  # Include client_id in the event
        }

        with self.clients_lock:
            # If client_id is specified, send to that specific client
            if client_id and client_id in self.connected_clients:
                try:
                    self.connected_clients[client_id].put(event, block=False)
                    logger.info(f"Event sent to specific client: {client_id}")
                except queue.Full:
                    logger.warning(f"Client {client_id} queue is full, skipping event")
            # If no client_id, broadcast to all clients
            else:
                for queue in self.connected_clients.values():
                    try:
                        queue.put(event, block=False)
                    except queue.Full:
                        logger.warning("A client queue is full, skipping event")

    def clear_clients(self):
        """
        Clear all connected clients
        """
        with self.clients_lock:
            self.connected_clients.clear()
        logger.info("All clients cleared")

    def shutdown(self):
        """
        Graceful shutdown of SSE manager
        """
        self.is_shutting_down = True
        self.clear_clients()
        logger.info("SSE Manager shutting down")
