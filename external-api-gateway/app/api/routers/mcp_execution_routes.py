from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from app.core.auth_guard import role_required
from app.schemas.kafka import MCPExecutionRequest, MCPExecutionResponse, ServerMCPExecutionRequest
from app.services.kafka_service import kafka_service
from app.core.security import validate_server_auth_key

mcp_execution_router = APIRouter(prefix="/mcp-execute", tags=["MCPExecution"])


@mcp_execution_router.post(
    "/execute",
    status_code=status.HTTP_202_ACCEPTED,
    response_model=MCPExecutionResponse,
    name="Execute MCP Server",
    description="Execute an MCP server tool asynchronously using smart routing and return a request ID for tracking",
)
async def execute_mcp_server_endpoint(
    mcp_execution_request: MCPExecutionRequest,
    current_user: dict = Depends(role_required(["user", "admin"])),
) -> MCPExecutionResponse:
    """
    Endpoint to execute MCP server operations asynchronously using smart routing.

    This endpoint sends a request to the MCP execution service via Kafka and returns immediately
    with a request ID that can be used to track the execution. The service uses smart routing
    to automatically determine the connection type and retrieve user credentials.

    Args:
        mcp_execution_request: The MCP execution request containing mcp_id, tool_name, and tool_parameters
        current_user: Authenticated user context (user_id extracted from authentication)

    Returns:
        MCPExecutionResponse containing success status, message, and request ID

    Raises:
        HTTPException: If there's an error sending the request or validation fails
    """
    try:
        # Extract user_id from authentication context
        user_id = current_user["user_id"]

        # Convert Pydantic model to dict and add user_id from authentication
        request_data = mcp_execution_request.model_dump()
        request_data["user_id"] = user_id

        # Send the request to Kafka (all requests are async now)
        request_id = await kafka_service.send_mcp_execution_request(request_data)

        # Return structured response
        return MCPExecutionResponse(
            success=True,
            message="MCP execution request submitted successfully",
            request_id=request_id,
        )

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions
        raise http_exc
    except Exception as e:
        # Handle other exceptions
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


@mcp_execution_router.post(
    "/server",
    status_code=status.HTTP_202_ACCEPTED,
    response_model=MCPExecutionResponse,
    name="Execute MCP Server with Server Auth",
    description="Execute an MCP server tool asynchronously using smart routing with server authentication and return a request ID for tracking",
    dependencies=[Depends(validate_server_auth_key)],
)
async def execute_mcp_server_auth_endpoint(
    mcp_execution_request: ServerMCPExecutionRequest,
) -> MCPExecutionResponse:
    """
    Endpoint to execute MCP server operations asynchronously using smart routing with server authentication.

    This endpoint sends a request to the MCP execution service via Kafka and returns immediately
    with a request ID that can be used to track the execution. The service uses smart routing
    to automatically determine the connection type and retrieve user credentials.

    Args:
        mcp_execution_request: The MCP execution request containing user_id, mcp_id, tool_name, and tool_parameters

    Returns:
        MCPExecutionResponse containing success status, message, and request ID

    Raises:
        HTTPException: If there's an error sending the request or validation fails
    """
    try:
        # Convert Pydantic model to dict (user_id is already included in the request)
        request_data = mcp_execution_request.model_dump()

        # Send the request to Kafka (all requests are async now)
        request_id = await kafka_service.send_mcp_execution_request(request_data)

        # Return structured response
        return MCPExecutionResponse(
            success=True,
            message="MCP execution request submitted successfully",
            request_id=request_id,
        )

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions
        raise http_exc
    except Exception as e:
        # Handle other exceptions
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


@mcp_execution_router.get("/stream/{request_id}")
async def stream_mcp_updates(request_id: str) -> StreamingResponse:
    """
    Streams MCP execution updates via SSE by consuming Kafka messages for a specific request ID.

    Args:
        request_id: The request ID to stream updates for

    Returns:
        A streaming response with SSE formatted updates
    """
    print(f"Starting SSE stream for MCP request_id: {request_id}")
    return StreamingResponse(
        kafka_service.kafka_response_generator(request_id),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # Disable buffering for Nginx
        },
    )
