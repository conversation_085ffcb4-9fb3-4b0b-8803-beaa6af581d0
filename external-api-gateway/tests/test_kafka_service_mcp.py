import pytest
import asyncio
import json
import sys
import os
from unittest.mock import patch, MagicMock, AsyncMock

# Add the app directory to the path so we can import from it
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from app.services.kafka_service import KafkaService, mcp_response_queues


@pytest.fixture
def kafka_service():
    service = KafkaService()
    service.producer = AsyncMock()
    service.consumer = AsyncMock()
    service._initialized = True
    return service


@pytest.mark.asyncio
async def test_send_mcp_execution_request_sync(kafka_service):
    """Test synchronous MCP execution request"""
    # Mock data
    request_data = {
        "server_script_path": "test_path",
        "tool_name": "test_tool",
        "tool_parameters": {"param1": "value1"},
        "retries": 2,
    }

    # Mock response
    mock_response = {"result": "success", "data": "test_data"}

    # Mock the queue.get method to return our mock response
    with (
        patch("asyncio.Queue.get", new_callable=AsyncMock) as mock_get,
        patch("uuid.uuid4") as mock_uuid,
    ):
        # Mock the UUID to get a predictable result
        test_uuid = "test-uuid-1234"
        mock_uuid.return_value = test_uuid
        mock_get.return_value = mock_response

        # Call the method
        response = await kafka_service.send_mcp_execution_request(request_data)

        # Verify the response
        assert response == mock_response

        # Verify that producer.send was called with the correct topic and message
        kafka_service.producer.send.assert_called_once()
        args, _ = kafka_service.producer.send.call_args
        assert args[0] == "mcp-execution-request"  # Topic

        # Verify that the request_id is in the message
        message = args[1]
        assert "request_id" in message
        assert message["server_script_path"] == "test_path"
        assert message["tool_name"] == "test_tool"
        assert message["tool_parameters"] == {"param1": "value1"}
        assert message["retries"] == 2

        # Clean up
        request_id = message["request_id"]
        if request_id in mcp_response_queues:
            del mcp_response_queues[request_id]


@pytest.mark.asyncio
async def test_send_mcp_execution_request_async(kafka_service):
    """Test asynchronous MCP execution request"""
    # Mock data with async_execution flag
    request_data = {
        "server_script_path": "test_path",
        "tool_name": "test_tool",
        "tool_parameters": {"param1": "value1"},
        "async_execution": True,
    }

    # Mock the UUID to get a predictable result
    with patch("uuid.uuid4") as mock_uuid:
        test_uuid = "test-uuid-5678"
        mock_uuid.return_value = test_uuid

        # Call the method
        response = await kafka_service.send_mcp_execution_request(request_data)

        # For async requests, the response should be the request_id
        assert response == test_uuid

        # Verify that producer.send was called with the correct topic and message
        kafka_service.producer.send.assert_called_once()
        args, _ = kafka_service.producer.send.call_args
        assert args[0] == "mcp-execution-request"  # Topic

        # Verify the message content
        message = args[1]
        assert message["request_id"] == test_uuid
        assert message["server_script_path"] == "test_path"
        assert message["tool_name"] == "test_tool"
        assert message["tool_parameters"] == {"param1": "value1"}

        # Clean up
        if test_uuid in mcp_response_queues:
            del mcp_response_queues[test_uuid]


@pytest.mark.asyncio
async def test_send_mcp_execution_request_validation(kafka_service):
    """Test validation of required fields in MCP execution request"""
    # Missing required fields
    request_data = {
        "tool_name": "test_tool",  # Missing server_script_path
        "tool_parameters": {"param1": "value1"},
    }

    # Expect a ValueError to be raised and converted to HTTPException
    with pytest.raises(Exception) as excinfo:
        await kafka_service.send_mcp_execution_request(request_data)

    # Verify the error message
    assert "Missing required field" in str(excinfo.value)


@pytest.mark.asyncio
async def test_background_consumer_task_mcp_response(kafka_service):
    # Create a mock message
    request_id = "test-request-id"
    mock_message = MagicMock()
    mock_message.topic = "mcp_results"
    mock_message.value = json.dumps({"result": "success", "request_id": request_id}).encode("utf-8")
    mock_message.headers = []

    # Create a mock queue
    mock_queue = AsyncMock()
    mcp_response_queues[request_id] = mock_queue

    # Mock the consumer.__aiter__ to return our mock message
    kafka_service.consumer.__aiter__.return_value = [mock_message]

    # Create a task for _background_consumer_task that will run once and exit
    task = asyncio.create_task(kafka_service._background_consumer_task())

    # Give the task a moment to process the message
    await asyncio.sleep(0.1)

    # Cancel the task (since it's an infinite loop)
    task.cancel()
    try:
        await task
    except asyncio.CancelledError:
        pass

    # Verify that queue.put was called with the correct response
    mock_queue.put.assert_called_once()
    args, _ = mock_queue.put.call_args
    assert args[0]["result"] == "success"
    assert args[0]["request_id"] == request_id

    # Clean up
    if request_id in mcp_response_queues:
        del mcp_response_queues[request_id]


@pytest.mark.asyncio
async def test_background_consumer_task_ignores_non_matching_request_id(kafka_service):
    # Create a mock message with a non-matching request_id
    request_id = "non-matching-id"
    mock_message = MagicMock()
    mock_message.topic = "mcp_results"
    mock_message.value = json.dumps({"result": "success", "request_id": request_id}).encode("utf-8")
    mock_message.headers = []

    # We don't add this request_id to mcp_response_queues, so it should be ignored

    # Mock the consumer.__aiter__ to return our mock message
    kafka_service.consumer.__aiter__.return_value = [mock_message]

    # Create a task for _background_consumer_task that will run once and exit
    task = asyncio.create_task(kafka_service._background_consumer_task())

    # Give the task a moment to process the message
    await asyncio.sleep(0.1)

    # Cancel the task (since it's an infinite loop)
    task.cancel()
    try:
        await task
    except asyncio.CancelledError:
        pass

    # Verify that no queue was created for this request_id
    assert request_id not in mcp_response_queues
