#!/usr/bin/env python3
"""
Test script to validate the Kafka service validation logic.
"""
import asyncio
import json
from unittest.mock import AsyncMock, patch
from app.services.kafka_service import KafkaService

async def test_legacy_request_validation():
    """Test legacy request validation."""
    print("🧪 Testing legacy request validation...")
    
    kafka_service = KafkaService()
    
    legacy_request = {
        "server_script_path": "https://script-generation-mcp-dev-************.us-central1.run.app/sse",
        "tool_name": "script_generate",
        "tool_parameters": {
            "topic": "Wildlife",
            "script_type": "TOPIC",
            "video_type": "SHORT"
        }
    }
    
    try:
        # Mock the Kafka producer and consumer
        with patch.object(kafka_service, '_ensure_initialized', return_value=None):
            with patch.object(kafka_service, 'producer', AsyncMock()):
                request_id = await kafka_service.send_mcp_execution_request(legacy_request)
                print(f"✅ Legacy request validation PASSED - Request ID: {request_id}")
                return True
    except Exception as e:
        print(f"❌ Legacy request validation FAILED: {e}")
        return False

async def test_smart_routing_validation():
    """Test smart routing request validation."""
    print("\n🧪 Testing smart routing request validation...")
    
    kafka_service = KafkaService()
    
    smart_request = {
        "mcp_id": "0d183177-51a1-498a-b190-b10a93e7f668",
        "user_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
        "tool_name": "script_generate",
        "tool_parameters": {
            "topic": "Wildlife Adventure",
            "script_type": "TOPIC",
            "video_type": "SHORT"
        }
    }
    
    try:
        # Mock the Kafka producer and consumer
        with patch.object(kafka_service, '_ensure_initialized', return_value=None):
            with patch.object(kafka_service, 'producer', AsyncMock()):
                request_id = await kafka_service.send_mcp_execution_request(smart_request)
                print(f"✅ Smart routing request validation PASSED - Request ID: {request_id}")
                return True
    except Exception as e:
        print(f"❌ Smart routing request validation FAILED: {e}")
        return False

async def test_mixed_request_validation():
    """Test mixed request validation."""
    print("\n🧪 Testing mixed request validation...")
    
    kafka_service = KafkaService()
    
    mixed_request = {
        "server_script_path": "https://script-generation-mcp-dev-************.us-central1.run.app/sse",
        "mcp_id": "0d183177-51a1-498a-b190-b10a93e7f668",
        "user_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
        "tool_name": "script_generate",
        "tool_parameters": {
            "topic": "Wildlife",
            "script_type": "TOPIC",
            "video_type": "SHORT"
        }
    }
    
    try:
        # Mock the Kafka producer and consumer
        with patch.object(kafka_service, '_ensure_initialized', return_value=None):
            with patch.object(kafka_service, 'producer', AsyncMock()):
                request_id = await kafka_service.send_mcp_execution_request(mixed_request)
                print(f"✅ Mixed request validation PASSED - Request ID: {request_id}")
                return True
    except Exception as e:
        print(f"❌ Mixed request validation FAILED: {e}")
        return False

async def test_invalid_requests():
    """Test invalid request validation."""
    print("\n🧪 Testing invalid request validation...")
    
    kafka_service = KafkaService()
    
    # Test 1: Missing both execution methods
    invalid_request_1 = {
        "tool_name": "script_generate",
        "tool_parameters": {"topic": "Wildlife"}
    }
    
    try:
        with patch.object(kafka_service, '_ensure_initialized', return_value=None):
            await kafka_service.send_mcp_execution_request(invalid_request_1)
        print("❌ Should have failed for missing execution method")
        return False
    except ValueError as e:
        if "Either server_script_path or mcp_id must be provided" in str(e):
            print("✅ Correctly rejected missing execution method")
        else:
            print(f"❌ Wrong error message: {e}")
            return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    # Test 2: Smart routing without user_id
    invalid_request_2 = {
        "mcp_id": "test-mcp",
        "tool_name": "script_generate",
        "tool_parameters": {"topic": "Wildlife"}
    }
    
    try:
        with patch.object(kafka_service, '_ensure_initialized', return_value=None):
            await kafka_service.send_mcp_execution_request(invalid_request_2)
        print("❌ Should have failed for missing user_id")
        return False
    except ValueError as e:
        if "user_id is required when using smart routing" in str(e):
            print("✅ Correctly rejected smart routing without user_id")
        else:
            print(f"❌ Wrong error message: {e}")
            return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return True

async def test_payload_preparation():
    """Test payload preparation with different field combinations."""
    print("\n🧪 Testing payload preparation...")
    
    kafka_service = KafkaService()
    
    full_request = {
        "mcp_id": "test-mcp",
        "user_id": "test-user",
        "tool_name": "script_generate",
        "tool_parameters": {"topic": "Wildlife"},
        "connection_type": "sse",
        "headers": {"Authorization": "Bearer token"},
        "docker_image": "test:latest",
        "container_command": "python script.py",
        "retries": 5
    }
    
    # Mock the producer.send method to capture the payload
    captured_payload = None
    
    async def mock_send(topic, payload, headers=None):
        nonlocal captured_payload
        captured_payload = payload
    
    try:
        with patch.object(kafka_service, '_ensure_initialized', return_value=None):
            with patch.object(kafka_service, 'producer') as mock_producer:
                mock_producer.send = mock_send
                mock_producer.flush = AsyncMock()
                
                await kafka_service.send_mcp_execution_request(full_request)
                
                print("✅ Payload preparation completed")
                print("📋 Captured payload:")
                print(json.dumps(captured_payload, indent=2))
                
                # Verify all fields are present
                expected_fields = ["mcp_id", "user_id", "tool_name", "tool_parameters", 
                                 "connection_type", "headers", "docker_image", 
                                 "container_command", "retries", "request_id"]
                
                for field in expected_fields:
                    if field not in captured_payload:
                        print(f"❌ Missing field in payload: {field}")
                        return False
                
                print("✅ All expected fields present in payload")
                return True
                
    except Exception as e:
        print(f"❌ Payload preparation failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Testing Kafka Service Validation")
    print("=" * 50)
    
    tests = [
        test_legacy_request_validation,
        test_smart_routing_validation,
        test_mixed_request_validation,
        test_invalid_requests,
        test_payload_preparation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if await test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Kafka service validation tests passed!")
        print("The external API gateway is ready for smart routing.")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
