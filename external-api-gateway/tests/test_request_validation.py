#!/usr/bin/env python3
"""
Test script to validate the exact request format that's being sent.
"""
import json
from app.schemas.kafka import MCPExecutionRequest

def test_exact_request():
    """Test the exact request format being sent to the API."""
    print("🧪 Testing exact request format from test script...")
    
    # This is the exact payload from the test script
    request_data = {
        "tool_name": "script_generate",
        "tool_parameters": {
            "topic": "Wildlife Adventure",
            "video_type": "SHORT",
            "script_type": "TOPIC",
            "keywords": {
                "time": "1 min",
                "objective": "marketing",
                "audience": "adults",
                "gender": "male",
                "tone": "bold",
                "speakers": "me"
            }
        },
        "mcp_id": "0d183177-51a1-498a-b190-b10a93e7f668",
        "user_id": "91a237fd-0225-4e02-9e9f-805eff073b07"
    }
    
    print("📋 Request payload:")
    print(json.dumps(request_data, indent=2))
    
    try:
        request = MCPExecutionRequest(**request_data)
        print("\n✅ Request validation PASSED")
        print(f"   server_script_path: {request.server_script_path}")
        print(f"   mcp_id: {request.mcp_id}")
        print(f"   user_id: {request.user_id}")
        print(f"   tool_name: {request.tool_name}")
        print(f"   retries: {request.retries}")
        
        # Test serialization
        serialized = request.model_dump()
        print(f"\n📤 Serialized request:")
        print(json.dumps(serialized, indent=2))
        
        return True
    except Exception as e:
        print(f"\n❌ Request validation FAILED: {e}")
        return False

def test_minimal_smart_routing():
    """Test minimal smart routing request."""
    print("\n🧪 Testing minimal smart routing request...")
    
    minimal_request = {
        "mcp_id": "test-mcp",
        "user_id": "test-user",
        "tool_name": "test_tool",
        "tool_parameters": {"param": "value"}
    }
    
    try:
        request = MCPExecutionRequest(**minimal_request)
        print("✅ Minimal request validation PASSED")
        print(f"   mcp_id: {request.mcp_id}")
        print(f"   user_id: {request.user_id}")
        print(f"   retries: {request.retries}")  # Should use default
        return True
    except Exception as e:
        print(f"❌ Minimal request validation FAILED: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Request Validation")
    print("=" * 50)
    
    test1_passed = test_exact_request()
    test2_passed = test_minimal_smart_routing()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("🎉 All validation tests passed!")
        print("The 422 error is likely from the API server, not the schema.")
    else:
        print("⚠️  Validation tests failed. Schema needs fixing.")
