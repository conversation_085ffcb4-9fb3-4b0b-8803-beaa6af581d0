"""
Combine Text Component - Joins text inputs with a separator, supporting a variable number of inputs.

Special characters: Use __SPACE__, __TAB__, __NEWLINE__, __RETURN__, __CRLF__ for common separators.
Leave separator blank for direct combining (no separator).
"""
import logging
import traceback
import j<PERSON>
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator, ValidationError

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)


# Define request schema using Pydantic
class CombineTextRequest(BaseModel):
    """Schema for text combination requests."""
    main_input: Optional[Union[str, List[str], List[Any]]] = Field(None, description="The main text or list to combine")
    num_additional_inputs: int = Field(2, description="Number of additional inputs to process")
    separator: str = Field("", description="The separator to use for joining (default is empty string for direct combining). Use __SPACE__, __TAB__, __NEWLINE__ for special characters.")
    # Dynamic inputs - we'll handle these in the validation and processing methods
    input_1: Optional[str] = Field(None, description="Additional input 1")
    input_2: Optional[str] = Field(None, description="Additional input 2")
    input_3: Optional[str] = Field(None, description="Additional input 3")
    input_4: Optional[str] = Field(None, description="Additional input 4")
    input_5: Optional[str] = Field(None, description="Additional input 5")
    input_6: Optional[str] = Field(None, description="Additional input 6")
    input_7: Optional[str] = Field(None, description="Additional input 7")
    input_8: Optional[str] = Field(None, description="Additional input 8")
    input_9: Optional[str] = Field(None, description="Additional input 9")
    input_10: Optional[str] = Field(None, description="Additional input 10")

    # Add validator to ensure main_input is provided
    @field_validator('main_input')
    def check_main_input(cls, v):
        if v is None:
            raise ValueError("Main input is required")
        return v


# Register the component
@register_component("CombineTextComponent")
class CombineTextComponent(BaseComponent):
    """
    Component for combining text inputs with a separator, supporting a variable number of inputs.

    This component takes a main input and a configurable number of additional text inputs,
    each with its own connection handle, and joins them with the specified separator.
    
    Special characters: Use __SPACE__, __TAB__, __NEWLINE__, __RETURN__, __CRLF__ for common separators.
    Leave separator blank for direct combining (no separator).
    """

    # Maximum number of additional inputs to support
    MAX_ADDITIONAL_INPUTS = 10

    def __init__(self):
        """
        Initialize the CombineTextComponent component.
        """
        logger.info("Initializing Combine Text Component")
        super().__init__()
        # Set the request schema for automatic validation
        self.request_schema = CombineTextRequest
        logger.info("Combine Text Component initialized successfully")

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate a text combination payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Validating text combination payload for request_id: {request_id}")
        logger.debug(f"Payload to validate for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for validation.")
            # Use the tool_parameters as the actual payload for validation
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        logger.info(f"VALIDATION PARAMETERS KEYS: {list(parameters.keys())}")

        try:
            # First use schema validation (from parent class)
            # We still validate the original payload for backward compatibility
            schema_validation = await super().validate(payload)
            if not schema_validation.is_valid:
                # Try validating the parameters instead
                try:
                    CombineTextRequest(**parameters)
                except ValidationError as e:
                    logger.error(f"Schema validation failed for request_id {request_id}: {schema_validation.error_message}")
                    return schema_validation
            logger.debug(f"Schema validation passed for request_id {request_id}")

            # Check for main_input
            if "main_input" not in parameters:
                error_msg = f"Required field 'main_input' not found in parameters for request_id {request_id}"
                logger.error(error_msg)
                logger.error(f"Available keys in parameters: {list(parameters.keys())}")
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"main_input": "The main_input field is required"}
                )

            # Validate num_additional_inputs
            num_additional_inputs = parameters.get("num_additional_inputs")
            if num_additional_inputs is not None:
                try:
                    num_additional_inputs = int(num_additional_inputs)
                    if num_additional_inputs < 0 or num_additional_inputs > self.MAX_ADDITIONAL_INPUTS:
                        error_msg = f"Field 'num_additional_inputs' must be between 0 and {self.MAX_ADDITIONAL_INPUTS}, got {num_additional_inputs} for request_id {request_id}"
                        logger.error(error_msg)
                        return ValidationResult(
                            is_valid=False,
                            error_message=error_msg,
                            error_details={"num_additional_inputs": f"must be between 0 and {self.MAX_ADDITIONAL_INPUTS}"}
                        )
                except (ValueError, TypeError):
                    error_msg = f"Field 'num_additional_inputs' must be an integer, got {type(num_additional_inputs).__name__} for request_id {request_id}"
                    logger.error(error_msg)
                    return ValidationResult(
                        is_valid=False,
                        error_message=error_msg,
                        error_details={"num_additional_inputs": "must be an integer"}
                    )

            logger.info(f"Text combination payload validation passed for request_id {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected error during payload validation for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return ValidationResult(is_valid=False, error_message=error_msg, error_details={"validation_error": str(e)})

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the text combination operation.

        Args:
            payload: The request payload containing:
                - main_input: The main input data (string or list)
                - num_additional_inputs: Number of additional inputs to process
                - input_1, input_2, etc.: Additional text inputs
                - separator: The separator to use for joining (optional)

        Returns:
            A dictionary containing the combined text or an error message
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing text combination request for request_id: {request_id}")
        # Log the full payload with all keys to help debug
        logger.info(f"PAYLOAD KEYS: {list(payload.keys())}")
        logger.info(f"FULL PAYLOAD: {payload}")
        logger.debug(f"Full payload for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for parameters.")
            # Use the tool_parameters as the actual payload
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        logger.info(f"PARAMETERS KEYS: {list(parameters.keys())}")

        try:
            # Get inputs from parameters
            main_input = parameters.get("main_input")
            if main_input is None:
                raise KeyError("Required field 'main_input' not found in parameters")

            # Try to parse main_input as JSON if it looks like a list or dict and is a string
            if (
                isinstance(main_input, str)
                and (
                    (main_input.startswith("[") and main_input.endswith("]"))
                    or (main_input.startswith("{") and main_input.endswith("}"))
                )
            ):
                try:
                    main_input = json.loads(main_input)
                    logger.debug(f"Parsed main input as JSON: {type(main_input).__name__}")
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse main input as JSON: {e}")
                    # Keep as string if parsing fails

            # Get number of additional inputs
            try:
                num_additional_inputs = int(parameters.get("num_additional_inputs", 2))
                num_additional_inputs = min(num_additional_inputs, self.MAX_ADDITIONAL_INPUTS)
            except (ValueError, TypeError):
                logger.warning(f"Invalid num_additional_inputs, using default value of 2")
                num_additional_inputs = 2

            separator = parameters.get("separator", "")  # Default to empty string for direct combining

            logger.info(f"Combining text for request_id {request_id}. Separator: '{separator}', Num additional inputs: {num_additional_inputs}")
            logger.debug(f"Main input type: {type(main_input).__name__}")

            # Process separator - handle common separator patterns and special characters
            processed_separator = separator
            # Only process escape sequences if the separator is a string with escape characters
            if isinstance(separator, str):
                # Handle special character placeholders
                if separator == "__SPACE__":
                    processed_separator = " "
                elif separator == "__TAB__":
                    processed_separator = "\t"
                elif separator == "__NEWLINE__":
                    processed_separator = "\n"
                elif separator == "__RETURN__":
                    processed_separator = "\r"
                elif separator == "__CRLF__":
                    processed_separator = "\r\n"
                # Handle escape sequences (for backward compatibility)
                elif separator == "\\n":
                    processed_separator = "\n"
                elif separator == "\\t":
                    processed_separator = "\t"
                elif separator == "\\r":
                    processed_separator = "\r"
                elif separator == "\\r\\n":
                    processed_separator = "\r\n"
                # For other cases, use the separator as is (including empty string)

            # Fix: Avoid backslash in f-string expression by performing replacements beforehand
            display_separator = processed_separator.replace('\n', '\\n').replace('\t', '\\t').replace('\r', '\\r')
            logger.debug(f"Processed separator: '{display_separator}' for request_id {request_id}")

            # Prepare the list of strings to join
            strings_to_join = []

            # Process the main input
            if isinstance(main_input, list):
                # Convert all list items to strings
                for item in main_input:
                    if item is not None:  # Skip None values
                        strings_to_join.append(str(item))
                        logger.debug(f"Added item from main_input list")
                    else:
                        logger.debug(f"Skipping None item in main_input list")
            else:
                # Convert single input to string
                strings_to_join.append(str(main_input))
                logger.debug(f"Added main_input as string")

            # Process additional inputs
            for i in range(1, num_additional_inputs + 1):
                input_name = f"input_{i}"
                input_value = parameters.get(input_name)

                if input_value is not None and input_value != "":
                    strings_to_join.append(str(input_value))
                    logger.debug(f"Added {input_name}")
                else:
                    logger.debug(f"Skipping empty or None {input_name}")

            # Join the strings with the separator
            result = processed_separator.join(strings_to_join)
            logger.info(f"Text combined successfully for request_id {request_id}. Result length: {len(result)}")
            logger.debug(f"Combined text (truncated for log) for request_id {request_id}: {result[:200]}...")

            return {
                "status": "success",
                "result": result
            }

        except Exception as e:
            error_msg = f"Error combining text for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {
                "status": "error",
                "error": error_msg
            }
