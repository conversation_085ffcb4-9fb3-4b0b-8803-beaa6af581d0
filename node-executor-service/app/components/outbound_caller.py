import logging
import traceback
import j<PERSON>
from typing import Dict, Any, Optional

import httpx
from pydantic import BaseModel, Field, field_validator

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component
from app.config.config import settings # Import the centralized settings object

logger = logging.getLogger(__name__)

# --- Pydantic Schema for the Request ---
class StartOutboundCallRequest(BaseModel):
    """Schema for starting an outbound AI-powered call."""
    phone_no: str = Field(..., description="The target phone number to call.")
    systemprompt: Optional[str] = Field(None, description="Initial instructions for the AI agent.")
    user_id: Optional[str] = Field(None, description="ID of the user initiating the call.")
    call_id: Optional[str] = Field(None, description="Unique ID for this call.")
    agent_id: Optional[str] = Field(None, description="ID of the agent handling the call.")
    transfer_no: Optional[str] = Field(None, description="Number to transfer the call to if needed.")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context for the call agent.")
    request_id: Optional[str] = Field(None, description="Unique request identifier.")

    @field_validator('phone_no')
    def validate_phone_no(cls, v):
        if not v or not v.strip():
            raise ValueError("Phone number cannot be empty.")
        return v

# --- Main Component Class ---
@register_component("StartOutboundCallComponent")
class StartOutboundCallComponent(BaseComponent):
    """
    Component to initiate an outbound call via a dedicated AI agent service.
    """

    def __init__(self):
        """Initialize the StartOutboundCallComponent."""
        logger.info("Initializing Start Outbound Call Component")
        super().__init__()
        self.request_schema = StartOutboundCallRequest
        # The configuration is now handled by the centralized settings object.
        # Pydantic will raise an error on startup if the required variables are missing.
        logger.info("Start Outbound Call Component initialized successfully")

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate the payload for the outbound call.
        Leverages the Pydantic schema validation from the base class.
        """
        # The base `validate` method will automatically use `self.request_schema`.
        # We can simply call it and return the result.
        return await super().validate(payload)

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes the request to start an outbound call.

        Args:
            payload: A dictionary matching the StartOutboundCallRequest schema.

        Returns:
            A dictionary with the result of the API call.
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing outbound call request for request_id: {request_id}")

        # Configuration is now accessed via the 'settings' object.
        # Pydantic ensures these are loaded at startup, so we can rely on them being available.
        try:
            # 1. Prepare the request body from the payload.
            # Pydantic has already validated the types, so we can use them directly.
            # We filter out `request_id` as it's not part of the API's expected body.
            api_body = {
                key: value for key, value in payload.items()
                if key != 'request_id' and value is not None
            }
            logger.info(f"the server auth key is {settings.server_auth_key}")
            logger.info(f"the api base url is {settings.caller_api_base_url}")
            # 2. Prepare headers and URL
            endpoint_url = f"{settings.caller_api_base_url}/api/v1/outbound/node-call"
            headers = {
                "server-auth-key": settings.server_auth_key,
                "Content-Type": "application/json",
                "User-Agent": "WorkflowExecutorService/1.0"
            }

            logger.info(f"Making POST request to {endpoint_url} for request_id: {request_id}")
            logger.debug(f"Request body for {request_id}: {json.dumps(api_body)}")

            # 3. Make the asynchronous HTTP call using httpx
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    endpoint_url,
                    json=api_body,
                    headers=headers,
                    timeout=30.0  # 30-second timeout
                )
                
                # Raise an exception for non-2xx status codes. This is a best practice.
                response.raise_for_status()

                # 4. Process the successful response
                response_data = response.json()
                logger.info(f"Successfully initiated outbound call for request_id: {request_id}. Status: {response.status_code}")
                
                return {
                    "status": "success",
                    "result": {
                        "api_response": response_data,
                        "status_code": response.status_code
                    }
                }

        except httpx.HTTPStatusError as e:
            # Handle specific HTTP errors (e.g., 4xx, 5xx)
            error_body = e.response.text
            error_msg = f"API call failed with status {e.response.status_code}: {error_body}"
            logger.error(f"{error_msg} (request_id: {request_id})")
            return {
                "status": "error",
                "error": f"API Error: {error_body}",
                "status_code": e.response.status_code
            }
        except httpx.RequestError as e:
            # Handle network errors (e.g., connection refused, timeout)
            error_msg = f"Network error while calling outbound service: {e}"
            logger.error(f"{error_msg} (request_id: {request_id})")
            return {"status": "error", "error": f"Network Error: {e}"}
        except Exception as e:
            # Handle any other unexpected errors
            error_msg = f"Unexpected error processing outbound call for request_id: {e}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            return {"status": "error", "error": "An unexpected internal error occurred."}