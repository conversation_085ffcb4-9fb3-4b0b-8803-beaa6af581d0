{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AgentConfiguration", "description": "Schema for agent configuration used to create and register new agents.", "type": "object", "properties": {"agent_category": {"type": "string", "enum": ["Interactive", "user_proxy", "Assistant"], "description": "Specifies the agent type (e.g., 'AIAgent', 'Assistant')."}, "description": {"type": "string", "description": "A brief description outlining the purpose and functionality of the agent."}, "system_message": {"type": "string", "description": "The initial system prompt or instructions that define the agent's behavior."}, "model_client": {"type": "object", "description": "Configuration for the model client used by the agent.", "properties": {"provider": {"type": "string", "description": "Model client provider (e.g., 'OpenAIChatCompletionClient')."}, "model": {"type": "string", "description": "Identifier for the model (e.g., 'gpt-4o-mini')."}, "api_key": {"type": "string", "description": "API key for authenticating with the model client."}}, "required": ["provider", "model", "api_key"]}, "agent_tools": {"type": "array", "description": "A list of tool configurations available to the agent. Each tool configuration supports either a 'workflow' or an 'mcp_sse' type.", "items": {"type": "object", "properties": {"tool_type": {"type": "string", "enum": ["workflow", "mcp_sse"], "description": "Type of tool. Valid values include 'workflow' and 'mcp_sse'."}, "workflow": {"type": "object", "description": "Workflow configuration details for the tool. Required if tool_type is 'workflow'.", "properties": {"workflow_id": {"type": "string", "description": "Name of the workflow."}, "description": {"type": "string", "description": "Description of the workflow."}, "payload": {"type": "object", "description": "Payload details for the workflow.", "properties": {"user_dependent_fields": {"type": "array", "description": "List of fields that are dependent on user input.", "items": {"type": "string"}}, "user_payload_template": {"type": "object", "description": "Template for the user payload with dynamic values."}}, "required": ["user_dependent_fields", "user_payload_template"]}}, "required": ["workflow_id", "description", "payload"]}, "mcp_tool": {"type": "object", "description": "MCP tool configuration details. Required if tool_type is 'mcp_sse'.", "properties": {"tool_name": {"type": "string", "description": "Name of the MCP tool (e.g., 'translate')."}, "server_params": {"type": "object", "description": "Server parameters for the MCP tool.", "properties": {"url": {"type": "string", "description": "The URL for the MCP server."}, "headers": {"type": "object", "description": "HTTP headers for the MCP request.", "additionalProperties": {"type": "string"}}, "timeout": {"type": "number", "description": "Timeout duration in seconds."}, "sse_read_timeout": {"type": "number", "description": "Timeout duration for reading SSE in seconds."}}, "required": ["url", "headers", "sse_read_timeout"]}}, "required": ["tool_name", "server_params"]}}, "allOf": [{"if": {"properties": {"tool_type": {"const": "workflow"}}}, "then": {"required": ["workflow"]}}, {"if": {"properties": {"tool_type": {"const": "mcp_sse"}}}, "then": {"required": ["mcp_tool"]}}], "required": ["tool_type"]}}, "delegate_tools": {"type": "array", "description": "Optional list of tool configurations used for delegating tasks to other agents.", "items": {"type": "object"}}, "agent_topic_type": {"type": "string", "description": "Defines the topic type for agent messaging (publishing/subscribing)."}, "user_topic_type": {"type": "string", "description": "Specifies the topic type for user-specific interactions."}, "subscriptions": {"type": "array", "description": "List of subscription configurations, each including a topic type.", "items": {"type": "object", "properties": {"topic_type": {"type": "string", "description": "The topic that the agent subscribes to."}}, "required": ["topic_type"]}}}, "required": ["user_id", "admin_id", "owner_type", "id", "agent_name", "agent_category", "description", "system_message", "model_client", "agent_tools", "agent_topic_type", "subscriptions"]}