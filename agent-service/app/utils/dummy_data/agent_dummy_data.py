"""
Dummy data for agent service.
This file contains dummy data for agent templates and agent configs.
"""

import uuid
from datetime import datetime, timezone
from app.utils.constants.constants import (
    AgentOwnerTypeEnum,
    AgentCategoryEnum,
    AgentVisibilityEnum,
    AgentStatusEnum,
    AgentToneEnum,
)

# Generate some consistent UUIDs for reference
USER_ID_1 = "user-" + str(uuid.uuid4())
USER_ID_2 = "user-" + str(uuid.uuid4())
ENTERPRISE_ID_1 = "enterprise-" + str(uuid.uuid4())
PLATFORM_ID = "platform-" + str(uuid.uuid4())

# Agent Template Dummy Data
AGENT_TEMPLATE_DUMMY_DATA = [
    {
        "id": "template-" + str(uuid.uuid4()),
        "name": "Customer Support Agent",
        "description": "An AI agent designed to handle customer support inquiries and provide assistance.",
        "avatar": "https://example.com/avatars/customer_support.png",
        "agent_category": AgentCategoryEnum.AI_AGENT,
        "system_message": "You are a helpful customer support agent. Your goal is to assist customers with their inquiries and resolve their issues efficiently.",
        "model_provider": "openai",
        "model_name": "gpt-4",
        "model_api_key": None,
        "workflow_ids": ["workflow-123", "workflow-456"],
        "mcp_server_ids": ["mcp-789"],
        "agent_topic_type": "customer_support",
        "department": "CUSTOMER_SUPPORT",
        "organization_id": ENTERPRISE_ID_1,
        "tone": AgentToneEnum.PROFESSIONAL,
        "files": ["file-123.pdf", "file-456.docx"],
        "urls": ["https://example.com/knowledge-base"],
        "owner_id": PLATFORM_ID,
        "use_count": 120,
        "average_rating": 4.7,
        "is_customizable": True,
        "tags": {
            "categories": ["support", "customer service"],
            "skills": ["problem solving", "communication"],
            "industries": ["retail", "e-commerce"]
        },
        "status": AgentStatusEnum.ACTIVE,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    },
    {
        "id": "template-" + str(uuid.uuid4()),
        "name": "Marketing Assistant",
        "description": "An AI agent that helps with marketing tasks and campaign planning.",
        "avatar": "https://example.com/avatars/marketing.png",
        "agent_category": AgentCategoryEnum.ASSISTANT,
        "system_message": "You are a marketing assistant. Your goal is to help users plan and execute effective marketing campaigns.",
        "model_provider": "anthropic",
        "model_name": "claude-3-opus",
        "model_api_key": None,
        "workflow_ids": ["workflow-789", "workflow-012"],
        "mcp_server_ids": ["mcp-345"],
        "agent_topic_type": "marketing",
        "department": "MARKETING",
        "organization_id": ENTERPRISE_ID_1,
        "tone": AgentToneEnum.ENTHUSIASTIC,
        "files": ["marketing-guide.pdf"],
        "urls": ["https://example.com/marketing-resources"],
        "owner_id": PLATFORM_ID,
        "use_count": 85,
        "average_rating": 4.5,
        "is_customizable": True,
        "tags": {
            "categories": ["marketing", "advertising"],
            "skills": ["campaign planning", "content creation"],
            "industries": ["all"]
        },
        "status": AgentStatusEnum.ACTIVE,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    },
    {
        "id": "template-" + str(uuid.uuid4()),
        "name": "Code Assistant",
        "description": "An AI agent that helps with programming and software development tasks.",
        "avatar": "https://example.com/avatars/code.png",
        "agent_category": AgentCategoryEnum.AI_AGENT,
        "system_message": "You are a coding assistant. Your goal is to help users with programming tasks, debugging, and software development.",
        "model_provider": "openai",
        "model_name": "gpt-4",
        "model_api_key": None,
        "workflow_ids": ["workflow-345"],
        "mcp_server_ids": ["mcp-678"],
        "agent_topic_type": "programming",
        "department": "ENGINEERING",
        "organization_id": ENTERPRISE_ID_1,
        "tone": AgentToneEnum.PROFESSIONAL,
        "files": ["coding-standards.pdf"],
        "urls": ["https://example.com/coding-resources"],
        "owner_id": PLATFORM_ID,
        "use_count": 210,
        "average_rating": 4.9,
        "is_customizable": True,
        "tags": {
            "categories": ["programming", "development"],
            "skills": ["debugging", "code review"],
            "languages": ["python", "javascript", "java"]
        },
        "status": AgentStatusEnum.ACTIVE,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    }
]

# Agent Config Dummy Data
AGENT_CONFIG_DUMMY_DATA = [
    {
        "id": "agent-" + str(uuid.uuid4()),
        "name": "My Customer Support Agent",
        "description": "Customized version of the customer support agent template.",
        "avatar": "https://example.com/avatars/custom_support.png",
        "owner_id": USER_ID_1,
        "user_ids": [USER_ID_1, USER_ID_2],
        "owner_type": AgentOwnerTypeEnum.USER,
        "template_id": AGENT_TEMPLATE_DUMMY_DATA[0]["id"],
        "template_owner_id": PLATFORM_ID,
        "is_imported": True,
        "agent_category": AgentCategoryEnum.AI_AGENT,
        "system_message": "You are a helpful customer support agent customized for my specific needs.",
        "model_provider": "openai",
        "model_name": "gpt-4",
        "model_api_key": None,
        "workflow_ids": ["workflow-123"],
        "mcp_server_ids": ["mcp-789"],
        "agent_topic_type": "customer_support",
        "department": "CUSTOMER_SUPPORT",
        "organization_id": ENTERPRISE_ID_1,
        "ruh_credentials": False,
        "tone": AgentToneEnum.FRIENDLY,
        "files": ["file-123.pdf"],
        "urls": ["https://example.com/my-knowledge-base"],
        "is_bench_employee": False,
        "is_changes_marketplace": False,
        "use_count": 15,
        "average_rating": 4.8,
        "visibility": AgentVisibilityEnum.PRIVATE,
        "tags": {
            "categories": ["support", "customer service"],
            "custom_tags": ["my-team"]
        },
        "status": AgentStatusEnum.ACTIVE,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    },
    {
        "id": "agent-" + str(uuid.uuid4()),
        "name": "Team Marketing Assistant",
        "description": "Marketing assistant configured for our team's needs.",
        "avatar": "https://example.com/avatars/team_marketing.png",
        "owner_id": USER_ID_2,
        "user_ids": [USER_ID_1, USER_ID_2],
        "owner_type": AgentOwnerTypeEnum.USER,
        "template_id": AGENT_TEMPLATE_DUMMY_DATA[1]["id"],
        "template_owner_id": PLATFORM_ID,
        "is_imported": True,
        "agent_category": AgentCategoryEnum.ASSISTANT,
        "system_message": "You are a marketing assistant customized for our team's specific campaigns.",
        "model_provider": "anthropic",
        "model_name": "claude-3-opus",
        "model_api_key": None,
        "workflow_ids": ["workflow-789"],
        "mcp_server_ids": ["mcp-345"],
        "agent_topic_type": "marketing",
        "department": "MARKETING",
        "organization_id": ENTERPRISE_ID_1,
        "ruh_credentials": False,
        "tone": AgentToneEnum.ENTHUSIASTIC,
        "files": ["team-marketing-guide.pdf"],
        "urls": ["https://example.com/team-resources"],
        "is_bench_employee": False,
        "is_changes_marketplace": False,
        "use_count": 42,
        "average_rating": 4.6,
        "visibility": AgentVisibilityEnum.PRIVATE,
        "tags": {
            "categories": ["marketing", "team"],
            "campaigns": ["summer-2023", "holiday-2023"]
        },
        "status": AgentStatusEnum.ACTIVE,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    }
]

# Agent Rating Dummy Data
AGENT_RATING_DUMMY_DATA = [
    {
        "id": "rating-" + str(uuid.uuid4()),
        "agent_id": AGENT_CONFIG_DUMMY_DATA[0]["id"],
        "user_id": USER_ID_2,
        "rating": 5.0,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    },
    {
        "id": "rating-" + str(uuid.uuid4()),
        "agent_id": AGENT_CONFIG_DUMMY_DATA[0]["id"],
        "user_id": USER_ID_1,
        "rating": 4.5,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    },
    {
        "id": "rating-" + str(uuid.uuid4()),
        "agent_id": AGENT_CONFIG_DUMMY_DATA[1]["id"],
        "user_id": USER_ID_1,
        "rating": 4.0,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    },
    {
        "id": "rating-" + str(uuid.uuid4()),
        "agent_id": AGENT_CONFIG_DUMMY_DATA[1]["id"],
        "user_id": USER_ID_2,
        "rating": 5.0,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    }
]
