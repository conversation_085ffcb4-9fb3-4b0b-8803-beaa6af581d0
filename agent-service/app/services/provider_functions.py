import grpc
import structlog
from datetime import datetime
from typing import Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, func
from app.db.session import get_db
from app.models.provider import Provider, Model
from app.grpc import provider_pb2

logger = structlog.get_logger()


class ProviderFunctionsService:
    """Service for managing provider CRUD operations."""

    def __init__(self):
        pass

    def _get_db_session(self) -> Session:
        """Get database session."""
        return next(get_db())

    def _provider_to_protobuf(self, provider: Provider, model_count: int = 0) -> provider_pb2.ProviderInfo:
        """Convert Provider model to protobuf ProviderInfo."""
        return provider_pb2.ProviderInfo(
            id=provider.id,
            provider=provider.provider,
            description=provider.description or "",
            baseUrl=provider.base_url,
            isActive=provider.is_active,
            isDefault=provider.is_default,
            createdAt=provider.created_at.isoformat(),
            updatedAt=provider.updated_at.isoformat(),
            modelCount=model_count
        )

    def _ensure_single_default(self, db: Session, exclude_id: Optional[str] = None):
        """Ensure only one provider is marked as default."""
        query = db.query(Provider).filter(Provider.is_default == True)
        if exclude_id:
            query = query.filter(Provider.id != exclude_id)
        
        for provider in query.all():
            provider.is_default = False
        db.commit()

    def createProvider(self, request: provider_pb2.CreateProviderRequest, context: grpc.ServicerContext) -> provider_pb2.ProviderResponse:
        """Create a new provider."""
        try:
            db = self._get_db_session()
            
            # Check if provider name already exists
            existing_provider = db.query(Provider).filter(Provider.provider == request.provider).first()
            if existing_provider:
                return provider_pb2.ProviderResponse(
                    success=False,
                    message=f"Provider with name '{request.provider}' already exists"
                )

            # If this provider should be default, ensure no other provider is default
            if request.isDefault:
                self._ensure_single_default(db)

            # Create new provider
            new_provider = Provider(
                provider=request.provider,
                description=request.description if request.HasField('description') else None,
                base_url=request.baseUrl,
                is_active=request.isActive if request.HasField('isActive') else True,
                is_default=request.isDefault if request.HasField('isDefault') else False
            )

            db.add(new_provider)
            db.commit()
            db.refresh(new_provider)

            logger.info(f"Provider created successfully: {new_provider.id}")
            return provider_pb2.ProviderResponse(
                success=True,
                message="Provider created successfully",
                provider=self._provider_to_protobuf(new_provider)
            )

        except Exception as e:
            logger.error(f"Error creating provider: {str(e)}")
            db.rollback()
            return provider_pb2.ProviderResponse(
                success=False,
                message=f"Failed to create provider: {str(e)}"
            )
        finally:
            db.close()

    def getProvider(self, request: provider_pb2.GetByIdRequest, context: grpc.ServicerContext) -> provider_pb2.ProviderResponse:
        """Get provider by ID."""
        try:
            db = self._get_db_session()
            
            provider = db.query(Provider).filter(Provider.id == request.id).first()
            if not provider:
                return provider_pb2.ProviderResponse(
                    success=False,
                    message=f"Provider with ID '{request.id}' not found"
                )

            # Get model count for this provider
            model_count = db.query(func.count(Model.id)).filter(Model.provider_id == provider.id).scalar()

            return provider_pb2.ProviderResponse(
                success=True,
                message="Provider retrieved successfully",
                provider=self._provider_to_protobuf(provider, model_count)
            )

        except Exception as e:
            logger.error(f"Error getting provider: {str(e)}")
            return provider_pb2.ProviderResponse(
                success=False,
                message=f"Failed to get provider: {str(e)}"
            )
        finally:
            db.close()

    def updateProvider(self, request: provider_pb2.UpdateProviderRequest, context: grpc.ServicerContext) -> provider_pb2.ProviderResponse:
        """Update an existing provider."""
        try:
            db = self._get_db_session()
            
            provider = db.query(Provider).filter(Provider.id == request.id).first()
            if not provider:
                return provider_pb2.ProviderResponse(
                    success=False,
                    message=f"Provider with ID '{request.id}' not found"
                )

            # Check if provider name already exists (excluding current provider)
            if request.HasField('provider'):
                existing_provider = db.query(Provider).filter(
                    and_(Provider.provider == request.provider, Provider.id != request.id)
                ).first()
                if existing_provider:
                    return provider_pb2.ProviderResponse(
                        success=False,
                        message=f"Provider with name '{request.provider}' already exists"
                    )

            # If this provider should be default, ensure no other provider is default
            if request.HasField('isDefault') and request.isDefault:
                self._ensure_single_default(db, exclude_id=request.id)

            # Update fields
            if request.HasField('provider'):
                provider.provider = request.provider
            if request.HasField('description'):
                provider.description = request.description
            if request.HasField('baseUrl'):
                provider.base_url = request.baseUrl
            if request.HasField('isActive'):
                provider.is_active = request.isActive
            if request.HasField('isDefault'):
                provider.is_default = request.isDefault

            provider.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(provider)

            # Get model count for this provider
            model_count = db.query(func.count(Model.id)).filter(Model.provider_id == provider.id).scalar()

            logger.info(f"Provider updated successfully: {provider.id}")
            return provider_pb2.ProviderResponse(
                success=True,
                message="Provider updated successfully",
                provider=self._provider_to_protobuf(provider, model_count)
            )

        except Exception as e:
            logger.error(f"Error updating provider: {str(e)}")
            db.rollback()
            return provider_pb2.ProviderResponse(
                success=False,
                message=f"Failed to update provider: {str(e)}"
            )
        finally:
            db.close()

    def deleteProvider(self, request: provider_pb2.DeleteRequest, context: grpc.ServicerContext) -> provider_pb2.DeleteResponse:
        """Delete a provider."""
        try:
            db = self._get_db_session()
            
            provider = db.query(Provider).filter(Provider.id == request.id).first()
            if not provider:
                return provider_pb2.DeleteResponse(
                    success=False,
                    message=f"Provider with ID '{request.id}' not found"
                )

            # Check if provider has associated models
            model_count = db.query(func.count(Model.id)).filter(Model.provider_id == provider.id).scalar()
            if model_count > 0:
                return provider_pb2.DeleteResponse(
                    success=False,
                    message=f"Cannot delete provider. It has {model_count} associated models. Delete models first."
                )

            db.delete(provider)
            db.commit()

            logger.info(f"Provider deleted successfully: {request.id}")
            return provider_pb2.DeleteResponse(
                success=True,
                message="Provider deleted successfully"
            )

        except Exception as e:
            logger.error(f"Error deleting provider: {str(e)}")
            db.rollback()
            return provider_pb2.DeleteResponse(
                success=False,
                message=f"Failed to delete provider: {str(e)}"
            )
        finally:
            db.close()

    def listProviders(self, request: provider_pb2.ListRequest, context: grpc.ServicerContext) -> provider_pb2.ListProvidersResponse:
        """List providers with pagination."""
        try:
            db = self._get_db_session()
            
            # Build query
            query = db.query(Provider)
            
            # Filter by active status if specified
            if request.HasField('isActive'):
                query = query.filter(Provider.is_active == request.isActive)
            
            # Get total count
            total_count = query.count()
            
            # Apply pagination
            page = max(1, request.page) if request.page > 0 else 1
            page_size = min(100, max(1, request.pageSize)) if request.pageSize > 0 else 10
            offset = (page - 1) * page_size
            
            providers = query.offset(offset).limit(page_size).all()
            
            # Get model counts for each provider
            provider_infos = []
            for provider in providers:
                model_count = db.query(func.count(Model.id)).filter(Model.provider_id == provider.id).scalar()
                provider_infos.append(self._provider_to_protobuf(provider, model_count))
            
            # Calculate pagination info
            total_pages = (total_count + page_size - 1) // page_size
            
            pagination = provider_pb2.PaginationInfo(
                currentPage=page,
                totalPages=total_pages,
                totalItems=total_count,
                pageSize=page_size
            )

            return provider_pb2.ListProvidersResponse(
                success=True,
                message="Providers retrieved successfully",
                providers=provider_infos,
                pagination=pagination
            )

        except Exception as e:
            logger.error(f"Error listing providers: {str(e)}")
            return provider_pb2.ListProvidersResponse(
                success=False,
                message=f"Failed to list providers: {str(e)}",
                providers=[],
                pagination=provider_pb2.PaginationInfo()
            )
        finally:
            db.close()