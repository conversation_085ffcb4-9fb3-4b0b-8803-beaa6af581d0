import grpc
import structlog
from app.grpc import provider_pb2, provider_pb2_grpc
from app.services.provider_functions import ProviderFunctionsService
from app.services.model_functions import ModelFunctionsService

logger = structlog.get_logger()


class ProviderService(provider_pb2_grpc.ProviderServiceServicer):
    """
    gRPC service for managing AI Model Providers.
    
    This service handles CRUD operations for providers including
    validation, database operations, and business logic.
    """

    def __init__(self):
        self.provider_functions = ProviderFunctionsService()

    def CreateProvider(
        self, request: provider_pb2.CreateProviderRequest, context: grpc.ServicerContext
    ) -> provider_pb2.ProviderResponse:
        try:
            logger.info("create_provider_request")
            return self.provider_functions.createProvider(request, context)
        except Exception as e:
            logger.error(f"create provider failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def GetProvider(
        self, request: provider_pb2.GetByIdRequest, context: grpc.ServicerContext
    ) -> provider_pb2.ProviderResponse:
        try:
            logger.info("get_provider_request")
            return self.provider_functions.getProvider(request, context)
        except Exception as e:
            logger.error(f"get provider failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def UpdateProvider(
        self, request: provider_pb2.UpdateProviderRequest, context: grpc.ServicerContext
    ) -> provider_pb2.ProviderResponse:
        try:
            logger.info("update_provider_request")
            return self.provider_functions.updateProvider(request, context)
        except Exception as e:
            logger.error(f"update provider failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def DeleteProvider(
        self, request: provider_pb2.DeleteRequest, context: grpc.ServicerContext
    ) -> provider_pb2.DeleteResponse:
        try:
            logger.info("delete_provider_request")
            return self.provider_functions.deleteProvider(request, context)
        except Exception as e:
            logger.error(f"delete provider failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def ListProviders(
        self, request: provider_pb2.ListRequest, context: grpc.ServicerContext
    ) -> provider_pb2.ListProvidersResponse:
        try:
            logger.info("list_providers_request")
            return self.provider_functions.listProviders(request, context)
        except Exception as e:
            logger.error(f"list providers failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")


class ModelService(provider_pb2_grpc.ModelServiceServicer):
    """
    gRPC service for managing AI Models.
    
    This service handles CRUD operations for models including
    validation, database operations, and business logic.
    """

    def __init__(self):
        self.model_functions = ModelFunctionsService()

    def CreateModel(
        self, request: provider_pb2.CreateModelRequest, context: grpc.ServicerContext
    ) -> provider_pb2.ModelResponse:
        try:
            logger.info("create_model_request")
            return self.model_functions.createModel(request, context)
        except Exception as e:
            logger.error(f"create model failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def GetModel(
        self, request: provider_pb2.GetByIdRequest, context: grpc.ServicerContext
    ) -> provider_pb2.ModelResponse:
        try:
            logger.info("get_model_request")
            return self.model_functions.getModel(request, context)
        except Exception as e:
            logger.error(f"get model failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def UpdateModel(
        self, request: provider_pb2.UpdateModelRequest, context: grpc.ServicerContext
    ) -> provider_pb2.ModelResponse:
        try:
            logger.info("update_model_request")
            return self.model_functions.updateModel(request, context)
        except Exception as e:
            logger.error(f"update model failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def DeleteModel(
        self, request: provider_pb2.DeleteRequest, context: grpc.ServicerContext
    ) -> provider_pb2.DeleteResponse:
        try:
            logger.info("delete_model_request")
            return self.model_functions.deleteModel(request, context)
        except Exception as e:
            logger.error(f"delete model failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def ListModels(
        self, request: provider_pb2.ListRequest, context: grpc.ServicerContext
    ) -> provider_pb2.ListModelsResponse:
        try:
            logger.info("list_models_request")
            return self.model_functions.listModels(request, context)
        except Exception as e:
            logger.error(f"list models failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def ListModelsByProvider(
        self, request: provider_pb2.GetByProviderRequest, context: grpc.ServicerContext
    ) -> provider_pb2.ListModelsResponse:
        try:
            logger.info("list_models_by_provider_request")
            return self.model_functions.listModelsByProvider(request, context)
        except Exception as e:
            logger.error(f"list models by provider failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")