"""
Script to load dummy provider and model data for development/testing.
"""
import asyncio
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ...db.session import get_db
from app.models.provider import Provider, Model
from sqlalchemy.orm import Session
from datetime import datetime
import uuid


def create_dummy_data(db: Session):
    """Create dummy providers and models for testing."""
    
    # Create providers
    providers_data = [
        {
            "name": "OpenAI",
            "description": "OpenAI API provider for GPT models",
            "is_default": True
        },
        {
            "name": "Anthropic",
            "description": "Anthropic Claude API provider",
            "is_default": False
        },
        {
            "name": "Google",
            "description": "Google AI/Gemini API provider",
            "is_default": False
        },
        {
            "name": "Cohere",
            "description": "Cohere API provider for language models",
            "is_default": False
        }
    ]
    
    created_providers = []
    
    for provider_data in providers_data:
        # Check if provider already exists
        existing_provider = db.query(Provider).filter(Provider.name == provider_data["name"]).first()
        if existing_provider:
            print(f"Provider '{provider_data['name']}' already exists, skipping...")
            created_providers.append(existing_provider)
            continue
            
        provider = Provider(
            id=str(uuid.uuid4()),
            name=provider_data["name"],
            description=provider_data["description"],
            is_default=provider_data["is_default"],
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(provider)
        created_providers.append(provider)
        print(f"Created provider: {provider.name}")
    
    # Commit providers first
    db.commit()
    
    # Create models for each provider
    models_data = [
        # OpenAI models
        {
            "provider_name": "OpenAI",
            "models": [
                {"name": "GPT-4", "description": "OpenAI GPT-4 model", "model_type": "text", "is_default": True},
                {"name": "GPT-4-turbo", "description": "OpenAI GPT-4 Turbo model", "model_type": "text", "is_default": False},
                {"name": "GPT-3.5-turbo", "description": "OpenAI GPT-3.5 Turbo model", "model_type": "text", "is_default": False},
                {"name": "DALL-E-3", "description": "OpenAI DALL-E 3 image generation model", "model_type": "image", "is_default": False},
            ]
        },
        # Anthropic models
        {
            "provider_name": "Anthropic",
            "models": [
                {"name": "Claude-3-Opus", "description": "Anthropic Claude 3 Opus model", "model_type": "text", "is_default": True},
                {"name": "Claude-3-Sonnet", "description": "Anthropic Claude 3 Sonnet model", "model_type": "text", "is_default": False},
                {"name": "Claude-3-Haiku", "description": "Anthropic Claude 3 Haiku model", "model_type": "text", "is_default": False},
            ]
        },
        # Google models
        {
            "provider_name": "Google",
            "models": [
                {"name": "Gemini-Pro", "description": "Google Gemini Pro model", "model_type": "text", "is_default": True},
                {"name": "Gemini-Pro-Vision", "description": "Google Gemini Pro Vision model", "model_type": "multimodal", "is_default": False},
                {"name": "PaLM-2", "description": "Google PaLM 2 model", "model_type": "text", "is_default": False},
            ]
        },
        # Cohere models
        {
            "provider_name": "Cohere",
            "models": [
                {"name": "Command", "description": "Cohere Command model", "model_type": "text", "is_default": True},
                {"name": "Command-Light", "description": "Cohere Command Light model", "model_type": "text", "is_default": False},
                {"name": "Embed-English", "description": "Cohere English embedding model", "model_type": "embedding", "is_default": False},
            ]
        }
    ]
    
    for provider_models in models_data:
        provider = db.query(Provider).filter(Provider.name == provider_models["provider_name"]).first()
        if not provider:
            print(f"Provider '{provider_models['provider_name']}' not found, skipping models...")
            continue
            
        for model_data in provider_models["models"]:
            # Check if model already exists
            existing_model = db.query(Model).filter(
                Model.provider_id == provider.id,
                Model.name == model_data["name"]
            ).first()
            if existing_model:
                print(f"Model '{model_data['name']}' already exists for provider '{provider.name}', skipping...")
                continue
                
            model = Model(
                id=str(uuid.uuid4()),
                provider_id=provider.id,
                name=model_data["name"],
                description=model_data["description"],
                model_type=model_data["model_type"],
                is_default=model_data["is_default"],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            db.add(model)
            print(f"Created model: {model.name} for provider: {provider.name}")
    
    # Commit all changes
    db.commit()
    print("Dummy data creation completed!")


def main():
    """Main function to run the data loading script."""
    print("Loading dummy provider and model data...")
    
    # Get database session
    db = next(get_db())
    
    try:
        create_dummy_data(db)
    except Exception as e:
        print(f"Error creating dummy data: {str(e)}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    main()