import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, DateTime, Float
from sqlalchemy.orm import declarative_base


from app.utils.constants.table_names import AGENT_RATING_TABLE

Base = declarative_base()


class AgentRating(Base):
    __tablename__ = AGENT_RATING_TABLE

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Agent ID (can be from either test-agent-templates or test-agents)
    # Removed the foreign key constraint to allow ratings for both templates and configs
    agent_id = Column(String, nullable=False)

    # User who provided the rating
    user_id = Column(String, nullable=False)

    # Rating value (1.0 to 5.0)
    rating = Column(Float, nullable=False)

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

    def __repr__(self):
        return (
            f"<AgentRating(id={self.id}, agent_id='{self.agent_id}', "
            f"user_id='{self.user_id}', rating={self.rating})>"
        )
