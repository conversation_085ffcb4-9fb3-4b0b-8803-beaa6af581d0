"""
Simple test for the is_updated field functionality in agent-service.

This test verifies the core is_updated field behavior:
1. New agents have is_updated=False by default
2. Direct database updates can set is_updated=True
3. The field exists and works as expected
"""

import sys
import os
from datetime import datetime
import traceback

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

try:
    from app.models.agent import AgentConfig, AgentConfigVersion
    from app.db.session import SessionLocal
    from app.utils.constants.constants import AgentStatusEnum, AgentVisibilityEnum, AgentOwnerTypeEnum, AgentCategoryEnum
    print("✅ All imports successful")
except Exception as e:
    print(f"❌ Import failed: {str(e)}")
    traceback.print_exc()
    sys.exit(1)


def test_agent_model_has_is_updated_field():
    """Test that AgentConfig model has is_updated field with correct default"""
    
    print("=== Test: AgentConfig model has is_updated field ===")
    
    db = SessionLocal()
    
    try:
        # Create a minimal agent
        test_agent = AgentConfig(
            id="test-model-agent-123",
            name="Test Model Agent",
            description="Test agent for model validation",
            avatar="https://example.com/avatar.png",
            owner_id="test-user-123",
            owner_type=AgentOwnerTypeEnum.USER,
            visibility=AgentVisibilityEnum.PRIVATE,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Test system message"
        )
        
        # Check that is_updated field exists
        assert hasattr(test_agent, 'is_updated'), "AgentConfig model doesn't have is_updated field"
        
        # The default value might be None until saved to database, so let's check after save
        print(f"is_updated value before save: {test_agent.is_updated}")
        
        print("✅ AgentConfig model has is_updated field")
        
        # Save to database
        db.add(test_agent)
        db.commit()
        
        # Retrieve from database and verify default value
        saved_agent = db.query(AgentConfig).filter(AgentConfig.id == test_agent.id).first()
        assert saved_agent is not None, "Agent not found in database"
        print(f"is_updated value after save: {saved_agent.is_updated}")
        
        # The database should apply the default value of False
        assert saved_agent.is_updated == False, f"Expected is_updated=False from database, got {saved_agent.is_updated}"
        
        print("✅ is_updated field has correct default value False in database")
        
        # Test setting is_updated to True
        saved_agent.is_updated = True
        db.commit()
        
        # Verify the change
        updated_agent = db.query(AgentConfig).filter(AgentConfig.id == test_agent.id).first()
        assert updated_agent.is_updated == True, f"Expected is_updated=True after update, got {updated_agent.is_updated}"
        
        print("✅ is_updated field can be set to True")
        
        # Test setting back to False
        updated_agent.is_updated = False
        db.commit()
        
        # Verify the change
        reset_agent = db.query(AgentConfig).filter(AgentConfig.id == test_agent.id).first()
        assert reset_agent.is_updated == False, f"Expected is_updated=False after reset, got {reset_agent.is_updated}"
        
        print("✅ is_updated field can be reset to False")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        raise
        
    finally:
        # Cleanup
        try:
            db.query(AgentConfig).filter(AgentConfig.id == "test-model-agent-123").delete()
            db.commit()
            print("✅ Cleanup completed")
        except Exception as cleanup_error:
            print(f"⚠️ Cleanup warning: {cleanup_error}")
            db.rollback()
        finally:
            db.close()


def test_agent_version_model_structure():
    """Test that AgentConfigVersion model has the expected structure"""
    
    print("=== Test: AgentConfigVersion model structure ===")
    
    db = SessionLocal()
    
    try:
        # Create a minimal agent first
        test_agent = AgentConfig(
            id="test-version-model-agent-123",
            name="Test Version Model Agent",
            description="Test agent for version model validation",
            avatar="https://example.com/avatar.png",
            owner_id="test-user-123",
            owner_type=AgentOwnerTypeEnum.USER,
            visibility=AgentVisibilityEnum.PRIVATE,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Test system message",
            is_updated=False
        )
        
        db.add(test_agent)
        db.flush()
        
        # Create a version
        test_version = AgentConfigVersion(
            agent_config_id=test_agent.id,
            version_number="1.0.0",
            name=test_agent.name,
            description=test_agent.description,
            avatar=test_agent.avatar,
            agent_category=test_agent.agent_category,
            system_message=test_agent.system_message,
            version_notes="Test version"
        )
        
        db.add(test_version)
        db.commit()
        
        # Verify the version was created
        saved_version = db.query(AgentConfigVersion).filter(AgentConfigVersion.agent_config_id == test_agent.id).first()
        assert saved_version is not None, "Version not found in database"
        assert saved_version.version_number == "1.0.0", f"Expected version_number=1.0.0, got {saved_version.version_number}"
        
        print("✅ AgentConfigVersion model works correctly")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        raise
        
    finally:
        # Cleanup
        try:
            db.query(AgentConfigVersion).filter(AgentConfigVersion.agent_config_id == "test-version-model-agent-123").delete()
            db.query(AgentConfig).filter(AgentConfig.id == "test-version-model-agent-123").delete()
            db.commit()
            print("✅ Cleanup completed")
        except Exception as cleanup_error:
            print(f"⚠️ Cleanup warning: {cleanup_error}")
            db.rollback()
        finally:
            db.close()


def test_is_updated_field_lifecycle():
    """Test the complete lifecycle of is_updated field"""
    
    print("=== Test: is_updated field lifecycle ===")
    
    db = SessionLocal()
    
    try:
        # 1. Create agent (default value will be applied by database)
        agent = AgentConfig(
            id="test-lifecycle-agent-123",
            name="Test Lifecycle Agent",
            description="Test agent for lifecycle validation",
            avatar="https://example.com/avatar.png",
            owner_id="test-user-123",
            owner_type=AgentOwnerTypeEnum.USER,
            visibility=AgentVisibilityEnum.PRIVATE,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Test system message"
        )
        
        # 2. Save to database (this will apply the default value)
        db.add(agent)
        db.commit()
        
        # 3. Verify default value after save
        saved_agent = db.query(AgentConfig).filter(AgentConfig.id == agent.id).first()
        assert saved_agent.is_updated == False, "New agent should have is_updated=False after save"
        print("✅ Step 1: New agent has is_updated=False after save")
        
        # 4. Simulate an update (like what service methods would do)
        saved_agent.name = "Updated Lifecycle Agent"
        saved_agent.is_updated = True
        db.commit()
        
        # 5. Verify the update
        updated_agent = db.query(AgentConfig).filter(AgentConfig.id == agent.id).first()
        assert updated_agent.is_updated == True, "Agent should have is_updated=True after update"
        print("✅ Step 2: Agent has is_updated=True after update")
        
        # 6. Simulate version creation (like what version service would do)
        version = AgentConfigVersion(
            agent_config_id=agent.id,
            version_number="1.0.0",
            name=updated_agent.name,
            description=updated_agent.description,
            avatar=updated_agent.avatar,
            agent_category=updated_agent.agent_category,
            system_message=updated_agent.system_message,
            version_notes="Version created after update"
        )
        
        db.add(version)
        db.flush()
        
        # 7. Reset is_updated to False (like version service would do)
        updated_agent.is_updated = False
        updated_agent.current_version_id = version.id
        db.commit()
        
        # 8. Verify the reset
        final_agent = db.query(AgentConfig).filter(AgentConfig.id == agent.id).first()
        assert final_agent.is_updated == False, "Agent should have is_updated=False after version creation"
        print("✅ Step 3: Agent has is_updated=False after version creation")
        
        print("✅ Complete lifecycle test passed")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        raise
        
    finally:
        # Cleanup
        try:
            # Set current_version_id to NULL first
            agent = db.query(AgentConfig).filter(AgentConfig.id == "test-lifecycle-agent-123").first()
            if agent:
                agent.current_version_id = None
                db.commit()
            
            # Delete versions and agent
            db.query(AgentConfigVersion).filter(AgentConfigVersion.agent_config_id == "test-lifecycle-agent-123").delete()
            db.query(AgentConfig).filter(AgentConfig.id == "test-lifecycle-agent-123").delete()
            db.commit()
            print("✅ Cleanup completed")
        except Exception as cleanup_error:
            print(f"⚠️ Cleanup warning: {cleanup_error}")
            db.rollback()
        finally:
            db.close()


def run_all_tests():
    """Run all simple is_updated field tests"""
    
    print("🧪 Running simple is_updated field tests for agent-service...")
    
    tests = [
        test_agent_model_has_is_updated_field,
        test_agent_version_model_structure,
        test_is_updated_field_lifecycle
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
            print(f"✅ {test.__name__} PASSED\n")
        except Exception as e:
            failed += 1
            print(f"❌ {test.__name__} FAILED with exception: {str(e)}\n")
    
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED!")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)