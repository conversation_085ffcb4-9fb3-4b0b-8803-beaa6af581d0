"""
Test suite for Provider and Model services.
"""
import pytest
import uuid
from unittest.mock import Mock, patch
from datetime import datetime

from app.services.provider_functions import (
    create_provider,
    get_provider_by_id,
    get_all_providers,
    update_provider,
    delete_provider
)
from app.services.model_functions import (
    create_model,
    get_model_by_id,
    get_all_models,
    update_model,
    delete_model
)
from app.models.provider import Provider, Model


class TestProviderFunctions:
    """Test cases for provider functions."""

    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock()

    @pytest.fixture
    def sample_provider_data(self):
        """Sample provider data for testing."""
        return {
            "name": "Test Provider",
            "description": "Test provider description",
            "is_default": False
        }

    @pytest.fixture
    def sample_provider(self):
        """Sample provider instance."""
        return Provider(
            id=str(uuid.uuid4()),
            name="Test Provider",
            description="Test provider description",
            is_default=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

    def test_create_provider_success(self, mock_db, sample_provider_data):
        """Test successful provider creation."""
        # Mock database operations
        mock_db.query.return_value.filter.return_value.first.return_value = None
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()

        # Create provider
        result = create_provider(mock_db, **sample_provider_data)

        # Assertions
        assert result["success"] is True
        assert result["message"] == "Provider created successfully"
        assert result["provider"] is not None
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()

    def test_create_provider_duplicate_name(self, mock_db, sample_provider_data, sample_provider):
        """Test provider creation with duplicate name."""
        # Mock existing provider
        mock_db.query.return_value.filter.return_value.first.return_value = sample_provider

        # Create provider
        result = create_provider(mock_db, **sample_provider_data)

        # Assertions
        assert result["success"] is False
        assert "already exists" in result["message"]
        assert result["provider"] is None

    def test_create_provider_default_constraint(self, mock_db, sample_provider_data, sample_provider):
        """Test provider creation with default constraint."""
        # Set as default
        sample_provider_data["is_default"] = True
        
        # Mock no existing provider with same name
        mock_db.query.return_value.filter.return_value.first.return_value = None
        # Mock existing default provider
        mock_db.query.return_value.filter.return_value.all.return_value = [sample_provider]
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()

        # Create provider
        result = create_provider(mock_db, **sample_provider_data)

        # Assertions
        assert result["success"] is True
        # Should have updated existing default provider
        assert sample_provider.is_default is False

    def test_get_provider_by_id_success(self, mock_db, sample_provider):
        """Test successful provider retrieval by ID."""
        # Mock database query
        mock_db.query.return_value.filter.return_value.first.return_value = sample_provider

        # Get provider
        result = get_provider_by_id(mock_db, sample_provider.id)

        # Assertions
        assert result["success"] is True
        assert result["provider"] == sample_provider

    def test_get_provider_by_id_not_found(self, mock_db):
        """Test provider retrieval with non-existent ID."""
        # Mock database query
        mock_db.query.return_value.filter.return_value.first.return_value = None

        # Get provider
        result = get_provider_by_id(mock_db, "non-existent-id")

        # Assertions
        assert result["success"] is False
        assert "not found" in result["message"]

    def test_get_all_providers_success(self, mock_db, sample_provider):
        """Test successful providers listing."""
        # Mock database query
        mock_query = Mock()
        mock_query.offset.return_value.limit.return_value.all.return_value = [sample_provider]
        mock_query.count.return_value = 1
        mock_db.query.return_value = mock_query

        # Get providers
        result = get_all_providers(mock_db, page=1, page_size=10)

        # Assertions
        assert result["success"] is True
        assert len(result["providers"]) == 1
        assert result["pagination"]["total_items"] == 1

    def test_update_provider_success(self, mock_db, sample_provider):
        """Test successful provider update."""
        # Mock database query
        mock_db.query.return_value.filter.return_value.first.return_value = sample_provider
        mock_db.commit = Mock()
        mock_db.refresh = Mock()

        # Update provider
        update_data = {"name": "Updated Provider", "description": "Updated description"}
        result = update_provider(mock_db, sample_provider.id, **update_data)

        # Assertions
        assert result["success"] is True
        assert sample_provider.name == "Updated Provider"
        assert sample_provider.description == "Updated description"

    def test_delete_provider_success(self, mock_db, sample_provider):
        """Test successful provider deletion."""
        # Mock database query
        mock_db.query.return_value.filter.return_value.first.return_value = sample_provider
        mock_db.delete = Mock()
        mock_db.commit = Mock()

        # Delete provider
        result = delete_provider(mock_db, sample_provider.id)

        # Assertions
        assert result["success"] is True
        mock_db.delete.assert_called_once_with(sample_provider)
        mock_db.commit.assert_called_once()


class TestModelFunctions:
    """Test cases for model functions."""

    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock()

    @pytest.fixture
    def sample_provider(self):
        """Sample provider instance."""
        return Provider(
            id=str(uuid.uuid4()),
            name="Test Provider",
            description="Test provider description",
            is_default=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

    @pytest.fixture
    def sample_model_data(self, sample_provider):
        """Sample model data for testing."""
        return {
            "provider_id": sample_provider.id,
            "name": "Test Model",
            "description": "Test model description",
            "model_type": "text",
            "is_default": False
        }

    @pytest.fixture
    def sample_model(self, sample_provider):
        """Sample model instance."""
        return Model(
            id=str(uuid.uuid4()),
            provider_id=sample_provider.id,
            name="Test Model",
            description="Test model description",
            model_type="text",
            is_default=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

    def test_create_model_success(self, mock_db, sample_model_data, sample_provider):
        """Test successful model creation."""
        # Mock database operations
        mock_db.query.return_value.filter.return_value.first.return_value = sample_provider
        mock_db.query.return_value.filter.return_value.filter.return_value.first.return_value = None
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()

        # Create model
        result = create_model(mock_db, **sample_model_data)

        # Assertions
        assert result["success"] is True
        assert result["message"] == "Model created successfully"
        assert result["model"] is not None
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()

    def test_create_model_provider_not_found(self, mock_db, sample_model_data):
        """Test model creation with non-existent provider."""
        # Mock provider not found
        mock_db.query.return_value.filter.return_value.first.return_value = None

        # Create model
        result = create_model(mock_db, **sample_model_data)

        # Assertions
        assert result["success"] is False
        assert "Provider not found" in result["message"]

    def test_create_model_duplicate_name(self, mock_db, sample_model_data, sample_provider, sample_model):
        """Test model creation with duplicate name within provider."""
        # Mock provider exists
        mock_db.query.return_value.filter.return_value.first.return_value = sample_provider
        # Mock existing model with same name
        mock_db.query.return_value.filter.return_value.filter.return_value.first.return_value = sample_model

        # Create model
        result = create_model(mock_db, **sample_model_data)

        # Assertions
        assert result["success"] is False
        assert "already exists" in result["message"]

    def test_get_model_by_id_success(self, mock_db, sample_model):
        """Test successful model retrieval by ID."""
        # Mock database query with relationship loading
        mock_db.query.return_value.options.return_value.filter.return_value.first.return_value = sample_model

        # Get model
        result = get_model_by_id(mock_db, sample_model.id)

        # Assertions
        assert result["success"] is True
        assert result["model"] == sample_model

    def test_get_all_models_success(self, mock_db, sample_model):
        """Test successful models listing."""
        # Mock database query
        mock_query = Mock()
        mock_query.options.return_value.offset.return_value.limit.return_value.all.return_value = [sample_model]
        mock_query.count.return_value = 1
        mock_db.query.return_value = mock_query

        # Get models
        result = get_all_models(mock_db, page=1, page_size=10)

        # Assertions
        assert result["success"] is True
        assert len(result["models"]) == 1
        assert result["pagination"]["total_items"] == 1

    def test_update_model_success(self, mock_db, sample_model):
        """Test successful model update."""
        # Mock database query
        mock_db.query.return_value.filter.return_value.first.return_value = sample_model
        mock_db.commit = Mock()
        mock_db.refresh = Mock()

        # Update model
        update_data = {"name": "Updated Model", "description": "Updated description"}
        result = update_model(mock_db, sample_model.id, **update_data)

        # Assertions
        assert result["success"] is True
        assert sample_model.name == "Updated Model"
        assert sample_model.description == "Updated description"

    def test_delete_model_success(self, mock_db, sample_model):
        """Test successful model deletion."""
        # Mock database query
        mock_db.query.return_value.filter.return_value.first.return_value = sample_model
        mock_db.delete = Mock()
        mock_db.commit = Mock()

        # Delete model
        result = delete_model(mock_db, sample_model.id)

        # Assertions
        assert result["success"] is True
        mock_db.delete.assert_called_once_with(sample_model)
        mock_db.commit.assert_called_once()


class TestProviderModelIntegration:
    """Integration tests for provider and model functionality."""

    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock()

    def test_cascade_delete_models_when_provider_deleted(self, mock_db):
        """Test that models are deleted when provider is deleted (cascade)."""
        # This would be tested with actual database in integration tests
        # Here we just verify the relationship is set up correctly
        provider = Provider(
            id=str(uuid.uuid4()),
            name="Test Provider",
            description="Test description",
            is_default=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        model = Model(
            id=str(uuid.uuid4()),
            provider_id=provider.id,
            name="Test Model",
            description="Test model",
            model_type="text",
            is_default=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # Verify relationship
        assert model.provider_id == provider.id

    def test_default_provider_constraint(self, mock_db):
        """Test that only one provider can be default."""
        # This test would verify the business logic constraint
        # that only one provider can be marked as default
        pass

    def test_default_model_per_provider_constraint(self, mock_db):
        """Test that only one model per provider can be default."""
        # This test would verify the business logic constraint
        # that only one model per provider can be marked as default
        pass