import pytest
from sqlalchemy.orm import Session
from app.models.agent import <PERSON><PERSON>onfig, AgentModelConfig, AgentKnowledgeBase, AgentConfigVersion
from app.services.agent_functions import AgentFunctionsService
from app.grpc import agent_pb2
from app.db.session import SessionLocal
from app.utils.helpers.agent_to_protobuf import _agent_to_protobuf


class TestAgentRefactoring:
    """Test suite for agent refactoring to separate model config and knowledge base"""
    
    def setup_method(self):
        """Setup test database session"""
        self.db = SessionLocal()
        self.agent_service = AgentFunctionsService()
    
    def teardown_method(self):
        """Cleanup test database session"""
        self.db.close()
    
    def test_create_agent_stores_model_config_separately(self):
        """Test that createAgent stores model configuration in separate table"""
        # Create test request
        owner = agent_pb2.Owner(
            id="test-user-123",
            email="<EMAIL>",
            full_name="Test User"
        )
        
        request = agent_pb2.CreateAgentRequest(
            name="Test Agent",
            description="Test Description",
            avatar="test-avatar.png",
            owner=owner,
            owner_type=agent_pb2.OwnerType.USER,
            system_message="You are a helpful assistant",
            model_provider="openai",
            model_name="gpt-4",
            model_api_key="test-api-key",
            files=["file1.pdf", "file2.txt"],
            urls=["https://example.com", "https://test.com"],
            visibility=agent_pb2.Visibility.PRIVATE,
            status=agent_pb2.Status.ACTIVE,
            tone=agent_pb2.Tone.PROFESSIONAL,
            category=agent_pb2.Category.GENERAL
        )
        
        # Mock context
        class MockContext:
            def set_code(self, code): pass
            def set_details(self, details): pass
        
        context = MockContext()
        
        # Create agent
        response = self.agent_service.createAgent(request, context)
        
        # Verify response
        assert response.success == True
        assert response.agent.name == "Test Agent"
        
        # Verify agent config does NOT store model fields directly
        agent = self.db.query(AgentConfig).filter(AgentConfig.id == response.agent.id).first()
        assert agent is not None
        
        # These fields should be None/empty in AgentConfig after refactoring
        # Currently they exist, but after refactoring they should be removed
        
        # Verify model config is stored separately
        version = self.db.query(AgentConfigVersion).filter(
            AgentConfigVersion.agent_config_id == agent.id
        ).first()
        assert version is not None
        assert version.model_config_id is not None
        
        model_config = self.db.query(AgentModelConfig).filter(
            AgentModelConfig.id == version.model_config_id
        ).first()
        assert model_config is not None
        assert model_config.model_provider == "openai"
        assert model_config.model_name == "gpt-4"
        assert model_config.model_api_key == "test-api-key"
        
        # Verify knowledge base is stored separately
        assert version.knowledge_base_id is not None
        knowledge_base = self.db.query(AgentKnowledgeBase).filter(
            AgentKnowledgeBase.id == version.knowledge_base_id
        ).first()
        assert knowledge_base is not None
        assert knowledge_base.files == ["file1.pdf", "file2.txt"]
        assert knowledge_base.urls == ["https://example.com", "https://test.com"]
    
    def test_agent_to_protobuf_joins_data_from_multiple_tables(self):
        """Test that _agent_to_protobuf retrieves data from joined tables"""
        # Create test data directly in database
        agent = AgentConfig(
            name="Test Agent",
            description="Test Description", 
            avatar="test-avatar.png",
            owner_id="test-user-123",
            owner_type="user",
            system_message="You are a helpful assistant",
            # After refactoring, these should be None
            model_provider=None,
            model_name=None,
            model_api_key=None,
            files=None,
            urls=None,
            visibility="private",
            status="active"
        )
        self.db.add(agent)
        self.db.flush()
        
        # Create model config
        model_config = AgentModelConfig(
            model_provider="openai",
            model_name="gpt-4", 
            model_api_key="test-api-key"
        )
        self.db.add(model_config)
        self.db.flush()
        
        # Create knowledge base
        knowledge_base = AgentKnowledgeBase(
            files=["file1.pdf", "file2.txt"],
            urls=["https://example.com", "https://test.com"]
        )
        self.db.add(knowledge_base)
        self.db.flush()
        
        # Create version linking to model config and knowledge base
        version = AgentConfigVersion(
            agent_config_id=agent.id,
            model_config_id=model_config.id,
            knowledge_base_id=knowledge_base.id,
            version_number="1.0.0",
            name=agent.name,
            description=agent.description,
            avatar=agent.avatar,
            agent_category=agent.agent_category,
            system_message=agent.system_message,
            workflow_ids=[],
            mcp_server_ids=[],
            agent_topic_type=agent.agent_topic_type,
            department=agent.department,
            organization_id=agent.organization_id,
            ruh_credentials=agent.ruh_credentials,
            tone=agent.tone,
            is_bench_employee=agent.is_bench_employee,
            is_changes_marketplace=agent.is_changes_marketplace,
            is_a2a=agent.is_a2a,
            is_customizable=agent.is_customizable,
            capabilities_id=agent.capabilities_id,
            example_prompts=[],
            category=agent.category,
            tags=[],
            status=agent.status,
            version_notes="Initial version"
        )
        self.db.add(version)
        self.db.flush()
        
        # Set current version
        agent.current_version_id = version.id
        self.db.add(agent)
        self.db.commit()
        
        # Convert to protobuf
        agent_proto = _agent_to_protobuf(db=self.db, agent=agent)
        
        # Verify protobuf contains data from joined tables
        assert agent_proto.model_provider == "openai"
        assert agent_proto.model_name == "gpt-4"
        assert agent_proto.model_api_key == "test-api-key"
        assert list(agent_proto.files) == ["file1.pdf", "file2.txt"]
        assert list(agent_proto.urls) == ["https://example.com", "https://test.com"]