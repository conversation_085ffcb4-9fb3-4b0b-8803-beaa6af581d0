apiVersion: v1
kind: ServiceAccount
metadata:
  name: agent-service-ai-sa
  namespace: ruh-<ENV>
  labels:
    name: agent-service-ai-sa
    namespace: ruh-<ENV>
    app: agent-service-ai
    deployment: agent-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-service-ai-dp
  namespace: ruh-<ENV>
  labels:
    name: agent-service-ai-dp
    namespace: ruh-<ENV>
    app: agent-service-ai
    serviceaccount: agent-service-ai-sa
    deployment: agent-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: agent-service-ai
      deployment: agent-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-<ENV>
        app: agent-service-ai
        deployment: agent-service-ai-dp
    spec:
      serviceAccountName: agent-service-ai-sa      
      containers:
      - name: agent-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:ruh-<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50052
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: agent-service-ai-svc
  namespace: ruh-<ENV>
spec:
  selector:
    app: agent-service-ai
    deployment: agent-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50052
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:agent-service-user-hpa
#   namespace: ruh-<ENV>
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:agent-service-user-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress






