[tool.poetry]
name = "agent-service"
version = "0.1.0"
description = "Agent Builder Microservice"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.11"
sqlalchemy = "^2.0.25"
alembic = "^1.13.1"
psycopg2-binary = "^2.9.9"
grpcio = "^1.60.0"
grpcio-tools = "^1.60.0"
python-jose = {extras = ["cryptography"], version = "^3.4.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
pydantic = "^2.5.3"
pydantic-settings = "^2.1.0"
python-dotenv = "^1.0.0"
structlog = "^24.1.0"
google-auth = "^2.27.0"
google-auth-oauthlib = "^1.2.0"
redis = "^5.0.1"
bcrypt = "4.0.1"
jsonschema = "^4.23.0"
google-cloud-storage = "^3.1.0"
kafka-python = "^2.0.6"
confluent-kafka = "^2.8.2"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.4"
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.23.3"
black = "^23.12.1"
isort = "^5.13.2"
mypy = "^1.8.0"
pylint = "^3.0.3"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ["py311"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.pylint.messages_control]
disable = ["C0111", "C0103", "C0330", "C0326"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=app --cov-report=term-missing"
asyncio_mode = "auto" 