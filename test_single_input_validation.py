#!/usr/bin/env python3
"""
Simple validation script to test the single input approach in ConditionalNode.
This script validates the changes without requiring external dependencies.
"""

import os
import sys
import re


def validate_conditional_node_single_input():
    """Validate that ConditionalNode has been updated to use single input approach."""
    
    print("🔍 Validating ConditionalNode single input implementation...")
    
    # Read the ConditionalNode file
    file_path = "workflow-service/app/components/control_flow/conditionalNode.py"
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Test 1: Check for single input_data input
    if 'name="input_data"' in content:
        print("✅ Found single 'input_data' input")
    else:
        print("❌ 'input_data' input not found")
        return False
    
    # Test 2: Check that old 'primary' input is removed
    if 'name="primary"' in content:
        print("❌ Old 'primary' input still exists - should be removed")
        return False
    else:
        print("✅ Old 'primary' input successfully removed")
    
    # Test 3: Check for single input documentation
    if "SINGLE INPUT DESIGN" in content:
        print("✅ Found single input design documentation")
    else:
        print("❌ Single input design documentation missing")
        return False
    
    # Test 4: Check that execute method uses input_data
    if 'self.get_input_value("input_data"' in content:
        print("✅ Execute method uses single input_data")
    else:
        print("❌ Execute method doesn't use input_data")
        return False
    
    # Test 5: Check that no separate condition data inputs are created
    condition_data_pattern = re.compile(r'create_dual_purpose_input\(\s*name=f"condition_\{i\}"')
    if condition_data_pattern.search(content):
        print("❌ Found separate condition data inputs - should be removed")
        return False
    else:
        print("✅ No separate condition data inputs found")
    
    # Test 6: Check that all conditions evaluate the same input_data
    if 'actual_data": input_data' in content:
        print("✅ All conditions evaluate the same input_data")
    else:
        print("❌ Conditions don't use single input_data")
        return False
    
    # Test 7: Check for proper output routing with input_data
    if 'outputs[output_name] = input_data' in content:
        print("✅ Output routing uses single input_data")
    else:
        print("❌ Output routing doesn't use single input_data")
        return False
    
    print("\n🎉 ConditionalNode single input validation completed successfully!")
    print("   ✓ Single input_data for all conditions")
    print("   ✓ No separate condition data inputs") 
    print("   ✓ All conditions evaluate same data source")
    print("   ✓ Simplified architecture achieved")
    return True


def main():
    """Main validation function."""
    print("=" * 60)
    print("SINGLE INPUT VALIDATION REPORT")
    print("=" * 60)
    
    # Change to backend directory
    os.chdir("/Users/<USER>/Desktop/ruh_ai/backend")
    
    success = validate_conditional_node_single_input()
    
    if success:
        print(f"\n✅ ALL VALIDATIONS PASSED")
        print("The ConditionalNode has been successfully updated to use a single input approach.")
        print("All conditions now evaluate against the same input data source.")
        sys.exit(0)
    else:
        print(f"\n❌ VALIDATION FAILED")
        print("Some issues were found in the single input implementation.")
        sys.exit(1)


if __name__ == "__main__":
    main()