2025-07-15 18:16:48 - NodeExecutor - INFO - [main:155] All components can be accessed using the tool_name parameter in requests
2025-07-15 18:16:48 - NodeExecutor - INFO - [main] All components can be accessed using the tool_name parameter in requests
2025-07-15 18:16:48 - NodeExecutor - INFO - [main:156] Available tools: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'IDGeneratorComponent', 'UniversalConverterComponent', 'MergeDataComponent', 'GmailTrackerComponent']
2025-07-15 18:16:48 - NodeExecutor - INFO - [main] Available tools: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'IDGeneratorComponent', 'UniversalConverterComponent', 'MergeDataComponent', 'GmailTrackerComponent']
2025-07-15 18:16:48 - NodeExecutor - INFO - [main:158] Node Executor is now running. Press Ctrl+C to stop.
2025-07-15 18:16:48 - NodeExecutor - INFO - [main] Node Executor is now running. Press Ctrl+C to stop.
2025-07-15 18:16:48 - ComponentSystem - INFO - [_consume_messages:501] Consumer loop started for component: ApiRequestNode
2025-07-15 18:16:48 - ComponentSystem - INFO - [_consume_messages] Consumer loop started for component: ApiRequestNode
2025-07-15 18:17:57 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1254, TaskID=ApiRequestNode-node-execution-request-0-1254-1752583677.865792
2025-07-15 18:17:57 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1254, TaskID=ApiRequestNode-node-execution-request-0-1254-1752583677.865792
2025-07-15 18:17:57 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1254-1752583677.865792, Payload={
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "operator": "equals",
        "next_transition": "transition-MergeDataComponent-1752580571006",
        "ends_at": [
          "transition-MergeDataComponent-1752580571006"
        ],
        "expected_value": "hello",
        "variable_name": "marketing"
      }
    ],
    "input_data": {},
    "source": "global_context",
    "default_transition": "transition-CombineTextComponent-*************",
    "default_ends_at": [
      "transition-CombineTextComponent-*************"
    ],
    "evaluation_strategy": "all_matches",
    "global_context": {
      "input_data": "hello"
    },
    "node_output": {}
  },
  "request_id": "********-ca1a-4116-9d50-e247eb4d9af6",
  "correlation_id": "c68a1876-236a-4aff-906a-0fa873ecdae9",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 18:17:57 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1254-1752583677.865792, Payload={
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "operator": "equals",
        "next_transition": "transition-MergeDataComponent-1752580571006",
        "ends_at": [
          "transition-MergeDataComponent-1752580571006"
        ],
        "expected_value": "hello",
        "variable_name": "marketing"
      }
    ],
    "input_data": {},
    "source": "global_context",
    "default_transition": "transition-CombineTextComponent-*************",
    "default_ends_at": [
      "transition-CombineTextComponent-*************"
    ],
    "evaluation_strategy": "all_matches",
    "global_context": {
      "input_data": "hello"
    },
    "node_output": {}
  },
  "request_id": "********-ca1a-4116-9d50-e247eb4d9af6",
  "correlation_id": "c68a1876-236a-4aff-906a-0fa873ecdae9",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 18:17:57 - ComponentSystem - INFO - [_process_message:713] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Executing tool conditional for RequestID=********-ca1a-4116-9d50-e247eb4d9af6, TaskID=ApiRequestNode-node-execution-request-0-1254-1752583677.865792
2025-07-15 18:17:57 - ComponentSystem - INFO - [_process_message] Executing tool conditional for RequestID=********-ca1a-4116-9d50-e247eb4d9af6, TaskID=ApiRequestNode-node-execution-request-0-1254-1752583677.865792
2025-07-15 18:17:57 - ToolExecutor - INFO - [execute_tool:94] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Executing tool for request_id: ********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: ********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - ToolExecutor - INFO - [execute_tool:97] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] ToolExecutor received payload: {
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "operator": "equals",
        "next_transition": "transition-MergeDataComponent-1752580571006",
        "ends_at": [
          "transition-MergeDataComponent-1752580571006"
        ],
        "expected_value": "hello",
        "variable_name": "marketing"
      }
    ],
    "input_data": {},
    "source": "global_context",
    "default_transition": "transition-CombineTextComponent-*************",
    "default_ends_at": [
      "transition-CombineTextComponent-*************"
    ],
    "evaluation_strategy": "all_matches",
    "global_context": {
      "input_data": "hello"
    },
    "node_output": {}
  },
  "request_id": "********-ca1a-4116-9d50-e247eb4d9af6",
  "correlation_id": "c68a1876-236a-4aff-906a-0fa873ecdae9",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 18:17:57 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "operator": "equals",
        "next_transition": "transition-MergeDataComponent-1752580571006",
        "ends_at": [
          "transition-MergeDataComponent-1752580571006"
        ],
        "expected_value": "hello",
        "variable_name": "marketing"
      }
    ],
    "input_data": {},
    "source": "global_context",
    "default_transition": "transition-CombineTextComponent-*************",
    "default_ends_at": [
      "transition-CombineTextComponent-*************"
    ],
    "evaluation_strategy": "all_matches",
    "global_context": {
      "input_data": "hello"
    },
    "node_output": {}
  },
  "request_id": "********-ca1a-4116-9d50-e247eb4d9af6",
  "correlation_id": "c68a1876-236a-4aff-906a-0fa873ecdae9",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 18:17:57 - ToolExecutor - INFO - [execute_tool:111] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Tool name: conditional for request_id: ********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - ToolExecutor - INFO - [execute_tool] Tool name: conditional for request_id: ********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - app.components.conditional_component - INFO - [__init__] ConditionalComponent initialized successfully
2025-07-15 18:17:57 - ToolExecutor - INFO - [execute_tool:144] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Processing payload with component conditional for request_id: ********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - ToolExecutor - INFO - [execute_tool] Processing payload with component conditional for request_id: ********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - app.components.conditional_component - INFO - [process] Processing conditional routing for request_id: ********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - app.components.conditional_component - INFO - [process] Direct mode: evaluating conditions against input_data directly
2025-07-15 18:17:57 - app.components.conditional_component - INFO - [_evaluate_all_matches] No conditions matched, using default: transition-CombineTextComponent-*************
2025-07-15 18:17:57 - ToolExecutor - INFO - [execute_tool:148] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Component conditional processed payload successfully for request_id: ********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - ToolExecutor - INFO - [execute_tool] Component conditional processed payload successfully for request_id: ********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - ToolExecutor - INFO - [execute_tool:154] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] ToolExecutor returning raw component result for request_id: ********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: ********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - ComponentSystem - INFO - [_process_message:717] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Tool conditional executed successfully for RequestID=********-ca1a-4116-9d50-e247eb4d9af6, TaskID=ApiRequestNode-node-execution-request-0-1254-1752583677.865792
2025-07-15 18:17:57 - ComponentSystem - INFO - [_process_message] Tool conditional executed successfully for RequestID=********-ca1a-4116-9d50-e247eb4d9af6, TaskID=ApiRequestNode-node-execution-request-0-1254-1752583677.865792
2025-07-15 18:17:57 - ComponentSystem - INFO - [_send_result:1007] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Preparing to send result for component ApiRequestNode, RequestID=********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:17:57 - ComponentSystem - INFO - [get_producer:244] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Creating Kafka producer for component ApiRequestNode with configuration:
2025-07-15 18:17:57 - ComponentSystem - INFO - [get_producer] Creating Kafka producer for component ApiRequestNode with configuration:
2025-07-15 18:17:57 - ComponentSystem - INFO - [get_producer:247] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9]   Bootstrap Servers: 34.172.106.233:9092
2025-07-15 18:17:57 - ComponentSystem - INFO - [get_producer]   Bootstrap Servers: 34.172.106.233:9092
2025-07-15 18:17:57 - ComponentSystem - INFO - [get_producer:248] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9]   Acks: all (ensuring message is written to all in-sync replicas)
2025-07-15 18:17:57 - ComponentSystem - INFO - [get_producer]   Acks: all (ensuring message is written to all in-sync replicas)
2025-07-15 18:17:57 - ComponentSystem - INFO - [get_producer:252] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9]   Request Timeout: 60000ms
2025-07-15 18:17:57 - ComponentSystem - INFO - [get_producer]   Request Timeout: 60000ms
2025-07-15 18:17:57 - ComponentSystem - INFO - [get_producer:255] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-07-15 18:17:57 - ComponentSystem - INFO - [get_producer]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-07-15 18:17:57 - ComponentSystem - INFO - [get_producer:259] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Creating new Kafka producer for component: ApiRequestNode with servers: 34.172.106.233:9092
2025-07-15 18:17:57 - ComponentSystem - INFO - [get_producer] Creating new Kafka producer for component: ApiRequestNode with servers: 34.172.106.233:9092
2025-07-15 18:17:59 - ComponentSystem - INFO - [get_producer:266] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Kafka producer started successfully for component: ApiRequestNode
2025-07-15 18:17:59 - ComponentSystem - INFO - [get_producer] Kafka producer started successfully for component: ApiRequestNode
2025-07-15 18:17:59 - ComponentSystem - INFO - [_send_result:1105] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Sending Kafka response: RequestID=********-ca1a-4116-9d50-e247eb4d9af6, Response={
  "request_id": "********-ca1a-4116-9d50-e247eb4d9af6",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.7803462,
  "transition_id": "transition-ConditionalNode-*************",
  "result": {
    "routing_decision": {
      "target_transitions": [
        "transition-CombineTextComponent-*************"
      ],
      "matched_conditions": [],
      "condition_result": false,
      "execution_time_ms": 8.**************
    },
    "metadata": {
      "total_conditions": 1,
      "total_matches": 0,
      "evaluation_strategy": "all_matches",
      "evaluation_mode": "direct",
      "condition_variable_name": null
    },
    "input_data": {}
  },
  "error": null
}
2025-07-15 18:17:59 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=********-ca1a-4116-9d50-e247eb4d9af6, Response={
  "request_id": "********-ca1a-4116-9d50-e247eb4d9af6",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.7803462,
  "transition_id": "transition-ConditionalNode-*************",
  "result": {
    "routing_decision": {
      "target_transitions": [
        "transition-CombineTextComponent-*************"
      ],
      "matched_conditions": [],
      "condition_result": false,
      "execution_time_ms": 8.**************
    },
    "metadata": {
      "total_conditions": 1,
      "total_matches": 0,
      "evaluation_strategy": "all_matches",
      "evaluation_mode": "direct",
      "condition_variable_name": null
    },
    "input_data": {}
  },
  "error": null
}
2025-07-15 18:18:00 - ComponentSystem - INFO - [_send_result:1121] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Sent result for component ApiRequestNode to topic node_results for RequestID=********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:18:00 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=********-ca1a-4116-9d50-e247eb4d9af6
2025-07-15 18:18:00 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Successfully committed offset 1255 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1254-1752583677.865792
2025-07-15 18:18:00 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1255 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1254-1752583677.865792
2025-07-15 18:18:00 - ComponentSystem - INFO - [_process_message:936] [ReqID:********-ca1a-4116-9d50-e247eb4d9af6] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1254, TaskID=ApiRequestNode-node-execution-request-0-1254-1752583677.865792
2025-07-15 18:18:00 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1254, TaskID=ApiRequestNode-node-execution-request-0-1254-1752583677.865792
2025-07-15 18:18:04 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1255, TaskID=ApiRequestNode-node-execution-request-0-1255-1752583684.735691
2025-07-15 18:18:04 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1255, TaskID=ApiRequestNode-node-execution-request-0-1255-1752583684.735691
2025-07-15 18:18:04 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1255-1752583684.735691, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "1",
    "input_1": "ma"
  },
  "request_id": "64573c2b-9128-4434-9187-6d3179fb1e3f",
  "correlation_id": "c68a1876-236a-4aff-906a-0fa873ecdae9",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 18:18:04 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1255-1752583684.735691, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "1",
    "input_1": "ma"
  },
  "request_id": "64573c2b-9128-4434-9187-6d3179fb1e3f",
  "correlation_id": "c68a1876-236a-4aff-906a-0fa873ecdae9",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 18:18:04 - ComponentSystem - INFO - [_process_message:713] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Executing tool CombineTextComponent for RequestID=64573c2b-9128-4434-9187-6d3179fb1e3f, TaskID=ApiRequestNode-node-execution-request-0-1255-1752583684.735691
2025-07-15 18:18:04 - ComponentSystem - INFO - [_process_message] Executing tool CombineTextComponent for RequestID=64573c2b-9128-4434-9187-6d3179fb1e3f, TaskID=ApiRequestNode-node-execution-request-0-1255-1752583684.735691
2025-07-15 18:18:04 - ToolExecutor - INFO - [execute_tool:94] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Executing tool for request_id: 64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: 64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - ToolExecutor - INFO - [execute_tool:97] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "1",
    "input_1": "ma"
  },
  "request_id": "64573c2b-9128-4434-9187-6d3179fb1e3f",
  "correlation_id": "c68a1876-236a-4aff-906a-0fa873ecdae9",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 18:18:04 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "1",
    "input_1": "ma"
  },
  "request_id": "64573c2b-9128-4434-9187-6d3179fb1e3f",
  "correlation_id": "c68a1876-236a-4aff-906a-0fa873ecdae9",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 18:18:04 - ToolExecutor - INFO - [execute_tool:111] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Tool name: CombineTextComponent for request_id: 64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - ToolExecutor - INFO - [execute_tool] Tool name: CombineTextComponent for request_id: 64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - app.components.combine_text_component_new - INFO - [__init__] Initializing Combine Text Component
2025-07-15 18:18:04 - app.components.combine_text_component_new - INFO - [__init__] Combine Text Component initialized successfully
2025-07-15 18:18:04 - ToolExecutor - INFO - [execute_tool:144] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Processing payload with component CombineTextComponent for request_id: 64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - ToolExecutor - INFO - [execute_tool] Processing payload with component CombineTextComponent for request_id: 64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - app.components.combine_text_component_new - INFO - [process] Processing text combination request for request_id: 64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - app.components.combine_text_component_new - INFO - [process] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'input_1', 'request_id']
2025-07-15 18:18:04 - app.components.combine_text_component_new - INFO - [process] FULL PAYLOAD: {'main_input': {}, 'num_additional_inputs': '1', 'input_1': 'ma', 'request_id': '64573c2b-9128-4434-9187-6d3179fb1e3f'}
2025-07-15 18:18:04 - app.components.combine_text_component_new - INFO - [process] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'input_1', 'request_id']
2025-07-15 18:18:04 - app.components.combine_text_component_new - INFO - [process] Combining text for request_id 64573c2b-9128-4434-9187-6d3179fb1e3f. Separator: '', Num additional inputs: 1
2025-07-15 18:18:04 - app.components.combine_text_component_new - INFO - [process] Text combined successfully for request_id 64573c2b-9128-4434-9187-6d3179fb1e3f. Result length: 4
2025-07-15 18:18:04 - ToolExecutor - INFO - [execute_tool:148] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Component CombineTextComponent processed payload successfully for request_id: 64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - ToolExecutor - INFO - [execute_tool] Component CombineTextComponent processed payload successfully for request_id: 64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - ToolExecutor - INFO - [execute_tool:154] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] ToolExecutor returning raw component result for request_id: 64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: 64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - ComponentSystem - INFO - [_process_message:717] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Tool CombineTextComponent executed successfully for RequestID=64573c2b-9128-4434-9187-6d3179fb1e3f, TaskID=ApiRequestNode-node-execution-request-0-1255-1752583684.735691
2025-07-15 18:18:04 - ComponentSystem - INFO - [_process_message] Tool CombineTextComponent executed successfully for RequestID=64573c2b-9128-4434-9187-6d3179fb1e3f, TaskID=ApiRequestNode-node-execution-request-0-1255-1752583684.735691
2025-07-15 18:18:04 - ComponentSystem - INFO - [_send_result:1007] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Preparing to send result for component ApiRequestNode, RequestID=64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - ComponentSystem - INFO - [_send_result:1105] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Sending Kafka response: RequestID=64573c2b-9128-4434-9187-6d3179fb1e3f, Response={
  "request_id": "64573c2b-9128-4434-9187-6d3179fb1e3f",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1752583684.737825,
  "transition_id": "transition-CombineTextComponent-*************",
  "result": "{}ma",
  "error": null
}
2025-07-15 18:18:04 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=64573c2b-9128-4434-9187-6d3179fb1e3f, Response={
  "request_id": "64573c2b-9128-4434-9187-6d3179fb1e3f",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1752583684.737825,
  "transition_id": "transition-CombineTextComponent-*************",
  "result": "{}ma",
  "error": null
}
2025-07-15 18:18:04 - ComponentSystem - INFO - [_send_result:1121] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Sent result for component ApiRequestNode to topic node_results for RequestID=64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:04 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=64573c2b-9128-4434-9187-6d3179fb1e3f
2025-07-15 18:18:05 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Successfully committed offset 1256 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1255-1752583684.735691
2025-07-15 18:18:05 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1256 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1255-1752583684.735691
2025-07-15 18:18:05 - ComponentSystem - INFO - [_process_message:936] [ReqID:64573c2b-9128-4434-9187-6d3179fb1e3f] [CorrID:c68a1876-236a-4aff-906a-0fa873ecdae9] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1255, TaskID=ApiRequestNode-node-execution-request-0-1255-1752583684.735691
2025-07-15 18:18:05 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1255, TaskID=ApiRequestNode-node-execution-request-0-1255-1752583684.735691
2025-07-15 18:20:06 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1256, TaskID=ApiRequestNode-node-execution-request-0-1256-**********.822478
2025-07-15 18:20:06 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1256, TaskID=ApiRequestNode-node-execution-request-0-1256-**********.822478
2025-07-15 18:20:06 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1256-**********.822478, Payload={
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "operator": "equals",
        "next_transition": "transition-MergeDataComponent-1752580571006",
        "ends_at": [
          "transition-MergeDataComponent-1752580571006"
        ],
        "expected_value": "hello"
      }
    ],
    "input_data": {},
    "source": "node_output",
    "default_transition": "transition-CombineTextComponent-*************",
    "default_ends_at": [
      "transition-CombineTextComponent-*************"
    ],
    "evaluation_strategy": "all_matches",
    "global_context": {
      "input_data": "hello"
    },
    "node_output": {}
  },
  "request_id": "fad04f12-34e9-4be1-81b7-b947b73af266",
  "correlation_id": "346d41ec-8085-44b6-83d9-9078e22cc7bd",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 18:20:06 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1256-**********.822478, Payload={
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "operator": "equals",
        "next_transition": "transition-MergeDataComponent-1752580571006",
        "ends_at": [
          "transition-MergeDataComponent-1752580571006"
        ],
        "expected_value": "hello"
      }
    ],
    "input_data": {},
    "source": "node_output",
    "default_transition": "transition-CombineTextComponent-*************",
    "default_ends_at": [
      "transition-CombineTextComponent-*************"
    ],
    "evaluation_strategy": "all_matches",
    "global_context": {
      "input_data": "hello"
    },
    "node_output": {}
  },
  "request_id": "fad04f12-34e9-4be1-81b7-b947b73af266",
  "correlation_id": "346d41ec-8085-44b6-83d9-9078e22cc7bd",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 18:20:06 - ComponentSystem - INFO - [_process_message:713] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Executing tool conditional for RequestID=fad04f12-34e9-4be1-81b7-b947b73af266, TaskID=ApiRequestNode-node-execution-request-0-1256-**********.822478
2025-07-15 18:20:06 - ComponentSystem - INFO - [_process_message] Executing tool conditional for RequestID=fad04f12-34e9-4be1-81b7-b947b73af266, TaskID=ApiRequestNode-node-execution-request-0-1256-**********.822478
2025-07-15 18:20:06 - ToolExecutor - INFO - [execute_tool:94] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Executing tool for request_id: fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - ToolExecutor - INFO - [execute_tool:97] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] ToolExecutor received payload: {
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "operator": "equals",
        "next_transition": "transition-MergeDataComponent-1752580571006",
        "ends_at": [
          "transition-MergeDataComponent-1752580571006"
        ],
        "expected_value": "hello"
      }
    ],
    "input_data": {},
    "source": "node_output",
    "default_transition": "transition-CombineTextComponent-*************",
    "default_ends_at": [
      "transition-CombineTextComponent-*************"
    ],
    "evaluation_strategy": "all_matches",
    "global_context": {
      "input_data": "hello"
    },
    "node_output": {}
  },
  "request_id": "fad04f12-34e9-4be1-81b7-b947b73af266",
  "correlation_id": "346d41ec-8085-44b6-83d9-9078e22cc7bd",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 18:20:06 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "operator": "equals",
        "next_transition": "transition-MergeDataComponent-1752580571006",
        "ends_at": [
          "transition-MergeDataComponent-1752580571006"
        ],
        "expected_value": "hello"
      }
    ],
    "input_data": {},
    "source": "node_output",
    "default_transition": "transition-CombineTextComponent-*************",
    "default_ends_at": [
      "transition-CombineTextComponent-*************"
    ],
    "evaluation_strategy": "all_matches",
    "global_context": {
      "input_data": "hello"
    },
    "node_output": {}
  },
  "request_id": "fad04f12-34e9-4be1-81b7-b947b73af266",
  "correlation_id": "346d41ec-8085-44b6-83d9-9078e22cc7bd",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 18:20:06 - ToolExecutor - INFO - [execute_tool:111] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Tool name: conditional for request_id: fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - ToolExecutor - INFO - [execute_tool] Tool name: conditional for request_id: fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - ToolExecutor - INFO - [execute_tool:144] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Processing payload with component conditional for request_id: fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - ToolExecutor - INFO - [execute_tool] Processing payload with component conditional for request_id: fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - app.components.conditional_component - INFO - [process] Processing conditional routing for request_id: fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - app.components.conditional_component - INFO - [process] Direct mode: evaluating conditions against input_data directly
2025-07-15 18:20:06 - app.components.conditional_component - INFO - [_evaluate_all_matches] No conditions matched, using default: transition-CombineTextComponent-*************
2025-07-15 18:20:06 - ToolExecutor - INFO - [execute_tool:148] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Component conditional processed payload successfully for request_id: fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - ToolExecutor - INFO - [execute_tool] Component conditional processed payload successfully for request_id: fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - ToolExecutor - INFO - [execute_tool:154] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] ToolExecutor returning raw component result for request_id: fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - ComponentSystem - INFO - [_process_message:717] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Tool conditional executed successfully for RequestID=fad04f12-34e9-4be1-81b7-b947b73af266, TaskID=ApiRequestNode-node-execution-request-0-1256-**********.822478
2025-07-15 18:20:06 - ComponentSystem - INFO - [_process_message] Tool conditional executed successfully for RequestID=fad04f12-34e9-4be1-81b7-b947b73af266, TaskID=ApiRequestNode-node-execution-request-0-1256-**********.822478
2025-07-15 18:20:06 - ComponentSystem - INFO - [_send_result:1007] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Preparing to send result for component ApiRequestNode, RequestID=fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:06 - ComponentSystem - INFO - [_send_result:1105] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Sending Kafka response: RequestID=fad04f12-34e9-4be1-81b7-b947b73af266, Response={
  "request_id": "fad04f12-34e9-4be1-81b7-b947b73af266",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.835628,
  "transition_id": "transition-ConditionalNode-*************",
  "result": {
    "routing_decision": {
      "target_transitions": [
        "transition-CombineTextComponent-*************"
      ],
      "matched_conditions": [],
      "condition_result": false,
      "execution_time_ms": 3.***************
    },
    "metadata": {
      "total_conditions": 1,
      "total_matches": 0,
      "evaluation_strategy": "all_matches",
      "evaluation_mode": "direct",
      "condition_variable_name": null
    },
    "input_data": {}
  },
  "error": null
}
2025-07-15 18:20:06 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=fad04f12-34e9-4be1-81b7-b947b73af266, Response={
  "request_id": "fad04f12-34e9-4be1-81b7-b947b73af266",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.835628,
  "transition_id": "transition-ConditionalNode-*************",
  "result": {
    "routing_decision": {
      "target_transitions": [
        "transition-CombineTextComponent-*************"
      ],
      "matched_conditions": [],
      "condition_result": false,
      "execution_time_ms": 3.***************
    },
    "metadata": {
      "total_conditions": 1,
      "total_matches": 0,
      "evaluation_strategy": "all_matches",
      "evaluation_mode": "direct",
      "condition_variable_name": null
    },
    "input_data": {}
  },
  "error": null
}
2025-07-15 18:20:07 - ComponentSystem - INFO - [_send_result:1121] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Sent result for component ApiRequestNode to topic node_results for RequestID=fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:07 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=fad04f12-34e9-4be1-81b7-b947b73af266
2025-07-15 18:20:07 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Successfully committed offset 1257 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1256-**********.822478
2025-07-15 18:20:07 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1257 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1256-**********.822478
2025-07-15 18:20:07 - ComponentSystem - INFO - [_process_message:936] [ReqID:fad04f12-34e9-4be1-81b7-b947b73af266] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1256, TaskID=ApiRequestNode-node-execution-request-0-1256-**********.822478
2025-07-15 18:20:07 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1256, TaskID=ApiRequestNode-node-execution-request-0-1256-**********.822478
2025-07-15 18:20:09 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1257, TaskID=ApiRequestNode-node-execution-request-0-1257-1752583809.868053
2025-07-15 18:20:09 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1257, TaskID=ApiRequestNode-node-execution-request-0-1257-1752583809.868053
2025-07-15 18:20:09 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1257-1752583809.868053, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "1",
    "input_1": "ma"
  },
  "request_id": "5c0451e2-439f-4a69-81d2-a014cbec3c54",
  "correlation_id": "346d41ec-8085-44b6-83d9-9078e22cc7bd",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 18:20:09 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1257-1752583809.868053, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "1",
    "input_1": "ma"
  },
  "request_id": "5c0451e2-439f-4a69-81d2-a014cbec3c54",
  "correlation_id": "346d41ec-8085-44b6-83d9-9078e22cc7bd",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 18:20:09 - ComponentSystem - INFO - [_process_message:713] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Executing tool CombineTextComponent for RequestID=5c0451e2-439f-4a69-81d2-a014cbec3c54, TaskID=ApiRequestNode-node-execution-request-0-1257-1752583809.868053
2025-07-15 18:20:09 - ComponentSystem - INFO - [_process_message] Executing tool CombineTextComponent for RequestID=5c0451e2-439f-4a69-81d2-a014cbec3c54, TaskID=ApiRequestNode-node-execution-request-0-1257-1752583809.868053
2025-07-15 18:20:09 - ToolExecutor - INFO - [execute_tool:94] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Executing tool for request_id: 5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: 5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - ToolExecutor - INFO - [execute_tool:97] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "1",
    "input_1": "ma"
  },
  "request_id": "5c0451e2-439f-4a69-81d2-a014cbec3c54",
  "correlation_id": "346d41ec-8085-44b6-83d9-9078e22cc7bd",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 18:20:09 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": {},
    "num_additional_inputs": "1",
    "input_1": "ma"
  },
  "request_id": "5c0451e2-439f-4a69-81d2-a014cbec3c54",
  "correlation_id": "346d41ec-8085-44b6-83d9-9078e22cc7bd",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 18:20:09 - ToolExecutor - INFO - [execute_tool:111] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Tool name: CombineTextComponent for request_id: 5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - ToolExecutor - INFO - [execute_tool] Tool name: CombineTextComponent for request_id: 5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - ToolExecutor - INFO - [execute_tool:144] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Processing payload with component CombineTextComponent for request_id: 5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - ToolExecutor - INFO - [execute_tool] Processing payload with component CombineTextComponent for request_id: 5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - app.components.combine_text_component_new - INFO - [process] Processing text combination request for request_id: 5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - app.components.combine_text_component_new - INFO - [process] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'input_1', 'request_id']
2025-07-15 18:20:09 - app.components.combine_text_component_new - INFO - [process] FULL PAYLOAD: {'main_input': {}, 'num_additional_inputs': '1', 'input_1': 'ma', 'request_id': '5c0451e2-439f-4a69-81d2-a014cbec3c54'}
2025-07-15 18:20:09 - app.components.combine_text_component_new - INFO - [process] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'input_1', 'request_id']
2025-07-15 18:20:09 - app.components.combine_text_component_new - INFO - [process] Combining text for request_id 5c0451e2-439f-4a69-81d2-a014cbec3c54. Separator: '', Num additional inputs: 1
2025-07-15 18:20:09 - app.components.combine_text_component_new - INFO - [process] Text combined successfully for request_id 5c0451e2-439f-4a69-81d2-a014cbec3c54. Result length: 4
2025-07-15 18:20:09 - ToolExecutor - INFO - [execute_tool:148] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Component CombineTextComponent processed payload successfully for request_id: 5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - ToolExecutor - INFO - [execute_tool] Component CombineTextComponent processed payload successfully for request_id: 5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - ToolExecutor - INFO - [execute_tool:154] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] ToolExecutor returning raw component result for request_id: 5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: 5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - ComponentSystem - INFO - [_process_message:717] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Tool CombineTextComponent executed successfully for RequestID=5c0451e2-439f-4a69-81d2-a014cbec3c54, TaskID=ApiRequestNode-node-execution-request-0-1257-1752583809.868053
2025-07-15 18:20:09 - ComponentSystem - INFO - [_process_message] Tool CombineTextComponent executed successfully for RequestID=5c0451e2-439f-4a69-81d2-a014cbec3c54, TaskID=ApiRequestNode-node-execution-request-0-1257-1752583809.868053
2025-07-15 18:20:09 - ComponentSystem - INFO - [_send_result:1007] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Preparing to send result for component ApiRequestNode, RequestID=5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:09 - ComponentSystem - INFO - [_send_result:1105] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Sending Kafka response: RequestID=5c0451e2-439f-4a69-81d2-a014cbec3c54, Response={
  "request_id": "5c0451e2-439f-4a69-81d2-a014cbec3c54",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1752583809.873482,
  "transition_id": "transition-CombineTextComponent-*************",
  "result": "{}ma",
  "error": null
}
2025-07-15 18:20:09 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=5c0451e2-439f-4a69-81d2-a014cbec3c54, Response={
  "request_id": "5c0451e2-439f-4a69-81d2-a014cbec3c54",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": 1752583809.873482,
  "transition_id": "transition-CombineTextComponent-*************",
  "result": "{}ma",
  "error": null
}
2025-07-15 18:20:10 - ComponentSystem - INFO - [_send_result:1121] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Sent result for component ApiRequestNode to topic node_results for RequestID=5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:10 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=5c0451e2-439f-4a69-81d2-a014cbec3c54
2025-07-15 18:20:10 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Successfully committed offset 1258 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1257-1752583809.868053
2025-07-15 18:20:10 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1258 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1257-1752583809.868053
2025-07-15 18:20:10 - ComponentSystem - INFO - [_process_message:936] [ReqID:5c0451e2-439f-4a69-81d2-a014cbec3c54] [CorrID:346d41ec-8085-44b6-83d9-9078e22cc7bd] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1257, TaskID=ApiRequestNode-node-execution-request-0-1257-1752583809.868053
2025-07-15 18:20:10 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1257, TaskID=ApiRequestNode-node-execution-request-0-1257-1752583809.868053
